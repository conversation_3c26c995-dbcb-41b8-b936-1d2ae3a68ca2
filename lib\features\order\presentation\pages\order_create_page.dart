import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import 'package:soko/core/enums/app_enums.dart';
import 'package:soko/shared/presentation/widgets/custom_app_bar.dart';
import 'package:soko/shared/presentation/widgets/custom_button.dart';
import 'package:soko/shared/presentation/widgets/loading_widget.dart';
import 'package:soko/features/order/presentation/providers/order_create_provider.dart';
import 'package:soko/features/order/presentation/widgets/order_create_address_section.dart';
import 'package:soko/features/order/presentation/widgets/order_create_items_section.dart';
import 'package:soko/features/order/presentation/widgets/order_create_payment_section.dart';
import 'package:soko/features/order/presentation/widgets/order_create_delivery_section.dart';
import 'package:soko/features/order/presentation/widgets/order_create_summary_section.dart';

/// 订单创建页面
class OrderCreatePage extends ConsumerStatefulWidget {
  const OrderCreatePage({super.key});

  @override
  ConsumerState<OrderCreatePage> createState() => _OrderCreatePageState();
}

class _OrderCreatePageState extends ConsumerState<OrderCreatePage> {
  @override
  void initState() {
    super.initState();
    // 页面初始化时清除之前的状态
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(orderCreateProvider.notifier).clearCreateOrderState();
    });
  }

  @override
  Widget build(BuildContext context) {
    final orderCreateState = ref.watch(orderCreateProvider);

    return Scaffold(
      appBar: const CustomAppBar(title: '确认订单'),
      body: orderCreateState.createOrderState.when(
        idle: () => _buildOrderCreateForm(orderCreateState),
        loading: () => _buildOrderCreateForm(orderCreateState),
        success: (order) => _buildOrderCreateForm(orderCreateState),
        error: (error) => _buildOrderCreateForm(orderCreateState),
      ),
      bottomNavigationBar: _buildBottomBar(orderCreateState),
    );
  }

  /// 构建订单创建表单
  Widget _buildOrderCreateForm(OrderCreateState state) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          // 收货地址选择
          OrderCreateAddressSection(
            selectedAddress: state.selectedAddress,
            onAddressSelected: (address) {
              ref.read(orderCreateProvider.notifier).setShippingAddress(address);
            },
          ),
          SizedBox(height: 16.h),

          // 订单商品
          OrderCreateItemsSection(items: state.items),
          SizedBox(height: 16.h),

          // 配送方式
          OrderCreateDeliverySection(
            selectedMethod: state.selectedDeliveryMethod,
            onMethodSelected: (method) {
              ref.read(orderCreateProvider.notifier).setDeliveryMethod(method);
            },
          ),
          SizedBox(height: 16.h),

          // 支付方式
          OrderCreatePaymentSection(
            selectedMethod: state.selectedPaymentMethod,
            onMethodSelected: (method) {
              ref.read(orderCreateProvider.notifier).setPaymentMethod(method);
            },
          ),
          SizedBox(height: 16.h),

          // 订单摘要
          OrderCreateSummarySection(
            itemsAmount: state.itemsAmount,
            deliveryFee: state.deliveryFee,
            discountAmount: state.discountAmount,
            totalAmount: state.totalAmount,
          ),

          // 底部留白，避免被底部按钮遮挡
          SizedBox(height: 100.h),
        ],
      ),
    );
  }

  /// 构建底部操作栏
  Widget _buildBottomBar(OrderCreateState state) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // 总金额显示
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '合计',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                  Text(
                    '¥${state.totalAmount.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.red,
                    ),
                  ),
                ],
              ),
            ),

            // 提交订单按钮
            SizedBox(
              width: 120.w,
              child: CustomButton(
                text: state.createOrderState.isLoading ? '创建中...' : '提交订单',
                onPressed: state.canCreateOrder && !state.createOrderState.isLoading
                    ? _createOrder
                    : null,
                loading: state.createOrderState.isLoading,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 创建订单
  Future<void> _createOrder() async {
    final order = await ref.read(orderCreateProvider.notifier).createOrder();
    
    if (order != null && mounted) {
      // 订单创建成功，跳转到支付页面或订单详情页面
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('订单创建成功')),
      );
      
      // TODO(payment): 跳转到支付页面
      // context.push('/payment/${order.id}');
      
      // 暂时跳转到订单详情页面
      // context.pushReplacement('/order/detail/${order.id}');
      
      // 返回上一页
      context.pop();
    } else if (mounted) {
      // 显示错误信息
      final errorMessage = ref.read(orderCreateProvider).createOrderState.maybeWhen(
        error: (error) => error,
        orElse: () => '订单创建失败',
      );
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(errorMessage)),
      );
    }
  }
}
