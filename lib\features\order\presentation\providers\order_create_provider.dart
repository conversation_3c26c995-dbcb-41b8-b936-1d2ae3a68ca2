import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/enums/app_enums.dart';
import 'package:soko/core/state/base_state.dart';
import 'package:soko/features/order/domain/entities/order.dart';
import 'package:soko/features/order/domain/entities/order_create_request.dart';
import 'package:soko/features/order/domain/entities/shipping_address.dart';
import 'package:soko/features/order/domain/usecases/create_order_usecase.dart';

/// 订单创建状态
@immutable
class OrderCreateState {

  const OrderCreateState({
    this.items = const [],
    this.selectedAddress,
    this.selectedPaymentMethod,
    this.selectedDeliveryMethod = DeliveryMethod.standard,
    this.couponId,
    this.note,
    this.invoiceType,
    this.invoiceTitle,
    this.itemsAmount = 0.0,
    this.deliveryFee = 0.0,
    this.discountAmount = 0.0,
    this.totalAmount = 0.0,
    this.createOrderState = const AsyncState.idle(),
  });
  final List<OrderCreateItem> items;
  final ShippingAddress? selectedAddress;
  final PaymentMethod? selectedPaymentMethod;
  final DeliveryMethod selectedDeliveryMethod;
  final String? couponId;
  final String? note;
  final InvoiceType? invoiceType;
  final String? invoiceTitle;
  final double itemsAmount;
  final double deliveryFee;
  final double discountAmount;
  final double totalAmount;
  final AsyncState<Order> createOrderState;

  OrderCreateState copyWith({
    List<OrderCreateItem>? items,
    ShippingAddress? selectedAddress,
    PaymentMethod? selectedPaymentMethod,
    DeliveryMethod? selectedDeliveryMethod,
    String? couponId,
    String? note,
    InvoiceType? invoiceType,
    String? invoiceTitle,
    double? itemsAmount,
    double? deliveryFee,
    double? discountAmount,
    double? totalAmount,
    AsyncState<Order>? createOrderState,
  }) {
    return OrderCreateState(
      items: items ?? this.items,
      selectedAddress: selectedAddress ?? this.selectedAddress,
      selectedPaymentMethod:
          selectedPaymentMethod ?? this.selectedPaymentMethod,
      selectedDeliveryMethod:
          selectedDeliveryMethod ?? this.selectedDeliveryMethod,
      couponId: couponId ?? this.couponId,
      note: note ?? this.note,
      invoiceType: invoiceType ?? this.invoiceType,
      invoiceTitle: invoiceTitle ?? this.invoiceTitle,
      itemsAmount: itemsAmount ?? this.itemsAmount,
      deliveryFee: deliveryFee ?? this.deliveryFee,
      discountAmount: discountAmount ?? this.discountAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      createOrderState: createOrderState ?? this.createOrderState,
    );
  }

  /// 检查是否可以创建订单
  bool get canCreateOrder {
    return items.isNotEmpty &&
        selectedAddress != null &&
        selectedPaymentMethod != null;
  }
}

/// 订单创建状态管理器
class OrderCreateNotifier extends StateNotifier<OrderCreateState> {

  OrderCreateNotifier(this._createOrderUseCase)
      : super(const OrderCreateState());
  final CreateOrderUseCase _createOrderUseCase;

  /// 设置订单商品项
  void setOrderItems(List<OrderCreateItem> items) {
    state = state.copyWith(items: items);
    _calculateAmounts();
  }

  /// 设置收货地址
  void setShippingAddress(ShippingAddress address) {
    state = state.copyWith(selectedAddress: address);
  }

  /// 设置支付方式
  void setPaymentMethod(PaymentMethod paymentMethod) {
    state = state.copyWith(selectedPaymentMethod: paymentMethod);
  }

  /// 设置配送方式
  void setDeliveryMethod(DeliveryMethod deliveryMethod) {
    state = state.copyWith(
      selectedDeliveryMethod: deliveryMethod,
      deliveryFee: deliveryMethod.fee,
    );
    _calculateAmounts();
  }

  /// 设置优惠券
  void setCoupon(String? couponId, double discountAmount) {
    state = state.copyWith(
      couponId: couponId,
      discountAmount: discountAmount,
    );
    _calculateAmounts();
  }

  /// 设置订单备注
  void setNote(String? note) {
    state = state.copyWith(note: note);
  }

  /// 设置发票信息
  void setInvoiceInfo(InvoiceType? invoiceType, String? invoiceTitle) {
    state = state.copyWith(
      invoiceType: invoiceType,
      invoiceTitle: invoiceTitle,
    );
  }

  /// 计算金额
  void _calculateAmounts() {
    final itemsAmount = state.items.fold<double>(
      0,
      (sum, item) => sum + (item.price * item.quantity),
    );

    final totalAmount = itemsAmount + state.deliveryFee - state.discountAmount;

    state = state.copyWith(
      itemsAmount: itemsAmount,
      totalAmount: totalAmount > 0 ? totalAmount : 0,
    );
  }

  /// 创建订单
  Future<Order?> createOrder() async {
    if (!state.canCreateOrder) {
      state = state.copyWith(
        createOrderState: const AsyncState.error('订单信息不完整'),
      );
      return null;
    }

    state = state.copyWith(createOrderState: const AsyncState.loading());

    try {
      final request = OrderCreateRequest(
        items: state.items,
        addressId: state.selectedAddress!.id,
        paymentMethod: state.selectedPaymentMethod!,
        deliveryMethod: state.selectedDeliveryMethod,
        couponId: state.couponId,
        note: state.note,
        invoiceType: state.invoiceType,
        invoiceTitle: state.invoiceTitle,
      );

      final result = await _createOrderUseCase(request);

      return result.fold(
        (failure) {
          state = state.copyWith(
            createOrderState: AsyncState.error(failure.message),
          );
          return null;
        },
        (order) {
          state = state.copyWith(
            createOrderState: AsyncState.success(order),
          );
          return order;
        },
      );
    } catch (e) {
      state = state.copyWith(
        createOrderState: AsyncState.error(e.toString()),
      );
      return null;
    }
  }

  /// 重置状态
  void reset() {
    state = const OrderCreateState();
  }

  /// 清除创建订单状态
  void clearCreateOrderState() {
    state = state.copyWith(createOrderState: const AsyncState.idle());
  }
}

/// 订单创建状态管理器提供者
final orderCreateProvider =
    StateNotifierProvider<OrderCreateNotifier, OrderCreateState>((ref) {
  final createOrderUseCase = ref.read(createOrderUseCaseProvider);
  return OrderCreateNotifier(createOrderUseCase);
});
