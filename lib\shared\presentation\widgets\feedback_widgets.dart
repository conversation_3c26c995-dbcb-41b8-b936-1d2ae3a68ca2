import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 评分组件
class RatingWidget extends StatefulWidget {
  const RatingWidget({
    super.key,
    this.rating = 0.0,
    this.maxRating = 5,
    this.size = 24.0,
    this.color,
    this.unratedColor,
    this.allowHalfRating = true,
    this.onRatingChanged,
    this.readOnly = false,
  });

  final double rating;
  final int maxRating;
  final double size;
  final Color? color;
  final Color? unratedColor;
  final bool allowHalfRating;
  final ValueChanged<double>? onRatingChanged;
  final bool readOnly;

  @override
  State<RatingWidget> createState() => _RatingWidgetState();
}

class _RatingWidgetState extends State<RatingWidget> {
  late double _currentRating;

  @override
  void initState() {
    super.initState();
    _currentRating = widget.rating;
  }

  @override
  Widget build(BuildContext context) {
    final color = widget.color ?? Colors.amber;
    final unratedColor = widget.unratedColor ?? Colors.grey[300]!;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(widget.maxRating, (index) {
        return GestureDetector(
          onTap: widget.readOnly ? null : () => _handleTap(index),
          child: Icon(
            _getStarIcon(index),
            size: widget.size,
            color: _getStarColor(index, color, unratedColor),
          ),
        );
      }),
    );
  }

  /// 处理点击
  void _handleTap(int index) {
    final newRating = (index + 1).toDouble();
    setState(() {
      _currentRating = newRating;
    });
    widget.onRatingChanged?.call(newRating);
  }

  /// 获取星星图标
  IconData _getStarIcon(int index) {
    final starValue = index + 1;
    if (_currentRating >= starValue) {
      return Icons.star;
    } else if (widget.allowHalfRating && _currentRating >= starValue - 0.5) {
      return Icons.star_half;
    } else {
      return Icons.star_border;
    }
  }

  /// 获取星星颜色
  Color _getStarColor(int index, Color ratedColor, Color unratedColor) {
    final starValue = index + 1;
    if (_currentRating >= starValue) {
      return ratedColor;
    } else if (widget.allowHalfRating && _currentRating >= starValue - 0.5) {
      return ratedColor;
    } else {
      return unratedColor;
    }
  }
}

/// 反馈对话框
class FeedbackDialog extends StatefulWidget {
  const FeedbackDialog({
    super.key,
    this.title = '意见反馈',
    this.hintText = '请描述您遇到的问题或建议',
    this.showRating = true,
    this.onSubmit,
  });

  final String title;
  final String hintText;
  final bool showRating;
  final Function(String feedback, double? rating)? onSubmit;

  @override
  State<FeedbackDialog> createState() => _FeedbackDialogState();
}

class _FeedbackDialogState extends State<FeedbackDialog> {
  final TextEditingController _controller = TextEditingController();
  double _rating = 5.0;

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.title),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 评分
          if (widget.showRating) ...[
            Text(
              '您的评分',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 8.h),
            RatingWidget(
              rating: _rating,
              size: 32.w,
              onRatingChanged: (rating) {
                setState(() {
                  _rating = rating;
                });
              },
            ),
            SizedBox(height: 16.h),
          ],
          
          // 反馈内容
          Text(
            '详细描述',
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8.h),
          TextField(
            controller: _controller,
            maxLines: 4,
            decoration: InputDecoration(
              hintText: widget.hintText,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
              contentPadding: EdgeInsets.all(12.w),
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: _submitFeedback,
          child: const Text('提交'),
        ),
      ],
    );
  }

  /// 提交反馈
  void _submitFeedback() {
    final feedback = _controller.text.trim();
    if (feedback.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入反馈内容')),
      );
      return;
    }

    widget.onSubmit?.call(feedback, widget.showRating ? _rating : null);
    Navigator.of(context).pop();
  }
}

/// 快速反馈组件
class QuickFeedbackWidget extends StatelessWidget {
  const QuickFeedbackWidget({
    super.key,
    this.onFeedback,
  });

  final Function(FeedbackType type)? onFeedback;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '这个页面对您有帮助吗？',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 12.h),
          Row(
            children: [
              Expanded(
                child: _buildFeedbackButton(
                  icon: Icons.thumb_up,
                  label: '有帮助',
                  color: Colors.green,
                  onTap: () => onFeedback?.call(FeedbackType.positive),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildFeedbackButton(
                  icon: Icons.thumb_down,
                  label: '没帮助',
                  color: Colors.red,
                  onTap: () => onFeedback?.call(FeedbackType.negative),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建反馈按钮
  Widget _buildFeedbackButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12.h),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 18.w,
              color: color,
            ),
            SizedBox(width: 8.w),
            Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 满意度调查组件
class SatisfactionSurveyWidget extends StatefulWidget {
  const SatisfactionSurveyWidget({
    super.key,
    this.title = '满意度调查',
    this.questions = const [],
    this.onSubmit,
  });

  final String title;
  final List<SurveyQuestion> questions;
  final Function(Map<String, dynamic> answers)? onSubmit;

  @override
  State<SatisfactionSurveyWidget> createState() => _SatisfactionSurveyWidgetState();
}

class _SatisfactionSurveyWidgetState extends State<SatisfactionSurveyWidget> {
  final Map<String, dynamic> _answers = {};

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.title,
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 16.h),
          
          // 问题列表
          ...widget.questions.map((question) => _buildQuestion(question)),
          
          SizedBox(height: 16.h),
          
          // 提交按钮
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _submitSurvey,
              child: const Text('提交'),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建问题
  Widget _buildQuestion(SurveyQuestion question) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            question.question,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8.h),
          
          if (question.type == QuestionType.rating)
            RatingWidget(
              onRatingChanged: (rating) {
                _answers[question.id] = rating;
              },
            )
          else if (question.type == QuestionType.multipleChoice)
            ...question.options!.map((option) => RadioListTile<String>(
              title: Text(option),
              value: option,
              groupValue: _answers[question.id],
              onChanged: (value) {
                setState(() {
                  _answers[question.id] = value;
                });
              },
            ))
          else if (question.type == QuestionType.text)
            TextField(
              onChanged: (value) {
                _answers[question.id] = value;
              },
              decoration: InputDecoration(
                hintText: '请输入您的答案',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 提交调查
  void _submitSurvey() {
    widget.onSubmit?.call(_answers);
  }
}

/// 反馈类型枚举
enum FeedbackType {
  positive,
  negative,
  suggestion,
}

/// 问题类型枚举
enum QuestionType {
  rating,
  multipleChoice,
  text,
}

/// 调查问题模型
class SurveyQuestion {
  const SurveyQuestion({
    required this.id,
    required this.question,
    required this.type,
    this.options,
    this.required = false,
  });

  final String id;
  final String question;
  final QuestionType type;
  final List<String>? options;
  final bool required;
}
