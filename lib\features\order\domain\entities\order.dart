import 'package:json_annotation/json_annotation.dart';

import 'package:soko/core/enums/app_enums.dart';

part 'order.g.dart';

/// 订单实体类
@JsonSerializable()
class Order {

  const Order({
    required this.id,
    required this.orderNo,
    required this.userId,
    required this.totalAmount,
    required this.payAmount,
    this.domesticFreight,
    this.internationalFreight,
    this.discountAmount,
    this.payType,
    required this.orderStatus,
    required this.receiverName,
    required this.receiverPhone,
    required this.receiverProvince,
    required this.receiverCity,
    required this.receiverDistrict,
    required this.receiverAddress,
    this.note,
    this.trackingNo,
    this.trackingCompany,
    this.salesType,
    required this.orderTime,
    this.payTime,
    this.shipTime,
    this.receiveTime,
    required this.createTime,
    required this.updateTime,
    this.orderItems,
    this.orderRecords,
  });

  factory Order.fromJson(Map<String, dynamic> json) => _$OrderFromJson(json);
  @<PERSON><PERSON><PERSON><PERSON>(name: 'id')
  final String id;

  @Json<PERSON>ey(name: 'orderNo')
  final String orderNo;

  @JsonKey(name: 'userId')
  final String userId;

  @JsonKey(name: 'totalAmount')
  final double totalAmount;

  @Json<PERSON>ey(name: 'payAmount')
  final double payAmount;

  @JsonKey(name: 'domesticFreight')
  final double? domesticFreight;

  @JsonKey(name: 'internationalFreight')
  final double? internationalFreight;

  @JsonKey(name: 'discountAmount')
  final double? discountAmount;

  @JsonKey(name: 'payType')
  final String? payType;

  @JsonKey(name: 'orderStatus')
  final String orderStatus;

  @JsonKey(name: 'receiverName')
  final String receiverName;

  @JsonKey(name: 'receiverPhone')
  final String receiverPhone;

  @JsonKey(name: 'receiverProvince')
  final String receiverProvince;

  @JsonKey(name: 'receiverCity')
  final String receiverCity;

  @JsonKey(name: 'receiverDistrict')
  final String receiverDistrict;

  @JsonKey(name: 'receiverAddress')
  final String receiverAddress;

  @JsonKey(name: 'note')
  final String? note;

  @JsonKey(name: 'trackingNo')
  final String? trackingNo;

  @JsonKey(name: 'trackingCompany')
  final String? trackingCompany;

  @JsonKey(name: 'salesType')
  final String? salesType;

  @JsonKey(name: 'orderTime')
  final int orderTime;

  @JsonKey(name: 'payTime')
  final int? payTime;

  @JsonKey(name: 'shipTime')
  final int? shipTime;

  @JsonKey(name: 'receiveTime')
  final int? receiveTime;

  @JsonKey(name: 'createTime')
  final int createTime;

  @JsonKey(name: 'updateTime')
  final int updateTime;

  @JsonKey(name: 'orderItems')
  final List<OrderItem>? orderItems;

  @JsonKey(name: 'orderRecords')
  final List<OrderRecord>? orderRecords;

  Map<String, dynamic> toJson() => _$OrderToJson(this);

  /// 获取完整收货地址
  String get fullAddress =>
      '$receiverProvince$receiverCity$receiverDistrict$receiverAddress';

  /// 获取订单状态枚举
  OrderStatus get statusEnum {
    switch (orderStatus.toUpperCase()) {
      case 'UNPAID':
      case 'PENDING':
        return OrderStatus.pending;
      case 'PAID':
        return OrderStatus.paid;
      case 'SHIPPED':
        return OrderStatus.shipped;
      case 'DELIVERED':
        return OrderStatus.delivered;
      case 'COMPLETED':
        return OrderStatus.completed;
      case 'CANCELLED':
        return OrderStatus.cancelled;
      case 'REFUNDED':
        return OrderStatus.refunded;
      default:
        return OrderStatus.pending;
    }
  }

  /// 获取支付方式枚举
  PaymentMethod? get paymentMethodEnum {
    if (payType == null) return null;
    switch (payType!) {
      case 'alipay':
        return PaymentMethod.alipay;
      case 'wechat':
        return PaymentMethod.wechat;
      case 'unionpay':
        return PaymentMethod.unionpay;
      case 'balance':
        return PaymentMethod.balance;
      default:
        return null;
    }
  }

  /// 获取运费总额
  double get totalFreight {
    return (domesticFreight ?? 0) + (internationalFreight ?? 0);
  }

  /// 获取商品总额（不含运费）
  double get itemsAmount {
    return totalAmount - totalFreight;
  }

  /// 获取订单项列表
  List<OrderItem> get items {
    return orderItems ?? [];
  }

  /// 获取创建时间（DateTime 格式）
  DateTime get createdAt {
    return DateTime.fromMillisecondsSinceEpoch(createTime);
  }

  /// 是否可以取消
  bool get canCancel {
    return statusEnum == OrderStatus.pending;
  }

  /// 是否可以支付
  bool get canPay {
    return statusEnum == OrderStatus.pending;
  }

  /// 是否可以确认收货
  bool get canConfirmReceive {
    return statusEnum == OrderStatus.shipped;
  }

  /// 是否可以申请退款
  bool get canRefund {
    return statusEnum == OrderStatus.paid ||
        statusEnum == OrderStatus.shipped ||
        statusEnum == OrderStatus.delivered;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Order && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Order(id: $id, orderNo: $orderNo, status: $orderStatus, totalAmount: $totalAmount)';
  }
}

/// 订单项
@JsonSerializable()
class OrderItem {

  const OrderItem({
    required this.id,
    required this.orderId,
    required this.productId,
    required this.productName,
    this.productImage,
    this.skuId,
    this.skuName,
    required this.price,
    required this.quantity,
    required this.totalAmount,
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) =>
      _$OrderItemFromJson(json);
  @JsonKey(name: 'id')
  final String id;

  @JsonKey(name: 'orderId')
  final String orderId;

  @JsonKey(name: 'productId')
  final String productId;

  @JsonKey(name: 'productName')
  final String productName;

  @JsonKey(name: 'productImage')
  final String? productImage;

  @JsonKey(name: 'skuId')
  final String? skuId;

  @JsonKey(name: 'skuName')
  final String? skuName;

  @JsonKey(name: 'price')
  final double price;

  @JsonKey(name: 'quantity')
  final int quantity;

  @JsonKey(name: 'totalAmount')
  final double totalAmount;

  Map<String, dynamic> toJson() => _$OrderItemToJson(this);
}

/// 订单记录
@JsonSerializable()
class OrderRecord {

  const OrderRecord({
    required this.id,
    required this.orderId,
    required this.status,
    required this.description,
    required this.operateTime,
    this.operatorId,
    this.operatorName,
  });

  factory OrderRecord.fromJson(Map<String, dynamic> json) =>
      _$OrderRecordFromJson(json);
  @JsonKey(name: 'id')
  final String id;

  @JsonKey(name: 'orderId')
  final String orderId;

  @JsonKey(name: 'status')
  final String status;

  @JsonKey(name: 'description')
  final String description;

  @JsonKey(name: 'operateTime')
  final int operateTime;

  @JsonKey(name: 'operatorId')
  final String? operatorId;

  @JsonKey(name: 'operatorName')
  final String? operatorName;

  Map<String, dynamic> toJson() => _$OrderRecordToJson(this);
}
