import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import 'package:soko/core/theme/app_colors.dart';
import 'package:soko/core/theme/app_text_styles.dart';
import 'package:soko/shared/presentation/widgets/custom_app_bar.dart';
import 'package:soko/shared/presentation/widgets/loading_widget.dart';
import 'package:soko/shared/presentation/widgets/error_widget.dart';
import 'package:soko/shared/presentation/widgets/empty_widget.dart';
import 'package:soko/features/product/presentation/providers/product_favorite_provider.dart';
import 'package:soko/features/product/presentation/widgets/product_card.dart';

/// 商品收藏页面
class ProductFavoritePage extends ConsumerStatefulWidget {
  const ProductFavoritePage({super.key});

  @override
  ConsumerState<ProductFavoritePage> createState() => _ProductFavoritePageState();
}

class _ProductFavoritePageState extends ConsumerState<ProductFavoritePage> {
  final ScrollController _scrollController = ScrollController();
  bool _isEditMode = false;
  final Set<String> _selectedIds = {};

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    
    // 加载收藏列表
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(favoriteListProvider.notifier).loadFirstPage();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      ref.read(favoriteListProvider.notifier).loadMore();
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(favoriteListProvider);
    final favoriteState = ref.watch(favoriteProvider);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CustomAppBar(
        title: '我的收藏',
        actions: [
          if (state.items.isNotEmpty)
            TextButton(
              onPressed: () {
                setState(() {
                  _isEditMode = !_isEditMode;
                  _selectedIds.clear();
                });
              },
              child: Text(
                _isEditMode ? '完成' : '编辑',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body: _buildBody(state, favoriteState),
      bottomNavigationBar: _isEditMode && _selectedIds.isNotEmpty 
          ? _buildBottomActionBar() 
          : null,
    );
  }

  /// 构建主体内容
  Widget _buildBody(state, favoriteState) {
    if (state.isLoading && state.items.isEmpty) {
      return const LoadingWidget();
    }

    if (state.error != null && state.items.isEmpty) {
      return ErrorDisplayWidget(
        message: state.error!,
        onRetry: () => ref.read(favoriteListProvider.notifier).refresh(),
      );
    }

    if (state.items.isEmpty) {
      return const EmptyWidget(
        message: '暂无收藏商品',
        description: '快去收藏你喜欢的商品吧',
        icon: Icons.favorite_border,
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(favoriteListProvider.notifier).refresh();
        await ref.read(favoriteProvider.notifier).refresh();
      },
      child: ListView.builder(
        controller: _scrollController,
        padding: EdgeInsets.symmetric(vertical: 8.h),
        itemCount: state.items.length + (state.hasMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= state.items.length) {
            return _buildLoadMoreIndicator(state);
          }

          final product = state.items[index];
          final isSelected = _selectedIds.contains(product.id);
          final isFavorite = ref.watch(isProductFavoriteProvider(product.id));

          return _buildProductItem(product, isSelected, isFavorite);
        },
      ),
    );
  }

  /// 构建商品项
  Widget _buildProductItem(product, bool isSelected, bool isFavorite) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
      child: Row(
        children: [
          // 编辑模式选择框
          if (_isEditMode) ...[
            Checkbox(
              value: isSelected,
              onChanged: (value) {
                setState(() {
                  if (value == true) {
                    _selectedIds.add(product.id);
                  } else {
                    _selectedIds.remove(product.id);
                  }
                });
              },
              activeColor: AppColors.primary,
            ),
            SizedBox(width: 8.w),
          ],
          // 商品卡片
          Expanded(
            child: ProductCard(
              product: product,
              isFavorite: isFavorite,
              showFavoriteButton: !_isEditMode,
              onTap: _isEditMode 
                  ? () {
                      setState(() {
                        if (isSelected) {
                          _selectedIds.remove(product.id);
                        } else {
                          _selectedIds.add(product.id);
                        }
                      });
                    }
                  : () => _navigateToProductDetail(product.id),
              onFavorite: () => _toggleFavorite(product.id),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建加载更多指示器
  Widget _buildLoadMoreIndicator(state) {
    if (state.isLoading) {
      return Container(
        padding: EdgeInsets.all(16.w),
        alignment: Alignment.center,
        child: const LoadingWidget(),
      );
    }

    if (state.error != null) {
      return Container(
        padding: EdgeInsets.all(16.w),
        alignment: Alignment.center,
        child: Column(
          children: [
            const Text(
              '加载失败',
              style: TextStyle(color: AppColors.textSecondary),
            ),
            SizedBox(height: 8.h),
            TextButton(
              onPressed: () => ref.read(favoriteListProvider.notifier).loadMore(),
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    return const SizedBox.shrink();
  }

  /// 构建底部操作栏
  Widget _buildBottomActionBar() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 8,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // 全选按钮
            TextButton(
              onPressed: _toggleSelectAll,
              child: Text(
                _isAllSelected ? '取消全选' : '全选',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ),
            const Spacer(),
            // 删除按钮
            ElevatedButton(
              onPressed: _selectedIds.isNotEmpty ? _removeSelectedItems : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.error,
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
              ),
              child: Text(
                '删除 (${_selectedIds.length})',
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 是否全选
  bool get _isAllSelected {
    final state = ref.read(favoriteListProvider);
    return _selectedIds.length == state.items.length && state.items.isNotEmpty;
  }

  /// 切换全选状态
  void _toggleSelectAll() {
    final state = ref.read(favoriteListProvider);
    setState(() {
      if (_isAllSelected) {
        _selectedIds.clear();
      } else {
        _selectedIds.addAll(state.items.map((item) => item.id));
      }
    });
  }

  /// 删除选中的商品
  void _removeSelectedItems() {
    if (_selectedIds.isEmpty) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除选中的 ${_selectedIds.length} 个商品吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _performRemove();
            },
            child: const Text(
              '删除',
              style: TextStyle(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }

  /// 执行删除操作
  void _performRemove() {
    final selectedIds = List<String>.from(_selectedIds);
    
    // 从收藏状态中移除
    ref.read(favoriteProvider.notifier).removeFavorites(selectedIds);
    
    // 从列表中移除
    for (final id in selectedIds) {
      ref.read(favoriteListProvider.notifier).removeFromList(id);
    }
    
    setState(() {
      _selectedIds.clear();
      _isEditMode = false;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('已删除 ${selectedIds.length} 个商品')),
    );
  }

  /// 切换收藏状态
  void _toggleFavorite(String productId) {
    ref.read(favoriteProvider.notifier).toggleFavorite(productId);
  }

  /// 导航到商品详情页
  void _navigateToProductDetail(String productId) {
    context.push('/product/$productId');
  }
}
