import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/features/recycle/domain/entities/recycle_models.dart';
import 'package:soko/features/recycle/presentation/widgets/brand_selector.dart';
import 'package:soko/features/recycle/presentation/widgets/model_selector.dart';
import 'package:soko/features/recycle/presentation/widgets/condition_selector.dart';
import 'package:soko/features/recycle/presentation/widgets/accessories_selector.dart';
import 'package:soko/features/recycle/presentation/widgets/defects_selector.dart';
import 'package:soko/shared/presentation/widgets/custom_text_field.dart';
import 'package:soko/shared/presentation/widgets/date_picker_field.dart';

/// 价格评估表单组件
class PriceEvaluationForm extends ConsumerStatefulWidget {
  const PriceEvaluationForm({
    super.key,
    required this.onSubmit,
  });

  final Function(PriceEvaluationRequest) onSubmit;

  @override
  ConsumerState<PriceEvaluationForm> createState() => _PriceEvaluationFormState();
}

class _PriceEvaluationFormState extends ConsumerState<PriceEvaluationForm> {
  final _formKey = GlobalKey<FormState>();
  
  // 表单字段
  String? _selectedBrandId;
  String? _selectedModelId;
  String? _selectedCategoryId;
  String? _selectedConditionId;
  DateTime? _purchaseDate;
  double? _originalPrice;
  List<String> _selectedAccessories = [];
  List<String> _selectedDefects = [];
  List<String> _uploadedImages = [];
  String? _description;

  // 控制器
  final _originalPriceController = TextEditingController();
  final _descriptionController = TextEditingController();

  @override
  void dispose() {
    _originalPriceController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 基本信息部分
          _buildSectionTitle('基本信息'),
          SizedBox(height: 12.h),
          
          // 品牌选择
          BrandSelector(
            selectedBrandId: _selectedBrandId,
            onBrandSelected: (brandId, categoryId) {
              setState(() {
                _selectedBrandId = brandId;
                _selectedCategoryId = categoryId;
                _selectedModelId = null; // 重置型号选择
              });
            },
          ),
          SizedBox(height: 16.h),
          
          // 型号选择
          if (_selectedBrandId != null)
            ModelSelector(
              brandId: _selectedBrandId!,
              selectedModelId: _selectedModelId,
              onModelSelected: (modelId) {
                setState(() {
                  _selectedModelId = modelId;
                });
              },
            ),
          if (_selectedBrandId != null) SizedBox(height: 16.h),
          
          // 设备状况部分
          _buildSectionTitle('设备状况'),
          SizedBox(height: 12.h),
          
          // 状况选择
          ConditionSelector(
            selectedConditionId: _selectedConditionId,
            onConditionSelected: (conditionId) {
              setState(() {
                _selectedConditionId = conditionId;
              });
            },
          ),
          SizedBox(height: 16.h),
          
          // 购买信息部分
          _buildSectionTitle('购买信息'),
          SizedBox(height: 12.h),
          
          // 购买日期
          DatePickerField(
            label: '购买日期',
            selectedDate: _purchaseDate,
            onDateSelected: (date) {
              setState(() {
                _purchaseDate = date;
              });
            },
            hintText: '选择购买日期（可选）',
          ),
          SizedBox(height: 16.h),
          
          // 原价
          CustomTextField(
            controller: _originalPriceController,
            label: '购买原价',
            hintText: '请输入购买时的价格（可选）',
            keyboardType: TextInputType.number,
            prefixText: '¥ ',
            onChanged: (value) {
              _originalPrice = double.tryParse(value);
            },
          ),
          SizedBox(height: 16.h),
          
          // 配件信息部分
          _buildSectionTitle('配件信息'),
          SizedBox(height: 12.h),
          
          // 配件选择
          AccessoriesSelector(
            selectedAccessories: _selectedAccessories,
            onAccessoriesChanged: (accessories) {
              setState(() {
                _selectedAccessories = accessories;
              });
            },
          ),
          SizedBox(height: 16.h),
          
          // 缺陷信息部分
          _buildSectionTitle('缺陷信息'),
          SizedBox(height: 12.h),
          
          // 缺陷选择
          DefectsSelector(
            selectedDefects: _selectedDefects,
            onDefectsChanged: (defects) {
              setState(() {
                _selectedDefects = defects;
              });
            },
          ),
          SizedBox(height: 16.h),
          
          // 补充说明
          _buildSectionTitle('补充说明'),
          SizedBox(height: 12.h),
          
          CustomTextField(
            controller: _descriptionController,
            label: '详细描述',
            hintText: '请详细描述设备的使用情况、外观状态等（可选）',
            maxLines: 4,
            onChanged: (value) {
              _description = value.isEmpty ? null : value;
            },
          ),
          SizedBox(height: 24.h),
          
          // 提交按钮
          SizedBox(
            width: double.infinity,
            height: 48.h,
            child: ElevatedButton(
              onPressed: _canSubmit() ? _submitForm : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
              child: const Text(
                '开始评估',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建章节标题
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.w600,
        color: Colors.grey[800],
      ),
    );
  }

  /// 检查是否可以提交
  bool _canSubmit() {
    return _selectedBrandId != null &&
           _selectedModelId != null &&
           _selectedCategoryId != null &&
           _selectedConditionId != null;
  }

  /// 提交表单
  void _submitForm() {
    if (!_formKey.currentState!.validate() || !_canSubmit()) {
      return;
    }

    final request = PriceEvaluationRequest(
      brandId: _selectedBrandId!,
      modelId: _selectedModelId!,
      categoryId: _selectedCategoryId!,
      conditionId: _selectedConditionId!,
      purchaseDate: _purchaseDate?.millisecondsSinceEpoch,
      originalPrice: _originalPrice,
      accessories: _selectedAccessories.isEmpty ? null : _selectedAccessories,
      defects: _selectedDefects.isEmpty ? null : _selectedDefects,
      images: _uploadedImages.isEmpty ? null : _uploadedImages,
      description: _description,
    );

    widget.onSubmit(request);
  }
}
