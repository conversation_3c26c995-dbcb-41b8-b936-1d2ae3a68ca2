import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:io';

import 'package:soko/shared/presentation/widgets/custom_card.dart';
import 'package:soko/features/recycle/domain/entities/recycle_models.dart';

/// 回收订单创建 - 订单摘要步骤
class RecycleCreateSummaryStep extends ConsumerWidget {

  const RecycleCreateSummaryStep({
    super.key,
    required this.formData,
  });
  final RecycleOrderFormData formData;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 步骤标题
          Text(
            '确认订单信息',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '请仔细核对以下信息，确认无误后提交订单',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 24.h),

          // 订单摘要
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // 商品信息
                  _buildProductInfoCard(),
                  SizedBox(height: 16.h),

                  // 商品图片
                  if (formData.imageFiles.isNotEmpty) ...[
                    _buildImagesCard(),
                    SizedBox(height: 16.h),
                  ],

                  // 联系信息
                  _buildContactInfoCard(),
                  SizedBox(height: 16.h),

                  // 服务说明
                  _buildServiceInfoCard(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建商品信息卡片
  Widget _buildProductInfoCard() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.inventory_2_outlined,
                color: Colors.blue,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                '商品信息',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // 商品名称
          _buildInfoRow('商品名称', formData.customProductName ?? '未填写'),
          SizedBox(height: 12.h),

          // 商品描述
          if (formData.productDescription?.isNotEmpty == true) ...[
            _buildInfoRow('商品描述', formData.productDescription!),
            SizedBox(height: 12.h),
          ],

          // 图片数量
          _buildInfoRow('商品图片', '${formData.imageFiles.length}张'),
        ],
      ),
    );
  }

  /// 构建图片卡片
  Widget _buildImagesCard() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.photo_library_outlined,
                color: Colors.orange,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                '商品图片',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // 图片网格
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              crossAxisSpacing: 8.w,
              mainAxisSpacing: 8.h,
            ),
            itemCount: formData.imageFiles.length,
            itemBuilder: (context, index) {
              return ClipRRect(
                borderRadius: BorderRadius.circular(6.r),
                child: Image.file(
                  File(formData.imageFiles[index]),
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: Colors.grey[200],
                      child: Icon(
                        Icons.broken_image,
                        color: Colors.grey[400],
                        size: 20.w,
                      ),
                    );
                  },
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  /// 构建联系信息卡片
  Widget _buildContactInfoCard() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.contact_phone_outlined,
                color: Colors.green,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                '联系信息',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // 联系人
          _buildInfoRow('联系人', formData.contactName ?? '未填写'),
          SizedBox(height: 12.h),

          // 联系电话
          _buildInfoRow('联系电话', formData.contactPhone ?? '未填写'),
          SizedBox(height: 12.h),

          // 取货地址
          _buildInfoRow('取货地址', formData.pickupAddress ?? '未填写'),
        ],
      ),
    );
  }

  /// 构建服务说明卡片
  Widget _buildServiceInfoCard() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Colors.purple,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                '服务说明',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          Text(
            '• 提交订单后，我们会在24小时内联系您确认回收详情\n'
            '• 专业评估师会根据商品实际情况进行估价\n'
            '• 我们提供免费上门回收服务\n'
            '• 回收完成后，款项将在3-5个工作日内到账\n'
            '• 如有疑问，请随时联系客服',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80.w,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.black87,
            ),
          ),
        ),
      ],
    );
  }
}
