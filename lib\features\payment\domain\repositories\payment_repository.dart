import 'package:dartz/dartz.dart' hide Order;

import 'package:soko/core/error/failures.dart';
import 'package:soko/features/payment/domain/entities/payment_request.dart';

/// 支付仓库接口
abstract class PaymentRepository {
  /// 创建支付订单
  Future<Either<Failure, PaymentResponse>> createPayment(PaymentRequest request);

  /// 查询支付状态
  Future<Either<Failure, PaymentResponse>> queryPaymentStatus(String paymentOrderNo);

  /// 取消支付
  Future<Either<Failure, bool>> cancelPayment(String paymentOrderNo);

  /// 验证支付回调
  Future<Either<Failure, bool>> verifyPaymentCallback(Map<String, dynamic> callbackData);
}
