import 'package:dartz/dartz.dart';
import '../error/failures.dart';

/// 基础用例接口
abstract class UseCase<Type, Params> {
  Future<Either<Failure, Type>> call(Params params);
}

/// 无参数用例接口
abstract class NoParamsUseCase<Type> {
  Future<Either<Failure, Type>> call();
}

/// 无返回值用例接口
abstract class VoidUseCase<Params> {
  Future<Either<Failure, void>> call(Params params);
}

/// 无参数无返回值用例接口
abstract class NoParamsVoidUseCase {
  Future<Either<Failure, void>> call();
}
