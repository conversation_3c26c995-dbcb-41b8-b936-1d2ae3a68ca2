import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/core/enums/app_enums.dart';
import 'package:soko/core/utils/date_utils.dart' as app_date_utils;
import 'package:soko/shared/presentation/widgets/custom_card.dart';
import 'package:soko/features/order/domain/entities/order.dart';

/// 订单基本信息卡片
class OrderDetailInfoCard extends StatelessWidget {

  const OrderDetailInfoCard({
    super.key,
    required this.order,
  });
  final Order order;

  @override
  Widget build(BuildContext context) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 订单状态
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '订单状态',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: _getStatusColor().withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: Text(
                  _getStatusText(),
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: _getStatusColor(),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // 订单号
          _buildInfoRow('订单号', order.orderNo),
          SizedBox(height: 12.h),

          // 下单时间
          _buildInfoRow(
            '下单时间',
            app_date_utils.DateUtils.formatDateTime(order.createdAt),
          ),
          SizedBox(height: 12.h),

          // 支付时间（如果已支付）
          if (order.payTime != null) ...[
            _buildInfoRow(
              '支付时间',
              app_date_utils.DateUtils.formatDateTime(
                DateTime.fromMillisecondsSinceEpoch(order.payTime!),
              ),
            ),
            SizedBox(height: 12.h),
          ],

          // 发货时间（如果已发货）
          if (order.shipTime != null) ...[
            _buildInfoRow(
              '发货时间',
              app_date_utils.DateUtils.formatDateTime(
                DateTime.fromMillisecondsSinceEpoch(order.shipTime!),
              ),
            ),
            SizedBox(height: 12.h),
          ],

          // 收货时间（如果已收货）
          if (order.receiveTime != null) ...[
            _buildInfoRow(
              '收货时间',
              app_date_utils.DateUtils.formatDateTime(
                DateTime.fromMillisecondsSinceEpoch(order.receiveTime!),
              ),
            ),
            SizedBox(height: 12.h),
          ],

          // 支付方式（如果已支付）
          if (order.payType != null) ...[
            _buildInfoRow('支付方式', _getPaymentMethodText()),
            SizedBox(height: 12.h),
          ],

          // 订单备注（如果有）
          if (order.note?.isNotEmpty == true) ...[
            _buildInfoRow('订单备注', order.note!),
            SizedBox(height: 12.h),
          ],

          // 分割线
          Divider(height: 24.h, color: Colors.grey[300]),

          // 金额信息
          _buildAmountSection(),
        ],
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80.w,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.black87,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建金额信息部分
  Widget _buildAmountSection() {
    return Column(
      children: [
        // 商品总额
        _buildAmountRow('商品总额', '¥${order.itemsAmount.toStringAsFixed(2)}'),
        SizedBox(height: 8.h),

        // 运费
        if (order.totalFreight > 0) ...[
          _buildAmountRow('运费', '¥${order.totalFreight.toStringAsFixed(2)}'),
          SizedBox(height: 8.h),
        ],

        // 优惠金额
        if (order.discountAmount != null && order.discountAmount! > 0) ...[
          _buildAmountRow(
            '优惠金额',
            '-¥${order.discountAmount!.toStringAsFixed(2)}',
            valueColor: Colors.red,
          ),
          SizedBox(height: 8.h),
        ],

        // 实付金额
        _buildAmountRow(
          '实付金额',
          '¥${order.payAmount.toStringAsFixed(2)}',
          isTotal: true,
        ),
      ],
    );
  }

  /// 构建金额行
  Widget _buildAmountRow(
    String label,
    String value, {
    Color? valueColor,
    bool isTotal = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isTotal ? 16.sp : 14.sp,
            fontWeight: isTotal ? FontWeight.w600 : FontWeight.normal,
            color: Colors.grey[600],
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: isTotal ? 18.sp : 14.sp,
            fontWeight: isTotal ? FontWeight.w600 : FontWeight.normal,
            color: valueColor ?? (isTotal ? Colors.red : Colors.black87),
          ),
        ),
      ],
    );
  }

  /// 获取状态文本
  String _getStatusText() {
    switch (order.statusEnum) {
      case OrderStatus.pending:
        return '待付款';
      case OrderStatus.paid:
        return '已付款';
      case OrderStatus.shipped:
        return '已发货';
      case OrderStatus.delivered:
        return '已送达';
      case OrderStatus.completed:
        return '已完成';
      case OrderStatus.cancelled:
        return '已取消';
      case OrderStatus.refunded:
        return '已退款';
    }
  }

  /// 获取状态颜色
  Color _getStatusColor() {
    switch (order.statusEnum) {
      case OrderStatus.pending:
        return Colors.orange;
      case OrderStatus.paid:
        return Colors.blue;
      case OrderStatus.shipped:
        return Colors.green;
      case OrderStatus.delivered:
        return Colors.green;
      case OrderStatus.completed:
        return Colors.green;
      case OrderStatus.cancelled:
        return Colors.grey;
      case OrderStatus.refunded:
        return Colors.purple;
    }
  }

  /// 获取支付方式文本
  String _getPaymentMethodText() {
    switch (order.paymentMethodEnum) {
      case PaymentMethod.alipay:
        return '支付宝';
      case PaymentMethod.wechat:
        return '微信支付';
      case PaymentMethod.unionpay:
        return '银联支付';
      case PaymentMethod.balance:
        return '余额支付';
      case null:
        return '未知';
    }
  }
}
