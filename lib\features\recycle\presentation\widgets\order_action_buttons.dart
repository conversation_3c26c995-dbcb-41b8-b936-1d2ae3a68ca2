import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/features/recycle/domain/entities/recycle_order.dart';
import 'package:soko/core/enums/app_enums.dart';

/// 订单操作按钮组件
class OrderActionButtons extends StatelessWidget {
  const OrderActionButtons({
    super.key,
    required this.order,
    this.onCancel,
    this.onConfirmPrice,
    this.onConfirmShipment,
    this.onViewLogistics,
    this.onReorder,
  });

  final RecycleOrder order;
  final VoidCallback? onCancel;
  final VoidCallback? onConfirmPrice;
  final VoidCallback? onConfirmShipment;
  final VoidCallback? onViewLogistics;
  final VoidCallback? onReorder;

  @override
  Widget build(BuildContext context) {
    final actions = _getAvailableActions();
    
    if (actions.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: actions.length == 1
            ? _buildSingleButton(context, actions.first)
            : _buildMultipleButtons(context, actions),
      ),
    );
  }

  /// 构建单个按钮
  Widget _buildSingleButton(BuildContext context, OrderAction action) {
    return SizedBox(
      width: double.infinity,
      height: 48.h,
      child: ElevatedButton(
        onPressed: action.onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: action.isPrimary 
              ? Theme.of(context).primaryColor 
              : Colors.white,
          foregroundColor: action.isPrimary 
              ? Colors.white 
              : action.color,
          side: action.isPrimary 
              ? null 
              : BorderSide(color: action.color),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.r),
          ),
          elevation: action.isPrimary ? 2 : 0,
        ),
        child: Text(
          action.text,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  /// 构建多个按钮
  Widget _buildMultipleButtons(BuildContext context, List<OrderAction> actions) {
    return Row(
      children: actions.asMap().entries.map((entry) {
        final index = entry.key;
        final action = entry.value;
        
        return Expanded(
          child: Padding(
            padding: EdgeInsets.only(
              left: index > 0 ? 8.w : 0,
            ),
            child: SizedBox(
              height: 48.h,
              child: action.isPrimary
                  ? ElevatedButton(
                      onPressed: action.onPressed,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).primaryColor,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        elevation: 2,
                      ),
                      child: Text(
                        action.text,
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    )
                  : OutlinedButton(
                      onPressed: action.onPressed,
                      style: OutlinedButton.styleFrom(
                        foregroundColor: action.color,
                        side: BorderSide(color: action.color),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      child: Text(
                        action.text,
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
            ),
          ),
        );
      }).toList(),
    );
  }

  /// 获取可用的操作
  List<OrderAction> _getAvailableActions() {
    final actions = <OrderAction>[];

    switch (order.statusEnum) {
      case RecycleOrderStatus.created:
        // 待审核状态：可以取消订单
        if (onCancel != null) {
          actions.add(OrderAction(
            text: '取消订单',
            onPressed: onCancel!,
            color: Colors.red[600]!,
            isPrimary: false,
          ));
        }
        break;

      case RecycleOrderStatus.confirmed:
        // 已报价状态：可以确认价格或取消订单
        if (onCancel != null) {
          actions.add(OrderAction(
            text: '取消订单',
            onPressed: onCancel!,
            color: Colors.red[600]!,
            isPrimary: false,
          ));
        }
        if (onConfirmPrice != null) {
          actions.add(OrderAction(
            text: '确认价格',
            onPressed: onConfirmPrice!,
            color: Theme.of(context).primaryColor,
            isPrimary: true,
          ));
        }
        break;

      case RecycleOrderStatus.picked:
        // 已寄送状态：可以查看物流
        if (onViewLogistics != null) {
          actions.add(OrderAction(
            text: '查看物流',
            onPressed: onViewLogistics!,
            color: Colors.blue[600]!,
            isPrimary: true,
          ));
        }
        break;

      case RecycleOrderStatus.evaluated:
        // 已检测状态：可以确认最终价格
        if (onConfirmPrice != null) {
          actions.add(OrderAction(
            text: '确认价格',
            onPressed: onConfirmPrice!,
            color: Theme.of(context).primaryColor,
            isPrimary: true,
          ));
        }
        break;

      case RecycleOrderStatus.completed:
        // 已完成状态：可以重新下单
        if (onReorder != null) {
          actions.add(OrderAction(
            text: '再次回收',
            onPressed: onReorder!,
            color: Theme.of(context).primaryColor,
            isPrimary: true,
          ));
        }
        break;

      case RecycleOrderStatus.cancelled:
        // 已取消状态：可以重新下单
        if (onReorder != null) {
          actions.add(OrderAction(
            text: '重新下单',
            onPressed: onReorder!,
            color: Theme.of(context).primaryColor,
            isPrimary: true,
          ));
        }
        break;
    }

    return actions;
  }
}

/// 订单操作数据模型
class OrderAction {
  const OrderAction({
    required this.text,
    required this.onPressed,
    required this.color,
    this.isPrimary = false,
  });

  final String text;
  final VoidCallback onPressed;
  final Color color;
  final bool isPrimary;
}

/// RecycleOrder 扩展方法
extension RecycleOrderActions on RecycleOrder {
  /// 是否可以取消
  bool get canCancel {
    return statusEnum == RecycleOrderStatus.created || 
           statusEnum == RecycleOrderStatus.confirmed;
  }

  /// 是否可以确认价格
  bool get canConfirmPrice {
    return statusEnum == RecycleOrderStatus.confirmed || 
           statusEnum == RecycleOrderStatus.evaluated;
  }

  /// 是否可以确认寄送
  bool get canConfirmShipment {
    return statusEnum == RecycleOrderStatus.confirmed;
  }

  /// 是否可以查看物流
  bool get canViewLogistics {
    return statusEnum == RecycleOrderStatus.picked;
  }

  /// 是否可以重新下单
  bool get canReorder {
    return statusEnum == RecycleOrderStatus.completed || 
           statusEnum == RecycleOrderStatus.cancelled;
  }
}
