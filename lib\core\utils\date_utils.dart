import 'package:intl/intl.dart';

/// 日期时间工具类
class DateUtils {
  // 私有构造函数
  DateUtils._();

  // 常用日期格式
  static const String defaultDateFormat = 'yyyy-MM-dd';
  static const String defaultTimeFormat = 'HH:mm:ss';
  static const String defaultDateTimeFormat = 'yyyy-MM-dd HH:mm:ss';
  static const String chineseDateFormat = 'yyyy年MM月dd日';
  static const String chineseDateTimeFormat = 'yyyy年MM月dd日 HH:mm';
  static const String shortDateFormat = 'MM-dd';
  static const String shortTimeFormat = 'HH:mm';

  /// 格式化日期
  static String formatDate(DateTime date, {String format = defaultDateFormat}) {
    return DateFormat(format).format(date);
  }

  /// 格式化时间
  static String formatTime(DateTime date, {String format = defaultTimeFormat}) {
    return DateFormat(format).format(date);
  }

  /// 格式化日期时间
  static String formatDateTime(DateTime date, {String format = defaultDateTimeFormat}) {
    return DateFormat(format).format(date);
  }

  /// 解析日期字符串
  static DateTime? parseDate(String dateString, {String format = defaultDateFormat}) {
    try {
      return DateFormat(format).parse(dateString);
    } catch (e) {
      return null;
    }
  }

  /// 解析日期时间字符串
  static DateTime? parseDateTime(String dateTimeString, {String format = defaultDateTimeFormat}) {
    try {
      return DateFormat(format).parse(dateTimeString);
    } catch (e) {
      return null;
    }
  }

  /// 获取相对时间描述
  static String getRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()}年前';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()}个月前';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }

  /// 获取友好的日期时间描述
  static String getFriendlyDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final dateOnly = DateTime(dateTime.year, dateTime.month, dateTime.day);

    if (dateOnly == today) {
      return '今天 ${formatTime(dateTime, format: shortTimeFormat)}';
    } else if (dateOnly == yesterday) {
      return '昨天 ${formatTime(dateTime, format: shortTimeFormat)}';
    } else if (now.year == dateTime.year) {
      return formatDateTime(dateTime, format: 'MM-dd HH:mm');
    } else {
      return formatDateTime(dateTime, format: 'yyyy-MM-dd HH:mm');
    }
  }

  /// 判断是否为今天
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month && date.day == now.day;
  }

  /// 判断是否为昨天
  static bool isYesterday(DateTime date) {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return date.year == yesterday.year && 
           date.month == yesterday.month && 
           date.day == yesterday.day;
  }

  /// 判断是否为本周
  static bool isThisWeek(DateTime date) {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    
    return date.isAfter(startOfWeek.subtract(const Duration(days: 1))) &&
           date.isBefore(endOfWeek.add(const Duration(days: 1)));
  }

  /// 判断是否为本月
  static bool isThisMonth(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month;
  }

  /// 判断是否为本年
  static bool isThisYear(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year;
  }

  /// 获取月份的第一天
  static DateTime getFirstDayOfMonth(DateTime date) {
    return DateTime(date.year, date.month);
  }

  /// 获取月份的最后一天
  static DateTime getLastDayOfMonth(DateTime date) {
    return DateTime(date.year, date.month + 1, 0);
  }

  /// 获取周的第一天（周一）
  static DateTime getFirstDayOfWeek(DateTime date) {
    return date.subtract(Duration(days: date.weekday - 1));
  }

  /// 获取周的最后一天（周日）
  static DateTime getLastDayOfWeek(DateTime date) {
    return date.add(Duration(days: 7 - date.weekday));
  }

  /// 计算年龄
  static int calculateAge(DateTime birthDate) {
    final now = DateTime.now();
    var age = now.year - birthDate.year;
    
    if (now.month < birthDate.month || 
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    
    return age;
  }

  /// 获取时间戳（毫秒）
  static int getTimestamp([DateTime? date]) {
    return (date ?? DateTime.now()).millisecondsSinceEpoch;
  }

  /// 从时间戳创建DateTime
  static DateTime fromTimestamp(int timestamp) {
    return DateTime.fromMillisecondsSinceEpoch(timestamp);
  }

  /// 获取UTC时间戳（秒）
  static int getUtcTimestamp([DateTime? date]) {
    return ((date ?? DateTime.now()).millisecondsSinceEpoch / 1000).round();
  }

  /// 从UTC时间戳创建DateTime
  static DateTime fromUtcTimestamp(int timestamp) {
    return DateTime.fromMillisecondsSinceEpoch(timestamp * 1000, isUtc: true).toLocal();
  }
}
