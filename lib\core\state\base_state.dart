import 'package:freezed_annotation/freezed_annotation.dart';

part 'base_state.freezed.dart';

/// 基础状态类
@freezed
class BaseState<T> with _$BaseState<T> {
  const factory BaseState.initial() = _Initial<T>;
  const factory BaseState.loading() = _Loading<T>;
  const factory BaseState.success(T data) = _Success<T>;
  const factory BaseState.error(String message, {String? code}) = _Error<T>;
}

/// 分页状态类
@freezed
class PageState<T> with _$PageState<T> {
  const factory PageState({
    required List<T> items,
    required bool isLoading,
    required bool hasMore,
    required int currentPage,
    required int totalPages,
    required int totalCount,
    String? error,
  }) = _PageState<T>;

  factory PageState.initial() => const PageState(
        items: [],
        isLoading: false,
        hasMore: true,
        currentPage: 0,
        totalPages: 0,
        totalCount: 0,
      );
}

/// 异步状态类
@freezed
class AsyncState<T> with _$AsyncState<T> {
  const factory AsyncState.idle() = _Idle<T>;
  const factory AsyncState.loading() = _AsyncLoading<T>;
  const factory AsyncState.success(T data) = _AsyncSuccess<T>;
  const factory AsyncState.error(String message) = _AsyncError<T>;
}

/// 表单状态类
@freezed
class FormState with _$FormState {
  const factory FormState({
    required bool isValid,
    required bool isSubmitting,
    required Map<String, String> errors,
    String? submitError,
  }) = _FormState;

  factory FormState.initial() => const FormState(
        isValid: false,
        isSubmitting: false,
        errors: {},
      );
}

/// 状态扩展方法
extension BaseStateExtension<T> on BaseState<T> {
  /// 是否为初始状态
  bool get isInitial => this is _Initial<T>;

  /// 是否为加载状态
  bool get isLoading => this is _Loading<T>;

  /// 是否为成功状态
  bool get isSuccess => this is _Success<T>;

  /// 是否为错误状态
  bool get isError => this is _Error<T>;

  /// 获取数据（如果是成功状态）
  T? get dataOrNull => maybeWhen(
        success: (data) => data,
        orElse: () => null,
      );

  /// 获取错误信息（如果是错误状态）
  String? get errorOrNull => maybeWhen(
        error: (message, code) => message,
        orElse: () => null,
      );
}

extension PageStateExtension<T> on PageState<T> {
  /// 是否为空列表
  bool get isEmpty => items.isEmpty && !isLoading;

  /// 是否可以加载更多
  bool get canLoadMore => hasMore && !isLoading;

  /// 是否为第一页加载
  bool get isFirstPageLoading => isLoading && items.isEmpty;

  /// 是否为加载更多
  bool get isLoadingMore => isLoading && items.isNotEmpty;
}

extension AsyncStateExtension<T> on AsyncState<T> {
  /// 是否为空闲状态
  bool get isIdle => this is _Idle<T>;

  /// 是否为加载状态
  bool get isLoading => this is _AsyncLoading<T>;

  /// 是否为成功状态
  bool get isSuccess => this is _AsyncSuccess<T>;

  /// 是否为错误状态
  bool get isError => this is _AsyncError<T>;

  /// 获取数据（如果是成功状态）
  T? get dataOrNull => maybeWhen(
        success: (data) => data,
        orElse: () => null,
      );

  /// 获取错误信息（如果是错误状态）
  String? get errorOrNull => maybeWhen(
        error: (message) => message,
        orElse: () => null,
      );
}
