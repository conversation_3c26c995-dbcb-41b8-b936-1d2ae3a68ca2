import 'package:flutter/material.dart';

/// 应用国际化配置
class AppLocalizations {
  // 私有构造函数
  AppLocalizations._();
  
  /// 支持的语言列表
  static const List<Locale> supportedLocales = [
    Locale('zh', 'CN'), // 简体中文
    Locale('en', 'US'), // 英语
  ];
  
  /// 本地化代理列表
  static const List<LocalizationsDelegate> localizationsDelegates = [
    DefaultMaterialLocalizations.delegate,
    DefaultWidgetsLocalizations.delegate,
    DefaultCupertinoLocalizations.delegate,
  ];
  
  /// 获取当前语言环境
  static Locale? localeResolutionCallback(
    Locale? locale,
    Iterable<Locale> supportedLocales,
  ) {
    // 如果设备语言在支持列表中，使用设备语言
    if (locale != null) {
      for (final supportedLocale in supportedLocales) {
        if (supportedLocale.languageCode == locale.languageCode) {
          return supportedLocale;
        }
      }
    }
    
    // 默认使用中文
    return const Locale('zh', 'CN');
  }
}
