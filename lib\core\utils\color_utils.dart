import 'package:flutter/material.dart';

/// 颜色工具类
class ColorUtils {
  /// 将十六进制颜色字符串转换为 Color 对象
  static Color hexToColor(String hexString) {
    final buffer = StringBuffer();
    if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
    buffer.write(hexString.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }

  /// 将 Color 对象转换为十六进制字符串
  static String colorToHex(Color color) {
    return '#${color.value.toRadixString(16).substring(2).toUpperCase()}';
  }

  /// 获取颜色的亮度
  static double getLuminance(Color color) {
    return color.computeLuminance();
  }

  /// 判断颜色是否为深色
  static bool isDark(Color color) {
    return getLuminance(color) < 0.5;
  }

  /// 获取对比色（黑色或白色）
  static Color getContrastColor(Color color) {
    return isDark(color) ? Colors.white : Colors.black;
  }

  /// 获取颜色的透明度版本
  static Color withOpacity(Color color, double opacity) {
    return color.withValues(alpha: opacity);
  }

  /// 混合两种颜色
  static Color blendColors(Color color1, Color color2, double ratio) {
    return Color.lerp(color1, color2, ratio) ?? color1;
  }

  /// 获取颜色的深色版本
  static Color darken(Color color, [double amount = 0.1]) {
    final hsl = HSLColor.fromColor(color);
    final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));
    return hslDark.toColor();
  }

  /// 获取颜色的浅色版本
  static Color lighten(Color color, [double amount = 0.1]) {
    final hsl = HSLColor.fromColor(color);
    final hslLight = hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));
    return hslLight.toColor();
  }
}
