import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import 'package:soko/shared/presentation/widgets/custom_app_bar.dart';
import 'package:soko/features/payment/presentation/providers/payment_provider.dart';
import 'package:soko/features/payment/presentation/widgets/payment_result_card.dart';
import 'package:soko/features/payment/presentation/widgets/payment_next_steps.dart';

/// 支付结果页面
class PaymentResultPage extends ConsumerStatefulWidget {
  const PaymentResultPage({
    super.key,
    required this.success,
    required this.paymentId,
    required this.orderId,
    required this.amount,
    this.errorMessage,
  });

  final bool success;
  final String paymentId;
  final String orderId;
  final double amount;
  final String? errorMessage;

  @override
  ConsumerState<PaymentResultPage> createState() => _PaymentResultPageState();
}

class _PaymentResultPageState extends ConsumerState<PaymentResultPage> {
  @override
  void initState() {
    super.initState();
    
    // 如果支付成功，查询最终支付结果
    if (widget.success) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(paymentProvider.notifier).queryPaymentResult(widget.paymentId);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: '支付结果',
        showBackButton: false,
        actions: [
          IconButton(
            onPressed: () => context.go('/'),
            icon: const Icon(Icons.home),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            SizedBox(height: 40.h),
            
            // 支付结果卡片
            PaymentResultCard(
              success: widget.success,
              paymentId: widget.paymentId,
              orderId: widget.orderId,
              amount: widget.amount,
              errorMessage: widget.errorMessage,
            ),
            SizedBox(height: 32.h),
            
            // 下一步操作
            PaymentNextSteps(
              success: widget.success,
              orderId: widget.orderId,
            ),
            SizedBox(height: 32.h),
            
            // 操作按钮
            _buildActionButtons(),
            
            // 底部安全距离
            SizedBox(height: 32.h),
          ],
        ),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Column(
      children: [
        // 查看订单按钮
        SizedBox(
          width: double.infinity,
          height: 48.h,
          child: ElevatedButton(
            onPressed: () => _viewOrder(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            child: Text(
              '查看订单详情',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        SizedBox(height: 12.h),
        
        // 继续购物按钮
        SizedBox(
          width: double.infinity,
          height: 48.h,
          child: OutlinedButton(
            onPressed: () => _continueShopping(),
            style: OutlinedButton.styleFrom(
              foregroundColor: Theme.of(context).primaryColor,
              side: BorderSide(color: Theme.of(context).primaryColor),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            child: Text(
              '继续购物',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        
        // 如果支付失败，显示重新支付按钮
        if (!widget.success) ...[
          SizedBox(height: 12.h),
          SizedBox(
            width: double.infinity,
            height: 48.h,
            child: ElevatedButton(
              onPressed: () => _retryPayment(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
              child: Text(
                '重新支付',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// 查看订单
  void _viewOrder() {
    context.push('/order/${widget.orderId}');
  }

  /// 继续购物
  void _continueShopping() {
    context.go('/');
  }

  /// 重新支付
  void _retryPayment() {
    context.pushReplacement('/payment', extra: {
      'orderId': widget.orderId,
      'amount': widget.amount,
    });
  }
}
