// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

User _$UserFromJson(Map<String, dynamic> json) => User(
      id: json['id'] as String,
      username: json['username'] as String?,
      nickname: json['nickname'] as String?,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      avatar: json['avatar'] as String?,
      gender: json['gender'] as String?,
      birthday: json['birthday'] as String?,
      realName: json['realName'] as String?,
      idCard: json['idCard'] as String?,
      memberLevel: json['memberLevel'] as String?,
      memberExpireTime: (json['memberExpireTime'] as num?)?.toInt(),
      balance: (json['balance'] as num?)?.toDouble(),
      points: (json['points'] as num?)?.toInt(),
      status: json['status'] as String,
      createTime: (json['createTime'] as num).toInt(),
      updateTime: (json['updateTime'] as num).toInt(),
      lastLoginTime: (json['lastLoginTime'] as num?)?.toInt(),
      loginCount: (json['loginCount'] as num?)?.toInt(),
    );

Map<String, dynamic> _$UserToJson(User instance) => <String, dynamic>{
      'id': instance.id,
      'username': instance.username,
      'nickname': instance.nickname,
      'email': instance.email,
      'phone': instance.phone,
      'avatar': instance.avatar,
      'gender': instance.gender,
      'birthday': instance.birthday,
      'realName': instance.realName,
      'idCard': instance.idCard,
      'memberLevel': instance.memberLevel,
      'memberExpireTime': instance.memberExpireTime,
      'balance': instance.balance,
      'points': instance.points,
      'status': instance.status,
      'createTime': instance.createTime,
      'updateTime': instance.updateTime,
      'lastLoginTime': instance.lastLoginTime,
      'loginCount': instance.loginCount,
    };
