import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 状态徽章类型
enum StatusBadgeType {
  primary,
  success,
  warning,
  error,
  info,
  secondary,
}

/// 状态徽章组件
class StatusBadge extends StatelessWidget {
  const StatusBadge({
    super.key,
    required this.status,
    this.type = StatusBadgeType.primary,
    this.size = StatusBadgeSize.medium,
  });

  final String status;
  final StatusBadgeType type;
  final StatusBadgeSize size;

  @override
  Widget build(BuildContext context) {
    final colors = _getColors(context);
    final textStyle = _getTextStyle();

    return Container(
      padding: _getPadding(),
      decoration: BoxDecoration(
        color: colors.backgroundColor,
        borderRadius: BorderRadius.circular(_getBorderRadius()),
        border: Border.all(
          color: colors.borderColor,
          width: 1,
        ),
      ),
      child: Text(
        status,
        style: textStyle.copyWith(color: colors.textColor),
      ),
    );
  }

  /// 获取颜色配置
  _StatusBadgeColors _getColors(BuildContext context) {
    switch (type) {
      case StatusBadgeType.primary:
        return _StatusBadgeColors(
          backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
          borderColor: Theme.of(context).primaryColor.withValues(alpha: 0.3),
          textColor: Theme.of(context).primaryColor,
        );
      case StatusBadgeType.success:
        return _StatusBadgeColors(
          backgroundColor: Colors.green.withValues(alpha: 0.1),
          borderColor: Colors.green.withValues(alpha: 0.3),
          textColor: Colors.green[700]!,
        );
      case StatusBadgeType.warning:
        return _StatusBadgeColors(
          backgroundColor: Colors.orange.withValues(alpha: 0.1),
          borderColor: Colors.orange.withValues(alpha: 0.3),
          textColor: Colors.orange[700]!,
        );
      case StatusBadgeType.error:
        return _StatusBadgeColors(
          backgroundColor: Colors.red.withValues(alpha: 0.1),
          borderColor: Colors.red.withValues(alpha: 0.3),
          textColor: Colors.red[700]!,
        );
      case StatusBadgeType.info:
        return _StatusBadgeColors(
          backgroundColor: Colors.blue.withValues(alpha: 0.1),
          borderColor: Colors.blue.withValues(alpha: 0.3),
          textColor: Colors.blue[700]!,
        );
      case StatusBadgeType.secondary:
        return _StatusBadgeColors(
          backgroundColor: Colors.grey.withValues(alpha: 0.1),
          borderColor: Colors.grey.withValues(alpha: 0.3),
          textColor: Colors.grey[700]!,
        );
    }
  }

  /// 获取文本样式
  TextStyle _getTextStyle() {
    switch (size) {
      case StatusBadgeSize.small:
        return TextStyle(
          fontSize: 10.sp,
          fontWeight: FontWeight.w500,
        );
      case StatusBadgeSize.medium:
        return TextStyle(
          fontSize: 12.sp,
          fontWeight: FontWeight.w500,
        );
      case StatusBadgeSize.large:
        return TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w500,
        );
    }
  }

  /// 获取内边距
  EdgeInsets _getPadding() {
    switch (size) {
      case StatusBadgeSize.small:
        return EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h);
      case StatusBadgeSize.medium:
        return EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h);
      case StatusBadgeSize.large:
        return EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h);
    }
  }

  /// 获取圆角半径
  double _getBorderRadius() {
    switch (size) {
      case StatusBadgeSize.small:
        return 8.r;
      case StatusBadgeSize.medium:
        return 10.r;
      case StatusBadgeSize.large:
        return 12.r;
    }
  }
}

/// 状态徽章尺寸
enum StatusBadgeSize {
  small,
  medium,
  large,
}

/// 状态徽章颜色配置
class _StatusBadgeColors {
  const _StatusBadgeColors({
    required this.backgroundColor,
    required this.borderColor,
    required this.textColor,
  });

  final Color backgroundColor;
  final Color borderColor;
  final Color textColor;
}
