import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

/// 回收订单创建 - 图片上传步骤
class RecycleCreateImagesStep extends ConsumerStatefulWidget {

  const RecycleCreateImagesStep({
    super.key,
    required this.imageFiles,
    required this.onImagesChanged,
  });
  final List<String> imageFiles;
  final ValueChanged<List<String>> onImagesChanged;

  @override
  ConsumerState<RecycleCreateImagesStep> createState() => _RecycleCreateImagesStepState();
}

class _RecycleCreateImagesStepState extends ConsumerState<RecycleCreateImagesStep> {
  final ImagePicker _picker = ImagePicker();
  final int maxImages = 6;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 步骤标题
          Text(
            '上传商品图片',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '请上传商品的清晰照片，有助于准确估价',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 24.h),

          // 图片网格
          Expanded(
            child: GridView.builder(
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 12.w,
                mainAxisSpacing: 12.h,
              ),
              itemCount: widget.imageFiles.length + (widget.imageFiles.length < maxImages ? 1 : 0),
              itemBuilder: (context, index) {
                if (index < widget.imageFiles.length) {
                  return _buildImageItem(widget.imageFiles[index], index);
                } else {
                  return _buildAddImageButton();
                }
              },
            ),
          ),

          // 提示信息
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.camera_alt,
                      color: Colors.orange,
                      size: 16.w,
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      '拍照建议',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                        color: Colors.orange[700],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8.h),
                Text(
                  '• 请在光线充足的环境下拍摄\n• 包含商品正面、背面、侧面等多角度\n• 如有损坏或瑕疵，请特别拍摄\n• 最多可上传$maxImages张图片',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.orange[600],
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建图片项
  Widget _buildImageItem(String imagePath, int index) {
    return Stack(
      children: [
        // 图片
        Container(
          width: double.infinity,
          height: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8.r),
            child: Image.file(
              File(imagePath),
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey[200],
                  child: Icon(
                    Icons.broken_image,
                    color: Colors.grey[400],
                    size: 32.w,
                  ),
                );
              },
            ),
          ),
        ),

        // 删除按钮
        Positioned(
          top: 4.w,
          right: 4.w,
          child: InkWell(
            onTap: () => _removeImage(index),
            child: Container(
              width: 24.w,
              height: 24.w,
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Icon(
                Icons.close,
                color: Colors.white,
                size: 16.w,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建添加图片按钮
  Widget _buildAddImageButton() {
    return InkWell(
      onTap: _showImageSourceDialog,
      borderRadius: BorderRadius.circular(8.r),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: Colors.grey.withValues(alpha: 0.3),
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_a_photo,
              color: Colors.grey[600],
              size: 32.w,
            ),
            SizedBox(height: 8.h),
            Text(
              '添加图片',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示图片来源选择对话框
  void _showImageSourceDialog() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text('拍照'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImage(ImageSource.camera);
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('从相册选择'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImage(ImageSource.gallery);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  /// 选择图片
  Future<void> _pickImage(ImageSource source) async {
    try {
      final image = await _picker.pickImage(
        source: source,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 80,
      );

      if (image != null) {
        final newImages = List<String>.from(widget.imageFiles);
        newImages.add(image.path);
        widget.onImagesChanged(newImages);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('选择图片失败: $e')),
        );
      }
    }
  }

  /// 移除图片
  void _removeImage(int index) {
    final newImages = List<String>.from(widget.imageFiles);
    newImages.removeAt(index);
    widget.onImagesChanged(newImages);
  }
}
