import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/shared/presentation/widgets/custom_card.dart';
import 'package:soko/features/order/domain/entities/order.dart';

/// 订单收货地址卡片
class OrderDetailAddressCard extends StatelessWidget {

  const OrderDetailAddressCard({
    super.key,
    required this.order,
  });
  final Order order;

  @override
  Widget build(BuildContext context) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.location_on,
                color: Colors.blue,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                '收货地址',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // 收货人信息
          Row(
            children: [
              Text(
                order.receiverName,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
              SizedBox(width: 16.w),
              Text(
                order.receiverPhone,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),

          // 详细地址
          Text(
            order.fullAddress,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.black87,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }
}
