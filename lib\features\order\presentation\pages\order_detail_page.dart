import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import 'package:soko/core/enums/app_enums.dart';
import 'package:soko/core/utils/date_utils.dart' as app_date_utils;
import 'package:soko/shared/presentation/widgets/custom_app_bar.dart';
import 'package:soko/shared/presentation/widgets/custom_button.dart'
    hide TextButton;
import 'package:soko/shared/presentation/widgets/loading_widget.dart';
import 'package:soko/shared/presentation/widgets/error_widget.dart'
    as error_widget;
import 'package:soko/features/order/domain/entities/order.dart';
import 'package:soko/features/order/presentation/providers/order_detail_provider.dart';
import 'package:soko/features/order/presentation/providers/order_actions_provider.dart';
import 'package:soko/features/order/presentation/widgets/order_detail_info_card.dart';
import 'package:soko/features/order/presentation/widgets/order_detail_items_card.dart';
import 'package:soko/features/order/presentation/widgets/order_detail_address_card.dart';
import 'package:soko/features/order/presentation/widgets/order_detail_tracking_card.dart';
import 'package:soko/features/order/presentation/widgets/order_status_progress.dart';
import 'package:soko/features/order/presentation/widgets/order_status_timeline.dart';

/// 订单详情页面
class OrderDetailPage extends ConsumerStatefulWidget {

  const OrderDetailPage({
    super.key,
    required this.orderId,
  });
  final String orderId;

  @override
  ConsumerState<OrderDetailPage> createState() => _OrderDetailPageState();
}

class _OrderDetailPageState extends ConsumerState<OrderDetailPage> {
  @override
  void initState() {
    super.initState();
    // 页面初始化时加载订单详情
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(orderDetailProvider(widget.orderId).notifier).loadOrderDetail();
    });
  }

  @override
  Widget build(BuildContext context) {
    final orderDetailState = ref.watch(orderDetailProvider(widget.orderId));

    return Scaffold(
      appBar: const CustomAppBar(title: '订单详情'),
      body: orderDetailState.when(
        idle: () => const LoadingWidget(),
        loading: () => const LoadingWidget(),
        success: _buildOrderDetail,
        error: (error) => error_widget.ErrorDisplayWidget(
          message: error,
          onRetry: () => ref
              .read(orderDetailProvider(widget.orderId).notifier)
              .loadOrderDetail(),
        ),
      ),
      bottomNavigationBar: orderDetailState.maybeWhen(
        success: _buildBottomActions,
        orElse: () => null,
      ),
    );
  }

  /// 构建订单详情内容
  Widget _buildOrderDetail(Order order) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          // 订单状态进度
          OrderStatusProgress(order: order),
          SizedBox(height: 16.h),

          // 订单状态时间线
          OrderStatusTimeline(order: order),
          SizedBox(height: 16.h),

          // 订单基本信息
          OrderDetailInfoCard(order: order),
          SizedBox(height: 16.h),

          // 商品信息
          OrderDetailItemsCard(order: order),
          SizedBox(height: 16.h),

          // 收货地址
          OrderDetailAddressCard(order: order),
          SizedBox(height: 16.h),

          // 物流信息（如果已发货）
          if (order.statusEnum == OrderStatus.shipped ||
              order.statusEnum == OrderStatus.delivered ||
              order.statusEnum == OrderStatus.completed)
            OrderDetailTrackingCard(order: order),

          // 底部留白，避免被底部按钮遮挡
          SizedBox(height: 100.h),
        ],
      ),
    );
  }

  /// 构建底部操作按钮
  Widget _buildBottomActions(Order order) {
    final actions = _getOrderActions(order);
    if (actions.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: actions
              .map((action) => Expanded(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 4.w),
                      child: action,
                    ),
                  ),)
              .toList(),
        ),
      ),
    );
  }

  /// 获取订单操作按钮
  List<Widget> _getOrderActions(Order order) {
    final actions = <Widget>[];

    switch (order.statusEnum) {
      case OrderStatus.pending:
        actions.addAll([
          CustomButton(
            text: '取消订单',
            onPressed: () => _cancelOrder(order.id),
            type: ButtonType.outline,
          ),
          CustomButton(
            text: '立即支付',
            onPressed: () => _payOrder(order.id),
          ),
        ]);
      case OrderStatus.paid:
        // 已支付，等待发货，暂无操作
        break;
      case OrderStatus.shipped:
        actions.add(
          CustomButton(
            text: '确认收货',
            onPressed: () => _confirmReceived(order.id),
          ),
        );
      case OrderStatus.delivered:
        actions.add(
          CustomButton(
            text: '确认收货',
            onPressed: () => _confirmReceived(order.id),
          ),
        );
      case OrderStatus.completed:
        actions.add(
          CustomButton(
            text: '申请退款',
            onPressed: () => _requestRefund(order.id),
            type: ButtonType.outline,
          ),
        );
      case OrderStatus.cancelled:
        actions.add(
          CustomButton(
            text: '删除订单',
            onPressed: () => _deleteOrder(order.id),
            type: ButtonType.outline,
          ),
        );
      case OrderStatus.refunded:
        // 已退款，暂无操作
        break;
    }

    return actions;
  }

  /// 取消订单
  void _cancelOrder(String orderId) {
    _showActionDialog(
      title: '取消订单',
      content: '确定要取消这个订单吗？',
      onConfirm: () async {
        final success =
            await ref.read(orderActionsProvider.notifier).cancelOrder(orderId);
        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('订单已取消')),
          );
          // 刷新订单详情
          ref.read(orderDetailProvider(orderId).notifier).refreshOrderDetail();
        }
      },
    );
  }

  /// 支付订单
  void _payOrder(String orderId) {
    // TODO: 实现支付订单功能
    // context.push('/payment/$orderId');
  }

  /// 确认收货
  void _confirmReceived(String orderId) {
    _showActionDialog(
      title: '确认收货',
      content: '确认已收到商品吗？确认后订单将完成。',
      onConfirm: () async {
        final success = await ref
            .read(orderActionsProvider.notifier)
            .confirmReceived(orderId);
        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('确认收货成功')),
          );
          // 刷新订单详情
          await ref
              .read(orderDetailProvider(orderId).notifier)
              .refreshOrderDetail();
        }
      },
    );
  }

  /// 申请退款
  void _requestRefund(String orderId) {
    // TODO: 实现申请退款功能
    // context.push('/refund/$orderId');
  }

  /// 删除订单
  void _deleteOrder(String orderId) {
    _showActionDialog(
      title: '删除订单',
      content: '确定要删除这个订单吗？删除后无法恢复。',
      onConfirm: () async {
        final success =
            await ref.read(orderActionsProvider.notifier).deleteOrder(orderId);
        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('订单已删除')),
          );
          // 删除成功后返回上一页
          context.pop();
        }
      },
    );
  }

  /// 显示操作确认对话框
  void _showActionDialog({
    required String title,
    required String content,
    required Future<void> Function() onConfirm,
  }) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await onConfirm();
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
