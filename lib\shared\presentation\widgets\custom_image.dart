import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/core/theme/app_colors.dart';

/// 自定义图片组件
class CustomImage extends StatelessWidget {

  const CustomImage({
    super.key,
    this.imageUrl,
    this.placeholder,
    this.errorImage,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.backgroundColor,
    this.loadingWidget,
    this.errorWidget,
    this.onTap,
  });
  final String? imageUrl;
  final String? placeholder;
  final String? errorImage;
  final double? width;
  final double? height;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final Color? backgroundColor;
  final Widget? loadingWidget;
  final Widget? errorWidget;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    var imageWidget = _buildImageWidget();

    if (borderRadius != null) {
      imageWidget = ClipRRect(
        borderRadius: borderRadius!,
        child: imageWidget,
      );
    }

    if (onTap != null) {
      imageWidget = GestureDetector(
        onTap: onTap,
        child: imageWidget,
      );
    }

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.background,
        borderRadius: borderRadius,
      ),
      child: imageWidget,
    );
  }

  Widget _buildImageWidget() {
    if (imageUrl == null || imageUrl!.isEmpty) {
      return _buildPlaceholder();
    }

    if (imageUrl!.startsWith('http')) {
      return CachedNetworkImage(
        imageUrl: imageUrl!,
        width: width,
        height: height,
        fit: fit,
        placeholder: (context, url) => loadingWidget ?? _buildLoadingWidget(),
        errorWidget: (context, url, error) => errorWidget ?? _buildErrorWidget(),
      );
    } else {
      return Image.asset(
        imageUrl!,
        width: width,
        height: height,
        fit: fit,
        errorBuilder: (context, error, stackTrace) {
          return errorWidget ?? _buildErrorWidget();
        },
      );
    }
  }

  Widget _buildPlaceholder() {
    if (placeholder != null) {
      return Image.asset(
        placeholder!,
        width: width,
        height: height,
        fit: fit,
      );
    }
    return _buildDefaultPlaceholder();
  }

  Widget _buildLoadingWidget() {
    return Container(
      width: width,
      height: height,
      color: AppColors.background,
      child: Center(
        child: SizedBox(
          width: 24.w,
          height: 24.w,
          child: const CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    if (errorImage != null) {
      return Image.asset(
        errorImage!,
        width: width,
        height: height,
        fit: fit,
      );
    }
    return _buildDefaultErrorWidget();
  }

  Widget _buildDefaultPlaceholder() {
    return Container(
      width: width,
      height: height,
      color: AppColors.background,
      child: Center(
        child: Icon(
          Icons.image,
          size: 32.w,
          color: AppColors.textTertiary,
        ),
      ),
    );
  }

  Widget _buildDefaultErrorWidget() {
    return Container(
      width: width,
      height: height,
      color: AppColors.background,
      child: Center(
        child: Icon(
          Icons.broken_image,
          size: 32.w,
          color: AppColors.textTertiary,
        ),
      ),
    );
  }
}

/// 圆形头像组件
class AvatarImage extends StatelessWidget {

  const AvatarImage({
    super.key,
    this.imageUrl,
    this.size = 40,
    this.placeholder,
    this.onTap,
  });
  final String? imageUrl;
  final double size;
  final String? placeholder;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return CustomImage(
      imageUrl: imageUrl,
      placeholder: placeholder ?? 'assets/images/default_avatar.png',
      width: size.w,
      height: size.w,
      borderRadius: BorderRadius.circular(size.w / 2),
      onTap: onTap,
    );
  }
}

/// 商品图片组件
class ProductImage extends StatelessWidget {

  const ProductImage({
    super.key,
    this.imageUrl,
    this.width,
    this.height,
    this.onTap,
  });
  final String? imageUrl;
  final double? width;
  final double? height;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return CustomImage(
      imageUrl: imageUrl,
      placeholder: 'assets/images/default_product.png',
      width: width,
      height: height,
      borderRadius: BorderRadius.circular(8.r),
      onTap: onTap,
    );
  }
}

/// 轮播图组件
class BannerImage extends StatelessWidget {

  const BannerImage({
    super.key,
    this.imageUrl,
    this.width,
    this.height,
    this.onTap,
  });
  final String? imageUrl;
  final double? width;
  final double? height;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return CustomImage(
      imageUrl: imageUrl,
      placeholder: 'assets/images/default_banner.png',
      width: width,
      height: height,
      borderRadius: BorderRadius.circular(12.r),
      onTap: onTap,
    );
  }
}

/// 图片预览组件
class ImagePreview extends StatelessWidget {

  const ImagePreview({
    super.key,
    required this.images,
    this.initialIndex = 0,
  });
  final List<String> images;
  final int initialIndex;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close, color: Colors.white),
        ),
        title: Text(
          '${initialIndex + 1}/${images.length}',
          style: const TextStyle(color: Colors.white),
        ),
      ),
      body: PageView.builder(
        controller: PageController(initialPage: initialIndex),
        itemCount: images.length,
        itemBuilder: (context, index) {
          return Center(
            child: InteractiveViewer(
              child: CustomImage(
                imageUrl: images[index],
                fit: BoxFit.contain,
              ),
            ),
          );
        },
      ),
    );
  }

  static void show(
    BuildContext context, {
    required List<String> images,
    int initialIndex = 0,
  }) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ImagePreview(
          images: images,
          initialIndex: initialIndex,
        ),
      ),
    );
  }
}

/// 图片网格组件
class ImageGrid extends StatelessWidget {

  const ImageGrid({
    super.key,
    required this.images,
    this.maxCount = 9,
    this.spacing = 8,
    this.aspectRatio = 1.0,
    this.onAddImage,
    this.onImageTap,
    this.onImageRemove,
  });
  final List<String> images;
  final int maxCount;
  final double spacing;
  final double aspectRatio;
  final VoidCallback? onAddImage;
  final Function(int index)? onImageTap;
  final Function(int index)? onImageRemove;

  @override
  Widget build(BuildContext context) {
    final canAddMore = images.length < maxCount;
    final totalCount = canAddMore ? images.length + 1 : images.length;
    
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: spacing.w,
        mainAxisSpacing: spacing.h,
        childAspectRatio: aspectRatio,
      ),
      itemCount: totalCount,
      itemBuilder: (context, index) {
        if (index == images.length && canAddMore) {
          return _buildAddButton();
        }
        return _buildImageItem(index);
      },
    );
  }

  Widget _buildImageItem(int index) {
    return Stack(
      children: [
        GestureDetector(
          onTap: () => onImageTap?.call(index),
          child: CustomImage(
            imageUrl: images[index],
            width: double.infinity,
            height: double.infinity,
            borderRadius: BorderRadius.circular(8.r),
          ),
        ),
        if (onImageRemove != null)
          Positioned(
            top: 4.w,
            right: 4.w,
            child: GestureDetector(
              onTap: () => onImageRemove?.call(index),
              child: Container(
                width: 20.w,
                height: 20.w,
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(10.w),
                ),
                child: Icon(
                  Icons.close,
                  size: 14.w,
                  color: Colors.white,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildAddButton() {
    return GestureDetector(
      onTap: onAddImage,
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.background,
          border: Border.all(
            color: AppColors.border,
          ),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.add,
                size: 24.w,
                color: AppColors.textTertiary,
              ),
              SizedBox(height: 4.h),
              Text(
                '添加图片',
                style: TextStyle(
                  fontSize: 10.sp,
                  color: AppColors.textTertiary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
