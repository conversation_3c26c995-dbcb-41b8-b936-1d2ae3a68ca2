import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/features/recycle/domain/entities/recycle_models.dart';

/// 市场趋势组件
class MarketTrendSection extends StatelessWidget {
  const MarketTrendSection({
    super.key,
    required this.trend,
  });

  final MarketTrend trend;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.show_chart,
                size: 20.w,
                color: Theme.of(context).primaryColor,
              ),
              SizedBox(width: 8.w),
              Text(
                '市场趋势',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          
          // 趋势卡片
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: _getTrendColor().withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(
                color: _getTrendColor().withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                // 趋势图标
                Container(
                  width: 48.w,
                  height: 48.w,
                  decoration: BoxDecoration(
                    color: _getTrendColor().withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(24.r),
                  ),
                  child: Icon(
                    _getTrendIcon(),
                    size: 24.w,
                    color: _getTrendColor(),
                  ),
                ),
                SizedBox(width: 16.w),
                
                // 趋势信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            _getTrendLabel(),
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w600,
                              color: _getTrendColor(),
                            ),
                          ),
                          SizedBox(width: 8.w),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 6.w,
                              vertical: 2.h,
                            ),
                            decoration: BoxDecoration(
                              color: _getTrendColor(),
                              borderRadius: BorderRadius.circular(10.r),
                            ),
                            child: Text(
                              '${trend.percentage > 0 ? '+' : ''}${trend.percentage.toStringAsFixed(1)}%',
                              style: TextStyle(
                                fontSize: 10.sp,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        trend.description,
                        style: TextStyle(
                          fontSize: 13.sp,
                          color: Colors.grey[700],
                          height: 1.3,
                        ),
                      ),
                      if (trend.period != null) ...[
                        SizedBox(height: 4.h),
                        Text(
                          '统计周期：${trend.period}',
                          style: TextStyle(
                            fontSize: 11.sp,
                            color: Colors.grey[500],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 12.h),
          
          // 趋势说明
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 16.w,
                  color: Colors.grey[600],
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: Text(
                    _getTrendExplanation(),
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey[600],
                      height: 1.3,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 获取趋势颜色
  Color _getTrendColor() {
    switch (trend.trend.toLowerCase()) {
      case 'up':
      case 'rising':
        return Colors.green;
      case 'down':
      case 'falling':
        return Colors.red;
      case 'stable':
      case 'flat':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  /// 获取趋势图标
  IconData _getTrendIcon() {
    switch (trend.trend.toLowerCase()) {
      case 'up':
      case 'rising':
        return Icons.trending_up;
      case 'down':
      case 'falling':
        return Icons.trending_down;
      case 'stable':
      case 'flat':
        return Icons.trending_flat;
      default:
        return Icons.show_chart;
    }
  }

  /// 获取趋势标签
  String _getTrendLabel() {
    switch (trend.trend.toLowerCase()) {
      case 'up':
      case 'rising':
        return '价格上涨';
      case 'down':
      case 'falling':
        return '价格下跌';
      case 'stable':
      case 'flat':
        return '价格稳定';
      default:
        return '市场趋势';
    }
  }

  /// 获取趋势说明
  String _getTrendExplanation() {
    switch (trend.trend.toLowerCase()) {
      case 'up':
      case 'rising':
        return '该产品近期市场需求增加，回收价格呈上涨趋势，建议尽快出售';
      case 'down':
      case 'falling':
        return '该产品近期市场供应增加，回收价格呈下跌趋势，可考虑等待更好时机';
      case 'stable':
      case 'flat':
        return '该产品市场价格相对稳定，是出售的好时机';
      default:
        return '市场趋势数据仅供参考，实际价格可能因具体情况而异';
    }
  }
}
