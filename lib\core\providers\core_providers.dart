import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dio/dio.dart';

import '../network/dio_client.dart';
import '../network/network_info.dart';

/// 网络信息Provider
final networkInfoProvider = Provider<NetworkInfo>((ref) {
  return NetworkInfoImpl();
});

/// Dio客户端Provider
final dioClientProvider = Provider<Dio>((ref) {
  return DioClient().dio;
});

/// HTTP客户端Provider
final httpClientProvider = Provider<DioClient>((ref) {
  return DioClient();
});
