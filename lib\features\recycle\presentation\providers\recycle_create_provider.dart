import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/state/base_state.dart';
import 'package:soko/features/recycle/data/datasources/recycle_api_service.dart';
import 'package:soko/features/recycle/domain/entities/recycle_models.dart';
import 'package:soko/features/recycle/domain/entities/recycle_order.dart';

/// 回收订单创建状态
@immutable
class RecycleCreateState {

  const RecycleCreateState({
    this.currentStep = 0,
    this.formData = const RecycleOrderFormData(),
    this.createOrderState = const AsyncState.idle(),
    this.categoriesState = const AsyncState.idle(),
    this.brandsState = const AsyncState.idle(),
    this.modelsState = const AsyncState.idle(),
    this.conditionsState = const AsyncState.idle(),
    this.priceEstimateState = const AsyncState.idle(),
  });
  final int currentStep;
  final RecycleOrderFormData formData;
  final AsyncState<RecycleOrder> createOrderState;
  final AsyncState<List<CategoryItem>> categoriesState;
  final AsyncState<List<BrandInfo>> brandsState;
  final AsyncState<List<ProductModel>> modelsState;
  final AsyncState<List<ConditionOption>> conditionsState;
  final AsyncState<double> priceEstimateState;

  RecycleCreateState copyWith({
    int? currentStep,
    RecycleOrderFormData? formData,
    AsyncState<RecycleOrder>? createOrderState,
    AsyncState<List<CategoryItem>>? categoriesState,
    AsyncState<List<BrandInfo>>? brandsState,
    AsyncState<List<ProductModel>>? modelsState,
    AsyncState<List<ConditionOption>>? conditionsState,
    AsyncState<double>? priceEstimateState,
  }) {
    return RecycleCreateState(
      currentStep: currentStep ?? this.currentStep,
      formData: formData ?? this.formData,
      createOrderState: createOrderState ?? this.createOrderState,
      categoriesState: categoriesState ?? this.categoriesState,
      brandsState: brandsState ?? this.brandsState,
      modelsState: modelsState ?? this.modelsState,
      conditionsState: conditionsState ?? this.conditionsState,
      priceEstimateState: priceEstimateState ?? this.priceEstimateState,
    );
  }
}

/// 回收订单创建状态管理器
class RecycleCreateNotifier extends StateNotifier<RecycleCreateState> {

  RecycleCreateNotifier(this._apiService) : super(const RecycleCreateState());
  final RecycleApiService _apiService;

  /// 设置当前步骤
  void setCurrentStep(int step) {
    state = state.copyWith(currentStep: step);
  }

  /// 设置分类ID
  void setCategoryId(String categoryId) {
    state = state.copyWith(
      formData: state.formData.copyWith(categoryId: categoryId),
    );
    // 分类改变时，清空品牌和型号选择
    setBrandId(null);
    setModelId(null);
    // 加载该分类下的品牌
    loadBrands(categoryId);
  }

  /// 设置品牌ID
  void setBrandId(String? brandId) {
    state = state.copyWith(
      formData: state.formData.copyWith(brandId: brandId),
    );
    // 品牌改变时，清空型号选择
    setModelId(null);
    // 如果选择了品牌，加载该品牌下的型号
    if (brandId != null) {
      loadModels(brandId);
    }
  }

  /// 设置型号ID
  void setModelId(String? modelId) {
    state = state.copyWith(
      formData: state.formData.copyWith(modelId: modelId),
    );
    // 型号改变时，重新估价
    if (modelId != null) {
      estimatePrice();
    }
  }

  /// 设置产品信息
  void setProductInfo(String? name, String? description) {
    state = state.copyWith(
      formData: state.formData.copyWith(
        customProductName: name,
        productDescription: description,
      ),
    );
  }

  /// 设置成色ID
  void setConditionId(String? conditionId) {
    state = state.copyWith(
      formData: state.formData.copyWith(conditionId: conditionId),
    );
    // 成色改变时，重新估价
    estimatePrice();
  }

  /// 设置图片文件
  void setImageFiles(List<String> imageFiles) {
    state = state.copyWith(
      formData: state.formData.copyWith(imageFiles: imageFiles),
    );
  }

  /// 设置联系信息
  void setContactInfo(String? name, String? phone, String? address) {
    state = state.copyWith(
      formData: state.formData.copyWith(
        contactName: name,
        contactPhone: phone,
        pickupAddress: address,
      ),
    );
  }

  /// 加载分类列表
  Future<void> loadCategories() async {
    state = state.copyWith(categoriesState: const AsyncState.loading());

    try {
      final response = await _apiService.getRecycleCategories();

      if (response.isSuccess && response.data != null) {
        state = state.copyWith(
          categoriesState: AsyncState.success(response.data!),
        );
      } else {
        state = state.copyWith(
          categoriesState: AsyncState.error(response.message ?? '加载分类失败'),
        );
      }
    } catch (e) {
      state = state.copyWith(
        categoriesState: AsyncState.error(e.toString()),
      );
    }
  }

  /// 加载品牌列表
  Future<void> loadBrands(String categoryId) async {
    state = state.copyWith(brandsState: const AsyncState.loading());

    try {
      final response = await _apiService.getBrandsByCategory(categoryId);

      if (response.isSuccess && response.data != null) {
        state = state.copyWith(
          brandsState: AsyncState.success(response.data!),
        );
      } else {
        state = state.copyWith(
          brandsState: AsyncState.error(response.message ?? '加载品牌失败'),
        );
      }
    } catch (e) {
      state = state.copyWith(
        brandsState: AsyncState.error(e.toString()),
      );
    }
  }

  /// 加载型号列表
  Future<void> loadModels(String brandId) async {
    state = state.copyWith(modelsState: const AsyncState.loading());

    try {
      final response = await _apiService.getModelsByBrand(brandId);

      if (response.isSuccess && response.data != null) {
        state = state.copyWith(
          modelsState: AsyncState.success(response.data!),
        );
      } else {
        state = state.copyWith(
          modelsState: AsyncState.error(response.message ?? '加载型号失败'),
        );
      }
    } catch (e) {
      state = state.copyWith(
        modelsState: AsyncState.error(e.toString()),
      );
    }
  }

  /// 加载成色选项
  Future<void> loadConditions() async {
    state = state.copyWith(conditionsState: const AsyncState.loading());

    try {
      final response = await _apiService.getConditionOptions();

      if (response.isSuccess && response.data != null) {
        state = state.copyWith(
          conditionsState: AsyncState.success(response.data!),
        );
      } else {
        state = state.copyWith(
          conditionsState: AsyncState.error(response.message ?? '加载成色选项失败'),
        );
      }
    } catch (e) {
      state = state.copyWith(
        conditionsState: AsyncState.error(e.toString()),
      );
    }
  }

  /// 估算价格
  Future<void> estimatePrice() async {
    if (state.formData.modelId == null || state.formData.conditionId == null) {
      return;
    }

    state = state.copyWith(priceEstimateState: const AsyncState.loading());

    try {
      final response = await _apiService.estimatePrice(
        modelId: state.formData.modelId!,
        conditionId: state.formData.conditionId!,
      );

      if (response.isSuccess && response.data != null) {
        final estimatedPrice = response.data!;
        state = state.copyWith(
          priceEstimateState: AsyncState.success(estimatedPrice),
          formData: state.formData.copyWith(expectedPrice: estimatedPrice),
        );
      } else {
        state = state.copyWith(
          priceEstimateState: AsyncState.error(response.message ?? '价格估算失败'),
        );
      }
    } catch (e) {
      state = state.copyWith(
        priceEstimateState: AsyncState.error(e.toString()),
      );
    }
  }

  /// 创建回收订单
  Future<bool> createOrder() async {
    if (!state.formData.isValid) {
      state = state.copyWith(
        createOrderState: const AsyncState.error('订单信息不完整'),
      );
      return false;
    }

    state = state.copyWith(createOrderState: const AsyncState.loading());

    try {
      final request = state.formData.toCreateRequest();
      final response = await _apiService.createRecycleOrder(request);

      if (response.isSuccess && response.data != null) {
        state = state.copyWith(
          createOrderState: AsyncState.success(response.data!),
        );
        return true;
      } else {
        state = state.copyWith(
          createOrderState: AsyncState.error(response.message ?? '订单创建失败'),
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        createOrderState: AsyncState.error(e.toString()),
      );
      return false;
    }
  }

  /// 重置状态
  void reset() {
    state = const RecycleCreateState();
  }
}

/// 回收订单创建状态管理器提供者
final recycleCreateProvider =
    StateNotifierProvider<RecycleCreateNotifier, RecycleCreateState>((ref) {
  final apiService = RecycleApiService();
  return RecycleCreateNotifier(apiService);
});
