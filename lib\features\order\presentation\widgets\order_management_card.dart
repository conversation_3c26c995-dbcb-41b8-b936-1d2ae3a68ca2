import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/services.dart';

import 'package:soko/features/order/domain/entities/order.dart';
import 'package:soko/shared/presentation/widgets/status_badge.dart';
import 'package:soko/core/enums/app_enums.dart';

/// 订单管理卡片组件
class OrderManagementCard extends StatelessWidget {
  const OrderManagementCard({
    super.key,
    required this.order,
    this.isSelectionMode = false,
    this.isSelected = false,
    this.onTap,
    this.onSelectionChanged,
    this.onStatusChanged,
  });

  final Order order;
  final bool isSelectionMode;
  final bool isSelected;
  final VoidCallback? onTap;
  final ValueChanged<bool>? onSelectionChanged;
  final ValueChanged<OrderStatus>? onStatusChanged;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: isSelected ? Border.all(
          color: Theme.of(context).primaryColor,
          width: 2,
        ) : null,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 订单头部信息
              _buildOrderHeader(context),
              SizedBox(height: 12.h),
              
              // 订单基本信息
              _buildOrderInfo(context),
              SizedBox(height: 12.h),
              
              // 收货地址
              _buildShippingAddress(context),
              SizedBox(height: 12.h),
              
              // 订单金额和操作按钮
              _buildOrderFooter(context),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建订单头部
  Widget _buildOrderHeader(BuildContext context) {
    return Row(
      children: [
        // 选择框
        if (isSelectionMode)
          Padding(
            padding: EdgeInsets.only(right: 12.w),
            child: Checkbox(
              value: isSelected,
              onChanged: (value) => onSelectionChanged?.call(value ?? false),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4.r),
              ),
            ),
          ),
        
        // 订单号
        Expanded(
          child: Row(
            children: [
              Text(
                '订单号：${order.orderNo}',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
              SizedBox(width: 8.w),
              GestureDetector(
                onTap: () => _copyOrderNo(context),
                child: Icon(
                  Icons.copy,
                  size: 16.w,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        
        // 订单状态
        StatusBadge(
          status: _getStatusText(order.orderStatus),
          type: _getStatusBadgeType(order.orderStatus),
        ),
      ],
    );
  }

  /// 构建订单信息
  Widget _buildOrderInfo(BuildContext context) {
    return Column(
      children: [
        _buildInfoRow('下单时间', _formatDateTime(order.orderTime)),
        if (order.payTime != null)
          _buildInfoRow('付款时间', _formatDateTime(order.payTime!)),
        if (order.shipTime != null)
          _buildInfoRow('发货时间', _formatDateTime(order.shipTime!)),
        if (order.trackingNo != null)
          _buildInfoRow('物流单号', order.trackingNo!),
      ],
    );
  }

  /// 构建收货地址
  Widget _buildShippingAddress(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.location_on,
                size: 16.w,
                color: Colors.grey[600],
              ),
              SizedBox(width: 4.w),
              Text(
                '收货地址',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          SizedBox(height: 6.h),
          Text(
            '${order.receiverName} ${order.receiverPhone}',
            style: TextStyle(
              fontSize: 13.sp,
              fontWeight: FontWeight.w500,
              color: Colors.grey[800],
            ),
          ),
          SizedBox(height: 2.h),
          Text(
            '${order.receiverProvince}${order.receiverCity}${order.receiverDistrict}${order.receiverAddress}',
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建订单底部
  Widget _buildOrderFooter(BuildContext context) {
    return Row(
      children: [
        // 订单金额
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '订单金额',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.grey[600],
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                '¥${order.totalAmount.toStringAsFixed(2)}',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
        ),
        
        // 操作按钮
        if (!isSelectionMode) _buildActionButtons(context),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons(BuildContext context) {
    final buttons = <Widget>[];
    
    switch (order.orderStatus) {
      case OrderStatus.pending:
        buttons.addAll([
          _buildActionButton('取消', Colors.grey, () => _cancelOrder(context)),
          SizedBox(width: 8.w),
          _buildActionButton('付款', Theme.of(context).primaryColor, () => _payOrder(context)),
        ]);
        break;
      case OrderStatus.paid:
        buttons.addAll([
          _buildActionButton('发货', Theme.of(context).primaryColor, () => _shipOrder(context)),
        ]);
        break;
      case OrderStatus.shipped:
        buttons.addAll([
          _buildActionButton('查看物流', Colors.blue, () => _viewLogistics(context)),
        ]);
        break;
      default:
        break;
    }
    
    return Row(children: buttons);
  }

  /// 构建操作按钮
  Widget _buildActionButton(String text, Color color, VoidCallback onPressed) {
    return OutlinedButton(
      onPressed: onPressed,
      style: OutlinedButton.styleFrom(
        foregroundColor: color,
        side: BorderSide(color: color),
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        minimumSize: Size(0, 32.h),
      ),
      child: Text(
        text,
        style: TextStyle(fontSize: 12.sp),
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 4.h),
      child: Row(
        children: [
          SizedBox(
            width: 80.w,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey[800],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 复制订单号
  void _copyOrderNo(BuildContext context) {
    Clipboard.setData(ClipboardData(text: order.orderNo));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('订单号已复制'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// 取消订单
  void _cancelOrder(BuildContext context) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('取消订单'),
        content: const Text('确定要取消这个订单吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              onStatusChanged?.call(OrderStatus.cancelled);
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 付款订单
  void _payOrder(BuildContext context) {
    onStatusChanged?.call(OrderStatus.paid);
  }

  /// 发货订单
  void _shipOrder(BuildContext context) {
    onStatusChanged?.call(OrderStatus.shipped);
  }

  /// 查看物流
  void _viewLogistics(BuildContext context) {
    // 导航到物流页面
  }

  /// 获取状态文本
  String _getStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return '待付款';
      case OrderStatus.paid:
        return '待发货';
      case OrderStatus.shipped:
        return '已发货';
      case OrderStatus.completed:
        return '已完成';
      case OrderStatus.cancelled:
        return '已取消';
      default:
        return '未知';
    }
  }

  /// 获取状态徽章类型
  StatusBadgeType _getStatusBadgeType(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return StatusBadgeType.warning;
      case OrderStatus.paid:
        return StatusBadgeType.primary;
      case OrderStatus.shipped:
        return StatusBadgeType.info;
      case OrderStatus.completed:
        return StatusBadgeType.success;
      case OrderStatus.cancelled:
        return StatusBadgeType.error;
      default:
        return StatusBadgeType.secondary;
    }
  }

  /// 格式化日期时间
  String _formatDateTime(int timestamp) {
    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return '${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
