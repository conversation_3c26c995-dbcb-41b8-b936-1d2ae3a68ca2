import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';

import 'package:soko/core/theme/app_colors.dart';
import 'package:soko/core/theme/app_text_styles.dart';
import 'package:soko/shared/presentation/widgets/loading_widget.dart';
import 'package:soko/features/cart/domain/entities/cart_item.dart';

/// 购物车商品卡片
class CartItemCard extends StatelessWidget {

  const CartItemCard({
    super.key,
    required this.item,
    this.isEditMode = false,
    this.onTap,
    this.onQuantityChanged,
    this.onSelectionChanged,
    this.onRemove,
  });
  final CartItem item;
  final bool isEditMode;
  final VoidCallback? onTap;
  final Function(int quantity)? onQuantityChanged;
  final VoidCallback? onSelectionChanged;
  final VoidCallback? onRemove;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: isEditMode ? null : onTap,
        borderRadius: BorderRadius.circular(8.r),
        child: Padding(
          padding: EdgeInsets.all(12.w),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 选择框
              Checkbox(
                value: item.selected,
                onChanged: (_) => onSelectionChanged?.call(),
                activeColor: AppColors.primary,
              ),
              SizedBox(width: 8.w),
              // 商品图片
              _buildProductImage(),
              SizedBox(width: 12.w),
              // 商品信息
              Expanded(
                child: _buildProductInfo(),
              ),
              // 编辑模式下的删除按钮
              if (isEditMode) _buildDeleteButton(),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建商品图片
  Widget _buildProductImage() {
    return Container(
      width: 80.w,
      height: 80.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6.r),
        color: AppColors.background,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(6.r),
        child: item.productImage != null
            ? CachedNetworkImage(
                imageUrl: item.productImage!,
                fit: BoxFit.cover,
                placeholder: (context, url) => const LoadingWidget(),
                errorWidget: (context, url, error) => ColoredBox(
                  color: AppColors.background,
                  child: Icon(
                    Icons.image_not_supported,
                    color: AppColors.textTertiary,
                    size: 24.sp,
                  ),
                ),
              )
            : ColoredBox(
                color: AppColors.background,
                child: Icon(
                  Icons.image_not_supported,
                  color: AppColors.textTertiary,
                  size: 24.sp,
                ),
              ),
      ),
    );
  }

  /// 构建商品信息
  Widget _buildProductInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 商品名称
        Text(
          item.productName,
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        SizedBox(height: 4.h),
        // SKU信息
        if (item.skuName != null) ...[
          Text(
            item.skuName!,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 8.h),
        ],
        // 价格信息
        Row(
          children: [
            Text(
              '¥${item.price.toStringAsFixed(2)}',
              style: AppTextStyles.bodyLarge.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (item.hasDiscount) ...[
              SizedBox(width: 8.w),
              Text(
                '¥${item.originalPrice!.toStringAsFixed(2)}',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textTertiary,
                  decoration: TextDecoration.lineThrough,
                ),
              ),
            ],
          ],
        ),
        SizedBox(height: 12.h),
        // 数量控制和状态
        Row(
          children: [
            // 库存状态
            if (!item.available) ...[
              Container(
                padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                decoration: BoxDecoration(
                  color: AppColors.error.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Text(
                  '缺货',
                  style: AppTextStyles.caption.copyWith(
                    color: AppColors.error,
                  ),
                ),
              ),
              const Spacer(),
            ] else ...[
              const Spacer(),
              // 数量控制器
              _buildQuantityController(),
            ],
          ],
        ),
      ],
    );
  }

  /// 构建数量控制器
  Widget _buildQuantityController() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.borderLight),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 减少按钮
          _buildQuantityButton(
            icon: Icons.remove,
            onTap: item.canDecrease
                ? () => onQuantityChanged?.call(item.quantity - 1)
                : null,
          ),
          // 数量显示
          Container(
            width: 40.w,
            height: 32.h,
            alignment: Alignment.center,
            decoration: const BoxDecoration(
              border: Border.symmetric(
                vertical: BorderSide(color: AppColors.borderLight),
              ),
            ),
            child: Text(
              item.quantity.toString(),
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          // 增加按钮
          _buildQuantityButton(
            icon: Icons.add,
            onTap: item.canIncrease
                ? () => onQuantityChanged?.call(item.quantity + 1)
                : null,
          ),
        ],
      ),
    );
  }

  /// 构建数量按钮
  Widget _buildQuantityButton({
    required IconData icon,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 32.w,
        height: 32.h,
        alignment: Alignment.center,
        child: Icon(
          icon,
          size: 16.sp,
          color: onTap != null ? AppColors.textPrimary : AppColors.textDisabled,
        ),
      ),
    );
  }

  /// 构建删除按钮
  Widget _buildDeleteButton() {
    return IconButton(
      onPressed: onRemove,
      icon: Icon(
        Icons.delete_outline,
        color: AppColors.error,
        size: 20.sp,
      ),
      constraints: BoxConstraints(
        minWidth: 32.w,
        minHeight: 32.w,
      ),
      padding: EdgeInsets.zero,
    );
  }
}
