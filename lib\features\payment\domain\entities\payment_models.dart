import 'package:json_annotation/json_annotation.dart';

part 'payment_models.g.dart';

/// 支付请求
@JsonSerializable()
class PaymentRequest {
  const PaymentRequest({
    required this.orderId,
    required this.amount,
    required this.subject,
    required this.body,
    required this.paymentMethod,
    this.notifyUrl,
    this.returnUrl,
    this.timeoutExpress,
    this.passbackParams,
  });

  factory PaymentRequest.fromJson(Map<String, dynamic> json) =>
      _$PaymentRequestFromJson(json);

  @Json<PERSON>ey(name: 'orderId')
  final String orderId;

  @JsonKey(name: 'amount')
  final double amount;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'subject')
  final String subject;

  @JsonKey(name: 'body')
  final String body;

  @Json<PERSON>ey(name: 'paymentMethod')
  final PaymentMethod paymentMethod;

  @Json<PERSON>ey(name: 'notifyUrl')
  final String? notifyUrl;

  @Json<PERSON>ey(name: 'returnUrl')
  final String? returnUrl;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'timeoutExpress')
  final String? timeoutExpress;

  @J<PERSON><PERSON>ey(name: 'passbackParams')
  final String? passbackParams;

  Map<String, dynamic> toJson() => _$PaymentRequestToJson(this);
}

/// 支付响应
@JsonSerializable()
class PaymentResponse {
  const PaymentResponse({
    required this.success,
    required this.paymentId,
    this.orderString,
    this.qrCode,
    this.redirectUrl,
    this.errorCode,
    this.errorMessage,
    this.tradeNo,
  });

  factory PaymentResponse.fromJson(Map<String, dynamic> json) =>
      _$PaymentResponseFromJson(json);

  @JsonKey(name: 'success')
  final bool success;

  @JsonKey(name: 'paymentId')
  final String paymentId;

  @JsonKey(name: 'orderString')
  final String? orderString;

  @JsonKey(name: 'qrCode')
  final String? qrCode;

  @JsonKey(name: 'redirectUrl')
  final String? redirectUrl;

  @JsonKey(name: 'errorCode')
  final String? errorCode;

  @JsonKey(name: 'errorMessage')
  final String? errorMessage;

  @JsonKey(name: 'tradeNo')
  final String? tradeNo;

  Map<String, dynamic> toJson() => _$PaymentResponseToJson(this);
}

/// 支付结果
@JsonSerializable()
class PaymentResult {
  const PaymentResult({
    required this.success,
    required this.paymentId,
    required this.orderId,
    this.tradeNo,
    this.totalAmount,
    this.receiptAmount,
    this.buyerPayAmount,
    this.pointAmount,
    this.invoiceAmount,
    this.gmtPayment,
    this.fundBillList,
    this.errorCode,
    this.errorMessage,
    this.memo,
  });

  factory PaymentResult.fromJson(Map<String, dynamic> json) =>
      _$PaymentResultFromJson(json);

  @JsonKey(name: 'success')
  final bool success;

  @JsonKey(name: 'paymentId')
  final String paymentId;

  @JsonKey(name: 'orderId')
  final String orderId;

  @JsonKey(name: 'tradeNo')
  final String? tradeNo;

  @JsonKey(name: 'totalAmount')
  final String? totalAmount;

  @JsonKey(name: 'receiptAmount')
  final String? receiptAmount;

  @JsonKey(name: 'buyerPayAmount')
  final String? buyerPayAmount;

  @JsonKey(name: 'pointAmount')
  final String? pointAmount;

  @JsonKey(name: 'invoiceAmount')
  final String? invoiceAmount;

  @JsonKey(name: 'gmtPayment')
  final String? gmtPayment;

  @JsonKey(name: 'fundBillList')
  final List<FundBill>? fundBillList;

  @JsonKey(name: 'errorCode')
  final String? errorCode;

  @JsonKey(name: 'errorMessage')
  final String? errorMessage;

  @JsonKey(name: 'memo')
  final String? memo;

  Map<String, dynamic> toJson() => _$PaymentResultToJson(this);
}

/// 资金明细
@JsonSerializable()
class FundBill {
  const FundBill({
    required this.fundChannel,
    required this.amount,
    this.realAmount,
  });

  factory FundBill.fromJson(Map<String, dynamic> json) =>
      _$FundBillFromJson(json);

  @JsonKey(name: 'fundChannel')
  final String fundChannel;

  @JsonKey(name: 'amount')
  final String amount;

  @JsonKey(name: 'realAmount')
  final String? realAmount;

  Map<String, dynamic> toJson() => _$FundBillToJson(this);
}

/// 支付记录
@JsonSerializable()
class PaymentRecord {
  const PaymentRecord({
    required this.id,
    required this.orderId,
    required this.paymentId,
    required this.amount,
    required this.paymentMethod,
    required this.status,
    required this.createTime,
    this.payTime,
    this.tradeNo,
    this.errorCode,
    this.errorMessage,
    this.notifyData,
  });

  factory PaymentRecord.fromJson(Map<String, dynamic> json) =>
      _$PaymentRecordFromJson(json);

  @JsonKey(name: 'id')
  final String id;

  @JsonKey(name: 'orderId')
  final String orderId;

  @JsonKey(name: 'paymentId')
  final String paymentId;

  @JsonKey(name: 'amount')
  final double amount;

  @JsonKey(name: 'paymentMethod')
  final PaymentMethod paymentMethod;

  @JsonKey(name: 'status')
  final PaymentStatus status;

  @JsonKey(name: 'createTime')
  final int createTime;

  @JsonKey(name: 'payTime')
  final int? payTime;

  @JsonKey(name: 'tradeNo')
  final String? tradeNo;

  @JsonKey(name: 'errorCode')
  final String? errorCode;

  @JsonKey(name: 'errorMessage')
  final String? errorMessage;

  @JsonKey(name: 'notifyData')
  final Map<String, dynamic>? notifyData;

  Map<String, dynamic> toJson() => _$PaymentRecordToJson(this);

  /// 是否支付成功
  bool get isSuccess => status == PaymentStatus.success;

  /// 是否支付失败
  bool get isFailed => status == PaymentStatus.failed;

  /// 是否支付中
  bool get isPending => status == PaymentStatus.pending;
}

/// 支付方式枚举
enum PaymentMethod {
  @JsonValue('alipay')
  alipay,
  @JsonValue('wechat')
  wechat,
  @JsonValue('bankCard')
  bankCard,
  @JsonValue('balance')
  balance,
}

/// 支付状态枚举
enum PaymentStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('success')
  success,
  @JsonValue('failed')
  failed,
  @JsonValue('cancelled')
  cancelled,
  @JsonValue('timeout')
  timeout,
}

/// 支付配置
class PaymentConfig {
  const PaymentConfig({
    required this.appId,
    required this.privateKey,
    required this.publicKey,
    required this.signType,
    required this.isSandbox,
    this.notifyUrl,
    this.returnUrl,
  });

  final String appId;
  final String privateKey;
  final String publicKey;
  final String signType;
  final bool isSandbox;
  final String? notifyUrl;
  final String? returnUrl;

  /// 支付宝沙盒配置
  static const PaymentConfig alipaySandbox = PaymentConfig(
    appId: '****************', // 沙盒应用ID
    privateKey: '''-----BEGIN RSA PRIVATE KEY-----
MIIEpAIBAAKCAQEA1234567890abcdef...
-----END RSA PRIVATE KEY-----''', // 应用私钥
    publicKey: '''-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8A...
-----END PUBLIC KEY-----''', // 支付宝公钥
    signType: 'RSA2',
    isSandbox: true,
    notifyUrl: 'https://your-domain.com/api/payment/notify',
    returnUrl: 'https://your-domain.com/payment/return',
  );
}
