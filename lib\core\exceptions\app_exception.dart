/// 应用异常基类
abstract class AppException implements Exception {

  const AppException({
    required this.message,
    this.code,
    this.data,
  });
  final String message;
  final String? code;
  final dynamic data;

  @override
  String toString() {
    return 'AppException(message: $message, code: $code)';
  }
}

/// 业务逻辑异常
class BusinessException extends AppException {
  const BusinessException({
    required super.message,
    super.code,
    super.data,
  });

  @override
  String toString() {
    return 'BusinessException(message: $message, code: $code)';
  }
}

/// 验证异常
class ValidationException extends AppException {

  const ValidationException({
    required super.message,
    required this.errors,
    super.code,
    super.data,
  });
  final Map<String, String> errors;

  @override
  String toString() {
    return 'ValidationException(message: $message, errors: $errors)';
  }
}

/// 认证异常
class AuthException extends AppException {
  const AuthException({
    required super.message,
    super.code,
    super.data,
  });

  @override
  String toString() {
    return 'AuthException(message: $message, code: $code)';
  }
}

/// 权限异常
class PermissionException extends AppException {
  const PermissionException({
    required super.message,
    super.code,
    super.data,
  });

  @override
  String toString() {
    return 'PermissionException(message: $message, code: $code)';
  }
}

/// 缓存异常
class CacheException extends AppException {
  const CacheException({
    required super.message,
    super.code,
    super.data,
  });

  @override
  String toString() {
    return 'CacheException(message: $message, code: $code)';
  }
}

/// 文件操作异常
class FileException extends AppException {
  const FileException({
    required super.message,
    super.code,
    super.data,
  });

  @override
  String toString() {
    return 'FileException(message: $message, code: $code)';
  }
}

/// 解析异常
class ParseException extends AppException {
  const ParseException({
    required super.message,
    super.code,
    super.data,
  });

  @override
  String toString() {
    return 'ParseException(message: $message, code: $code)';
  }
}

/// 超时异常
class TimeoutException extends AppException {
  const TimeoutException({
    required super.message,
    super.code,
    super.data,
  });

  @override
  String toString() {
    return 'TimeoutException(message: $message, code: $code)';
  }
}

/// 未知异常
class UnknownException extends AppException {
  const UnknownException({
    required super.message,
    super.code,
    super.data,
  });

  @override
  String toString() {
    return 'UnknownException(message: $message, code: $code)';
  }
}
