import 'package:freezed_annotation/freezed_annotation.dart';

part 'failures.freezed.dart';

/// 基础错误类型
@freezed
abstract class Failure with _$Failure {
  const factory Failure.server(String message) = ServerFailure;
  const factory Failure.network(String message) = NetworkFailure;
  const factory Failure.cache(String message) = CacheFailure;
  const factory Failure.validation(String message) = ValidationFailure;
  const factory Failure.permission(String message) = PermissionFailure;
  const factory Failure.unknown(String message) = UnknownFailure;
}

/// 扩展方法
extension FailureExtension on Failure {
  String get message => when(
    server: (msg) => msg,
    network: (msg) => msg,
    cache: (msg) => msg,
    validation: (msg) => msg,
    permission: (msg) => msg,
    unknown: (msg) => msg,
  );
}
