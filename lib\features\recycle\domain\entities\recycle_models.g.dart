// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recycle_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CategoryItem _$CategoryItemFromJson(Map<String, dynamic> json) => CategoryItem(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      icon: json['icon'] as String?,
      sort: (json['sort'] as num).toInt(),
      isActive: json['isActive'] as bool,
    );

Map<String, dynamic> _$CategoryItemToJson(CategoryItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'icon': instance.icon,
      'sort': instance.sort,
      'isActive': instance.isActive,
    };

ConditionOption _$ConditionOptionFromJson(Map<String, dynamic> json) =>
    ConditionOption(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      priceMultiplier: (json['priceMultiplier'] as num).toDouble(),
      sort: (json['sort'] as num).toInt(),
    );

Map<String, dynamic> _$ConditionOptionToJson(ConditionOption instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'priceMultiplier': instance.priceMultiplier,
      'sort': instance.sort,
    };

CreateRecycleOrderRequest _$CreateRecycleOrderRequestFromJson(
        Map<String, dynamic> json) =>
    CreateRecycleOrderRequest(
      productName: json['productName'] as String,
      productDesc: json['productDesc'] as String?,
      productModel: json['productModel'] as String?,
      productCategory: json['productCategory'] as String?,
      brandId: json['brandId'] as String?,
      modelId: json['modelId'] as String?,
      conditionId: json['conditionId'] as String?,
      condition: json['condition'] as String?,
      expectedPrice: (json['expectedPrice'] as num?)?.toDouble(),
      contactName: json['contactName'] as String?,
      contactPhone: json['contactPhone'] as String?,
      pickupAddress: json['pickupAddress'] as String?,
      imageFiles: (json['imageFiles'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      remark: json['remark'] as String?,
    );

Map<String, dynamic> _$CreateRecycleOrderRequestToJson(
        CreateRecycleOrderRequest instance) =>
    <String, dynamic>{
      'productName': instance.productName,
      'productDesc': instance.productDesc,
      'productModel': instance.productModel,
      'productCategory': instance.productCategory,
      'brandId': instance.brandId,
      'modelId': instance.modelId,
      'conditionId': instance.conditionId,
      'condition': instance.condition,
      'expectedPrice': instance.expectedPrice,
      'contactName': instance.contactName,
      'contactPhone': instance.contactPhone,
      'pickupAddress': instance.pickupAddress,
      'imageFiles': instance.imageFiles,
      'remark': instance.remark,
    };

BrandInfo _$BrandInfoFromJson(Map<String, dynamic> json) => BrandInfo(
      id: json['id'] as String,
      name: json['name'] as String,
      logo: json['logo'] as String?,
      categoryId: json['categoryId'] as String,
      isPopular: json['isPopular'] as bool,
      sort: (json['sort'] as num).toInt(),
    );

Map<String, dynamic> _$BrandInfoToJson(BrandInfo instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'logo': instance.logo,
      'categoryId': instance.categoryId,
      'isPopular': instance.isPopular,
      'sort': instance.sort,
    };

ProductModel _$ProductModelFromJson(Map<String, dynamic> json) => ProductModel(
      id: json['id'] as String,
      name: json['name'] as String,
      brandId: json['brandId'] as String,
      categoryId: json['categoryId'] as String,
      basePrice: (json['basePrice'] as num).toDouble(),
      specifications: json['specifications'] as Map<String, dynamic>?,
      isPopular: json['isPopular'] as bool,
      sort: (json['sort'] as num).toInt(),
    );

Map<String, dynamic> _$ProductModelToJson(ProductModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'brandId': instance.brandId,
      'categoryId': instance.categoryId,
      'basePrice': instance.basePrice,
      'specifications': instance.specifications,
      'isPopular': instance.isPopular,
      'sort': instance.sort,
    };

RecycleOrderFile _$RecycleOrderFileFromJson(Map<String, dynamic> json) =>
    RecycleOrderFile(
      id: json['id'] as String,
      url: json['url'] as String,
      fileName: json['fileName'] as String,
      fileSize: (json['fileSize'] as num).toInt(),
      fileType: json['fileType'] as String,
      isMain: json['isMain'] as bool,
      description: json['description'] as String?,
      uploadTime: (json['uploadTime'] as num).toInt(),
    );

Map<String, dynamic> _$RecycleOrderFileToJson(RecycleOrderFile instance) =>
    <String, dynamic>{
      'id': instance.id,
      'url': instance.url,
      'fileName': instance.fileName,
      'fileSize': instance.fileSize,
      'fileType': instance.fileType,
      'isMain': instance.isMain,
      'description': instance.description,
      'uploadTime': instance.uploadTime,
    };

OrderReview _$OrderReviewFromJson(Map<String, dynamic> json) => OrderReview(
      id: json['id'] as String,
      orderId: json['orderId'] as String,
      userId: json['userId'] as String,
      rating: (json['rating'] as num).toInt(),
      comment: json['comment'] as String?,
      images:
          (json['images'] as List<dynamic>?)?.map((e) => e as String).toList(),
      createTime: (json['createTime'] as num).toInt(),
    );

Map<String, dynamic> _$OrderReviewToJson(OrderReview instance) =>
    <String, dynamic>{
      'id': instance.id,
      'orderId': instance.orderId,
      'userId': instance.userId,
      'rating': instance.rating,
      'comment': instance.comment,
      'images': instance.images,
      'createTime': instance.createTime,
    };

LogisticsInfo _$LogisticsInfoFromJson(Map<String, dynamic> json) =>
    LogisticsInfo(
      id: json['id'] as String,
      orderId: json['orderId'] as String,
      trackingNumber: json['trackingNumber'] as String,
      courierCompany: json['courierCompany'] as String,
      courierCompanyCode: json['courierCompanyCode'] as String,
      status: json['status'] as String,
      statusDesc: json['statusDesc'] as String,
      createTime: (json['createTime'] as num).toInt(),
      updateTime: (json['updateTime'] as num).toInt(),
      courierPhone: json['courierPhone'] as String?,
      courierName: json['courierName'] as String?,
      estimatedDeliveryTime: (json['estimatedDeliveryTime'] as num?)?.toInt(),
      actualDeliveryTime: (json['actualDeliveryTime'] as num?)?.toInt(),
      tracks: (json['tracks'] as List<dynamic>?)
          ?.map((e) => LogisticsTrack.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$LogisticsInfoToJson(LogisticsInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'orderId': instance.orderId,
      'trackingNumber': instance.trackingNumber,
      'courierCompany': instance.courierCompany,
      'courierCompanyCode': instance.courierCompanyCode,
      'courierPhone': instance.courierPhone,
      'courierName': instance.courierName,
      'status': instance.status,
      'statusDesc': instance.statusDesc,
      'estimatedDeliveryTime': instance.estimatedDeliveryTime,
      'actualDeliveryTime': instance.actualDeliveryTime,
      'createTime': instance.createTime,
      'updateTime': instance.updateTime,
      'tracks': instance.tracks,
    };

LogisticsTrack _$LogisticsTrackFromJson(Map<String, dynamic> json) =>
    LogisticsTrack(
      id: json['id'] as String,
      logisticsId: json['logisticsId'] as String,
      status: json['status'] as String,
      statusDesc: json['statusDesc'] as String,
      location: json['location'] as String,
      timestamp: (json['timestamp'] as num).toInt(),
      description: json['description'] as String?,
      operatorName: json['operatorName'] as String?,
    );

Map<String, dynamic> _$LogisticsTrackToJson(LogisticsTrack instance) =>
    <String, dynamic>{
      'id': instance.id,
      'logisticsId': instance.logisticsId,
      'status': instance.status,
      'statusDesc': instance.statusDesc,
      'location': instance.location,
      'description': instance.description,
      'timestamp': instance.timestamp,
      'operatorName': instance.operatorName,
    };

PriceEvaluationRequest _$PriceEvaluationRequestFromJson(
        Map<String, dynamic> json) =>
    PriceEvaluationRequest(
      brandId: json['brandId'] as String,
      modelId: json['modelId'] as String,
      categoryId: json['categoryId'] as String,
      conditionId: json['conditionId'] as String,
      purchaseDate: (json['purchaseDate'] as num?)?.toInt(),
      originalPrice: (json['originalPrice'] as num?)?.toDouble(),
      accessories: (json['accessories'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      defects:
          (json['defects'] as List<dynamic>?)?.map((e) => e as String).toList(),
      images:
          (json['images'] as List<dynamic>?)?.map((e) => e as String).toList(),
      description: json['description'] as String?,
    );

Map<String, dynamic> _$PriceEvaluationRequestToJson(
        PriceEvaluationRequest instance) =>
    <String, dynamic>{
      'brandId': instance.brandId,
      'modelId': instance.modelId,
      'categoryId': instance.categoryId,
      'conditionId': instance.conditionId,
      'purchaseDate': instance.purchaseDate,
      'originalPrice': instance.originalPrice,
      'accessories': instance.accessories,
      'defects': instance.defects,
      'images': instance.images,
      'description': instance.description,
    };

PriceEvaluationResult _$PriceEvaluationResultFromJson(
        Map<String, dynamic> json) =>
    PriceEvaluationResult(
      id: json['id'] as String,
      requestId: json['requestId'] as String,
      estimatedPrice: (json['estimatedPrice'] as num).toDouble(),
      priceRange:
          PriceRange.fromJson(json['priceRange'] as Map<String, dynamic>),
      confidence: (json['confidence'] as num).toDouble(),
      factors: (json['factors'] as List<dynamic>)
          .map((e) => PriceFactor.fromJson(e as Map<String, dynamic>))
          .toList(),
      marketTrend: json['marketTrend'] == null
          ? null
          : MarketTrend.fromJson(json['marketTrend'] as Map<String, dynamic>),
      recommendations: (json['recommendations'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      createTime: (json['createTime'] as num).toInt(),
      validUntil: (json['validUntil'] as num?)?.toInt(),
    );

Map<String, dynamic> _$PriceEvaluationResultToJson(
        PriceEvaluationResult instance) =>
    <String, dynamic>{
      'id': instance.id,
      'requestId': instance.requestId,
      'estimatedPrice': instance.estimatedPrice,
      'priceRange': instance.priceRange,
      'confidence': instance.confidence,
      'factors': instance.factors,
      'marketTrend': instance.marketTrend,
      'recommendations': instance.recommendations,
      'createTime': instance.createTime,
      'validUntil': instance.validUntil,
    };

PriceRange _$PriceRangeFromJson(Map<String, dynamic> json) => PriceRange(
      minPrice: (json['minPrice'] as num).toDouble(),
      maxPrice: (json['maxPrice'] as num).toDouble(),
    );

Map<String, dynamic> _$PriceRangeToJson(PriceRange instance) =>
    <String, dynamic>{
      'minPrice': instance.minPrice,
      'maxPrice': instance.maxPrice,
    };

PriceFactor _$PriceFactorFromJson(Map<String, dynamic> json) => PriceFactor(
      name: json['name'] as String,
      impact: (json['impact'] as num).toDouble(),
      description: json['description'] as String,
      weight: (json['weight'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$PriceFactorToJson(PriceFactor instance) =>
    <String, dynamic>{
      'name': instance.name,
      'impact': instance.impact,
      'description': instance.description,
      'weight': instance.weight,
    };

MarketTrend _$MarketTrendFromJson(Map<String, dynamic> json) => MarketTrend(
      trend: json['trend'] as String,
      percentage: (json['percentage'] as num).toDouble(),
      description: json['description'] as String,
      period: json['period'] as String?,
    );

Map<String, dynamic> _$MarketTrendToJson(MarketTrend instance) =>
    <String, dynamic>{
      'trend': instance.trend,
      'percentage': instance.percentage,
      'description': instance.description,
      'period': instance.period,
    };

PriceTrendData _$PriceTrendDataFromJson(Map<String, dynamic> json) =>
    PriceTrendData(
      date: (json['date'] as num).toInt(),
      price: (json['price'] as num).toDouble(),
      volume: (json['volume'] as num).toInt(),
    );

Map<String, dynamic> _$PriceTrendDataToJson(PriceTrendData instance) =>
    <String, dynamic>{
      'date': instance.date,
      'price': instance.price,
      'volume': instance.volume,
    };
