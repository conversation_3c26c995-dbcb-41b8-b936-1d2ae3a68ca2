import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';

/// 优化的图片加载组件
class OptimizedImageWidget extends StatelessWidget {
  const OptimizedImageWidget({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.placeholder,
    this.errorWidget,
    this.fadeInDuration = const Duration(milliseconds: 300),
    this.memCacheWidth,
    this.memCacheHeight,
    this.enableHeroAnimation = false,
    this.heroTag,
  });

  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final Widget? placeholder;
  final Widget? errorWidget;
  final Duration fadeInDuration;
  final int? memCacheWidth;
  final int? memCacheHeight;
  final bool enableHeroAnimation;
  final String? heroTag;

  @override
  Widget build(BuildContext context) {
    Widget imageWidget = _buildImageWidget();

    // 添加圆角
    if (borderRadius != null) {
      imageWidget = ClipRRect(
        borderRadius: borderRadius!,
        child: imageWidget,
      );
    }

    // 添加Hero动画
    if (enableHeroAnimation && heroTag != null) {
      imageWidget = Hero(
        tag: heroTag!,
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  /// 构建图片组件
  Widget _buildImageWidget() {
    if (imageUrl.isEmpty) {
      return _buildErrorWidget();
    }

    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      fadeInDuration: fadeInDuration,
      memCacheWidth: memCacheWidth,
      memCacheHeight: memCacheHeight,
      placeholder: (context, url) => _buildPlaceholderWidget(),
      errorWidget: (context, url, error) => _buildErrorWidget(),
      imageBuilder: (context, imageProvider) => Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: imageProvider,
            fit: fit,
          ),
        ),
      ),
    );
  }

  /// 构建占位符组件
  Widget _buildPlaceholderWidget() {
    if (placeholder != null) {
      return placeholder!;
    }

    return Container(
      width: width,
      height: height,
      color: Colors.grey[200],
      child: Center(
        child: SizedBox(
          width: 24.w,
          height: 24.w,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              Colors.grey[400]!,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建错误组件
  Widget _buildErrorWidget() {
    if (errorWidget != null) {
      return errorWidget!;
    }

    return Container(
      width: width,
      height: height,
      color: Colors.grey[200],
      child: Icon(
        Icons.broken_image,
        size: 32.w,
        color: Colors.grey[400],
      ),
    );
  }
}

/// 图片预览组件
class ImagePreviewWidget extends StatelessWidget {
  const ImagePreviewWidget({
    super.key,
    required this.images,
    this.initialIndex = 0,
    this.heroTag,
  });

  final List<String> images;
  final int initialIndex;
  final String? heroTag;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            onPressed: () => _shareImage(context),
            icon: const Icon(Icons.share),
          ),
          IconButton(
            onPressed: () => _downloadImage(context),
            icon: const Icon(Icons.download),
          ),
        ],
      ),
      body: PageView.builder(
        controller: PageController(initialPage: initialIndex),
        itemCount: images.length,
        itemBuilder: (context, index) {
          return Center(
            child: InteractiveViewer(
              minScale: 0.5,
              maxScale: 3.0,
              child: OptimizedImageWidget(
                imageUrl: images[index],
                fit: BoxFit.contain,
                enableHeroAnimation: index == initialIndex && heroTag != null,
                heroTag: heroTag,
              ),
            ),
          );
        },
      ),
      bottomNavigationBar: images.length > 1
          ? Container(
              padding: EdgeInsets.all(16.w),
              color: Colors.black.withValues(alpha: 0.5),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: images.asMap().entries.map((entry) {
                  return Container(
                    width: 8.w,
                    height: 8.w,
                    margin: EdgeInsets.symmetric(horizontal: 4.w),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: entry.key == initialIndex
                          ? Colors.white
                          : Colors.white.withValues(alpha: 0.5),
                    ),
                  );
                }).toList(),
              ),
            )
          : null,
    );
  }

  /// 分享图片
  void _shareImage(BuildContext context) {
    // TODO: 实现图片分享功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('分享功能开发中')),
    );
  }

  /// 下载图片
  void _downloadImage(BuildContext context) {
    // TODO: 实现图片下载功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('下载功能开发中')),
    );
  }
}

/// 图片网格组件
class ImageGridWidget extends StatelessWidget {
  const ImageGridWidget({
    super.key,
    required this.images,
    this.crossAxisCount = 3,
    this.aspectRatio = 1.0,
    this.spacing = 8.0,
    this.onImageTap,
    this.maxImages = 9,
    this.showMoreText = '更多',
  });

  final List<String> images;
  final int crossAxisCount;
  final double aspectRatio;
  final double spacing;
  final Function(int index, List<String> images)? onImageTap;
  final int maxImages;
  final String showMoreText;

  @override
  Widget build(BuildContext context) {
    final displayImages = images.take(maxImages).toList();
    final hasMore = images.length > maxImages;

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        aspectRatio: aspectRatio,
        crossAxisSpacing: spacing,
        mainAxisSpacing: spacing,
      ),
      itemCount: displayImages.length,
      itemBuilder: (context, index) {
        final isLastItem = index == displayImages.length - 1;
        final showMoreOverlay = hasMore && isLastItem;

        return GestureDetector(
          onTap: () => onImageTap?.call(index, images),
          child: Stack(
            children: [
              OptimizedImageWidget(
                imageUrl: displayImages[index],
                width: double.infinity,
                height: double.infinity,
                borderRadius: BorderRadius.circular(8.r),
                heroTag: 'image_$index',
              ),
              
              // 更多图片遮罩
              if (showMoreOverlay)
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.6),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.add,
                            color: Colors.white,
                            size: 24.w,
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            '+${images.length - maxImages}',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}
