import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/utils/validator_utils.dart';
import 'package:soko/shared/presentation/widgets/custom_button.dart';
import 'package:soko/shared/presentation/widgets/custom_text_field.dart';
import 'package:soko/features/auth/presentation/providers/auth_provider.dart';
import 'package:soko/features/auth/presentation/widgets/sms_code_button.dart';

/// 忘记密码页面
class ForgotPasswordPage extends ConsumerStatefulWidget {
  const ForgotPasswordPage({super.key});

  @override
  ConsumerState<ForgotPasswordPage> createState() => _ForgotPasswordPageState();
}

class _ForgotPasswordPageState extends ConsumerState<ForgotPasswordPage> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _smsCodeController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  @override
  void dispose() {
    _phoneController.dispose();
    _smsCodeController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: const Text(
          '重置密码',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Colors.black,
            size: 20,
          ),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题
                const Text(
                  '重置密码',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  '请输入手机号和验证码来重置密码',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 32),

                // 手机号输入框
                PhoneTextField(
                  controller: _phoneController,
                  label: '手机号',
                  hintText: '请输入手机号',
                ),
                const SizedBox(height: 16),

                // 验证码输入框
                Row(
                  children: [
                    Expanded(
                      child: CodeTextField(
                        controller: _smsCodeController,
                        label: '验证码',
                        hintText: '请输入验证码',
                      ),
                    ),
                    const SizedBox(width: 12),
                    Padding(
                      padding: const EdgeInsets.only(top: 24),
                      child: SmsCodeButton(
                        phone: _phoneController.text,
                        type: 'reset_password',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // 新密码输入框
                PasswordTextField(
                  controller: _newPasswordController,
                  label: '新密码',
                  hintText: '请输入新密码（6-20位）',
                ),
                const SizedBox(height: 16),

                // 确认新密码输入框
                PasswordTextField(
                  controller: _confirmPasswordController,
                  label: '确认新密码',
                  hintText: '请再次输入新密码',
                ),
                const SizedBox(height: 32),

                // 重置密码按钮
                Consumer(
                  builder: (context, ref, child) {
                    final passwordState = ref.watch(passwordProvider);
                    
                    return PrimaryButton(
                      text: '重置密码',
                      width: double.infinity,
                      isLoading: passwordState.isLoading,
                      onPressed: _handleResetPassword,
                    );
                  },
                ),
                const SizedBox(height: 24),

                // 返回登录链接
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text(
                      '想起密码了？',
                      style: TextStyle(color: Colors.grey),
                    ),
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text(
                        '返回登录',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 处理重置密码
  void _handleResetPassword() {
    if (!_validateForm()) return;

    ref.read(passwordProvider.notifier).resetPassword(
      phone: _phoneController.text.trim(),
      newPassword: _newPasswordController.text.trim(),
      smsCode: _smsCodeController.text.trim(),
    );

    // 监听重置结果
    ref.listen(passwordProvider, (previous, next) {
      next.when(
        idle: () {},
        loading: () {},
        success: (_) {
          // 重置成功
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('密码重置成功，请使用新密码登录')),
          );
          Navigator.of(context).pop();
        },
        error: (message) {
          // 重置失败
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(message)),
          );
        },
      );
    });
  }

  /// 验证表单
  bool _validateForm() {
    final phone = _phoneController.text.trim();
    final smsCode = _smsCodeController.text.trim();
    final newPassword = _newPasswordController.text.trim();
    final confirmPassword = _confirmPasswordController.text.trim();

    if (phone.isEmpty) {
      _showError('请输入手机号');
      return false;
    }

    if (!ValidatorUtils.isValidPhone(phone)) {
      _showError('请输入正确的手机号');
      return false;
    }

    if (smsCode.isEmpty) {
      _showError('请输入验证码');
      return false;
    }

    if (newPassword.isEmpty) {
      _showError('请输入新密码');
      return false;
    }

    if (!ValidatorUtils.isValidPassword(newPassword)) {
      _showError('密码长度应为6-20位');
      return false;
    }

    if (confirmPassword.isEmpty) {
      _showError('请确认新密码');
      return false;
    }

    if (newPassword != confirmPassword) {
      _showError('两次输入的密码不一致');
      return false;
    }

    return true;
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }
}
