import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/core/theme/app_colors.dart';
import 'package:soko/core/theme/app_text_styles.dart';

/// 加载组件
class LoadingWidget extends StatelessWidget {

  const LoadingWidget({
    super.key,
    this.message,
    this.color,
    this.size,
    this.showMessage = true,
  });
  final String? message;
  final Color? color;
  final double? size;
  final bool showMessage;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: size ?? 32.w,
            height: size ?? 32.w,
            child: CircularProgressIndicator(
              strokeWidth: 3,
              valueColor: AlwaysStoppedAnimation<Color>(
                color ?? AppColors.primary,
              ),
            ),
          ),
          if (showMessage && message != null) ...[
            SizedBox(height: 16.h),
            Text(
              message!,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

/// 页面加载组件
class PageLoadingWidget extends StatelessWidget {

  const PageLoadingWidget({
    super.key,
    this.message,
  });
  final String? message;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: LoadingWidget(
        message: message ?? '加载中...',
      ),
    );
  }
}

/// 内容加载组件
class ContentLoadingWidget extends StatelessWidget {

  const ContentLoadingWidget({
    super.key,
    this.message,
    this.height,
  });
  final String? message;
  final double? height;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height ?? 200.h,
      alignment: Alignment.center,
      child: LoadingWidget(
        message: message ?? '加载中...',
        size: 24.w,
      ),
    );
  }
}

/// 按钮加载组件
class ButtonLoadingWidget extends StatelessWidget {

  const ButtonLoadingWidget({
    super.key,
    this.color,
    this.size,
  });
  final Color? color;
  final double? size;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size ?? 16.w,
      height: size ?? 16.w,
      child: CircularProgressIndicator(
        strokeWidth: 2,
        valueColor: AlwaysStoppedAnimation<Color>(
          color ?? Colors.white,
        ),
      ),
    );
  }
}

/// 列表加载更多组件
class LoadMoreWidget extends StatelessWidget {

  const LoadMoreWidget({
    super.key,
    required this.isLoading,
    required this.hasMore,
    this.onLoadMore,
    this.loadingText,
    this.noMoreText,
  });
  final bool isLoading;
  final bool hasMore;
  final VoidCallback? onLoadMore;
  final String? loadingText;
  final String? noMoreText;

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Container(
        padding: EdgeInsets.all(16.w),
        alignment: Alignment.center,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 16.w,
              height: 16.w,
              child: const CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
            ),
            SizedBox(width: 8.w),
            Text(
              loadingText ?? '加载中...',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textTertiary,
              ),
            ),
          ],
        ),
      );
    }

    if (!hasMore) {
      return Container(
        padding: EdgeInsets.all(16.w),
        alignment: Alignment.center,
        child: Text(
          noMoreText ?? '没有更多数据了',
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.textTertiary,
          ),
        ),
      );
    }

    return GestureDetector(
      onTap: onLoadMore,
      child: Container(
        padding: EdgeInsets.all(16.w),
        alignment: Alignment.center,
        child: Text(
          '点击加载更多',
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.primary,
          ),
        ),
      ),
    );
  }
}

/// 骨架屏加载组件
class SkeletonWidget extends StatefulWidget {

  const SkeletonWidget({
    super.key,
    this.width,
    this.height,
    this.borderRadius,
  });
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;

  @override
  State<SkeletonWidget> createState() => _SkeletonWidgetState();
}

class _SkeletonWidgetState extends State<SkeletonWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.3, end: 1).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height ?? 16.h,
          decoration: BoxDecoration(
            color: AppColors.border.withOpacity(_animation.value),
            borderRadius: widget.borderRadius ?? BorderRadius.circular(4.r),
          ),
        );
      },
    );
  }
}

/// 骨架屏列表项
class SkeletonListItem extends StatelessWidget {
  const SkeletonListItem({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Row(
        children: [
          SkeletonWidget(
            width: 60.w,
            height: 60.w,
            borderRadius: BorderRadius.circular(8.r),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SkeletonWidget(
                  width: double.infinity,
                  height: 16.h,
                ),
                SizedBox(height: 8.h),
                SkeletonWidget(
                  width: 200.w,
                  height: 14.h,
                ),
                SizedBox(height: 8.h),
                SkeletonWidget(
                  width: 100.w,
                  height: 14.h,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
