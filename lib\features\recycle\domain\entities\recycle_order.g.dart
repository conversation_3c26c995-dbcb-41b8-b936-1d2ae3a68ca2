// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recycle_order.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RecycleOrder _$RecycleOrderFromJson(Map<String, dynamic> json) => RecycleOrder(
      id: json['id'] as String,
      userId: json['userId'] as String,
      userPhone: json['userPhone'] as String?,
      brandName: json['brandName'] as String,
      model: json['model'] as String,
      categoryName: json['categoryName'] as String,
      productDesc: json['productDesc'] as String?,
      conditionDescription: json['conditionDescription'] as String,
      estimatedPrice: (json['estimatedPrice'] as num).toDouble(),
      finalPrice: (json['finalPrice'] as num?)?.toDouble(),
      reviewedPrice: (json['reviewedPrice'] as num?)?.toDouble(),
      orderStatus: json['orderStatus'] as String,
      orderStatusDesc: json['orderStatusDesc'] as String,
      shippingInfo: json['shippingInfo'] as String?,
      buyerAddress: json['buyerAddress'] as String?,
      contactPerson: json['contactPerson'] as String?,
      createTime: (json['createTime'] as num).toInt(),
      updateTime: (json['updateTime'] as num).toInt(),
      mainImage: json['mainImage'] as String?,
      files: (json['files'] as List<dynamic>?)
          ?.map((e) => RecycleOrderFile.fromJson(e as Map<String, dynamic>))
          .toList(),
      reviews: (json['reviews'] as List<dynamic>?)
          ?.map((e) => OrderReview.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$RecycleOrderToJson(RecycleOrder instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'userPhone': instance.userPhone,
      'brandName': instance.brandName,
      'model': instance.model,
      'categoryName': instance.categoryName,
      'productDesc': instance.productDesc,
      'conditionDescription': instance.conditionDescription,
      'estimatedPrice': instance.estimatedPrice,
      'finalPrice': instance.finalPrice,
      'reviewedPrice': instance.reviewedPrice,
      'orderStatus': instance.orderStatus,
      'orderStatusDesc': instance.orderStatusDesc,
      'shippingInfo': instance.shippingInfo,
      'buyerAddress': instance.buyerAddress,
      'contactPerson': instance.contactPerson,
      'createTime': instance.createTime,
      'updateTime': instance.updateTime,
      'mainImage': instance.mainImage,
      'files': instance.files,
      'reviews': instance.reviews,
    };

RecycleOrderFile _$RecycleOrderFileFromJson(Map<String, dynamic> json) =>
    RecycleOrderFile(
      id: json['id'] as String,
      recyclingOrderId: json['recyclingOrderId'] as String,
      fileId: json['fileId'] as String,
      url: json['url'] as String,
      thumbnailUrl: json['thumbnailUrl'] as String?,
      sort: (json['sort'] as num).toInt(),
      isMain: json['isMain'] as bool,
    );

Map<String, dynamic> _$RecycleOrderFileToJson(RecycleOrderFile instance) =>
    <String, dynamic>{
      'id': instance.id,
      'recyclingOrderId': instance.recyclingOrderId,
      'fileId': instance.fileId,
      'url': instance.url,
      'thumbnailUrl': instance.thumbnailUrl,
      'sort': instance.sort,
      'isMain': instance.isMain,
    };

OrderReview _$OrderReviewFromJson(Map<String, dynamic> json) => OrderReview(
      id: json['id'] as String,
      orderId: json['orderId'] as String,
      reviewerId: json['reviewerId'] as String,
      reviewComments: json['reviewComments'] as String,
      createTime: (json['createTime'] as num).toInt(),
    );

Map<String, dynamic> _$OrderReviewToJson(OrderReview instance) =>
    <String, dynamic>{
      'id': instance.id,
      'orderId': instance.orderId,
      'reviewerId': instance.reviewerId,
      'reviewComments': instance.reviewComments,
      'createTime': instance.createTime,
    };

CreateRecycleOrderRequest _$CreateRecycleOrderRequestFromJson(
        Map<String, dynamic> json) =>
    CreateRecycleOrderRequest(
      productName: json['productName'] as String,
      productDesc: json['productDesc'] as String,
      productModel: json['productModel'] as String?,
      productCategory: json['productCategory'] as String,
      contactPerson: json['contactPerson'] as String,
      contactPhone: json['contactPhone'] as String,
      expectedPrice: (json['expectedPrice'] as num).toDouble(),
      condition: json['condition'] as String,
      imageFiles: (json['imageFiles'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$CreateRecycleOrderRequestToJson(
        CreateRecycleOrderRequest instance) =>
    <String, dynamic>{
      'productName': instance.productName,
      'productDesc': instance.productDesc,
      'productModel': instance.productModel,
      'productCategory': instance.productCategory,
      'contactPerson': instance.contactPerson,
      'contactPhone': instance.contactPhone,
      'expectedPrice': instance.expectedPrice,
      'condition': instance.condition,
      'imageFiles': instance.imageFiles,
    };

ShippingInfo _$ShippingInfoFromJson(Map<String, dynamic> json) => ShippingInfo(
      courierCompany: json['courierCompany'] as String,
      trackingNumber: json['trackingNumber'] as String,
      senderName: json['senderName'] as String,
      senderPhone: json['senderPhone'] as String,
      senderAddress: json['senderAddress'] as String,
    );

Map<String, dynamic> _$ShippingInfoToJson(ShippingInfo instance) =>
    <String, dynamic>{
      'courierCompany': instance.courierCompany,
      'trackingNumber': instance.trackingNumber,
      'senderName': instance.senderName,
      'senderPhone': instance.senderPhone,
      'senderAddress': instance.senderAddress,
    };
