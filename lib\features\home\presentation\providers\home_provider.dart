import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/state/base_notifier.dart';
import 'package:soko/core/state/base_state.dart';
import 'package:soko/features/product/data/datasources/product_api_service.dart';
import 'package:soko/features/product/domain/entities/product.dart';

/// 首页数据模型
class HomeData {

  const HomeData({
    required this.banners,
    required this.categories,
    required this.newProducts,
    required this.hotProducts,
    required this.recommendProducts,
  });
  final List<Banner> banners;
  final List<CategoryNode> categories;
  final List<Product> newProducts;
  final List<Product> hotProducts;
  final List<Product> recommendProducts;

  HomeData copyWith({
    List<Banner>? banners,
    List<CategoryNode>? categories,
    List<Product>? newProducts,
    List<Product>? hotProducts,
    List<Product>? recommendProducts,
  }) {
    return HomeData(
      banners: banners ?? this.banners,
      categories: categories ?? this.categories,
      newProducts: newProducts ?? this.newProducts,
      hotProducts: hotProducts ?? this.hotProducts,
      recommendProducts: recommendProducts ?? this.recommendProducts,
    );
  }
}

/// 首页状态提供者
final homeProvider = StateNotifierProvider<HomeNotifier, BaseState<HomeData>>((ref) {
  return HomeNotifier();
});

/// 轮播图状态提供者
final bannersProvider = StateNotifierProvider<BannersNotifier, BaseState<List<Banner>>>((ref) {
  return BannersNotifier();
});

/// 分类状态提供者
final categoriesProvider = StateNotifierProvider<CategoriesNotifier, BaseState<List<CategoryNode>>>((ref) {
  return CategoriesNotifier();
});

/// 新品状态提供者
final newProductsProvider = StateNotifierProvider<NewProductsNotifier, BaseState<List<Product>>>((ref) {
  return NewProductsNotifier();
});

/// 热门商品状态提供者
final hotProductsProvider = StateNotifierProvider<HotProductsNotifier, BaseState<List<Product>>>((ref) {
  return HotProductsNotifier();
});

/// 首页状态管理器
class HomeNotifier extends BaseNotifier<HomeData> {
  final ProductApiService _productApiService = ProductApiService();

  /// 加载首页数据
  Future<void> loadHomeData() async {
    await execute(() async {
      // 并行加载所有数据
      final results = await Future.wait([
        _productApiService.getBanners(),
        _productApiService.getCategoryTree(),
        _productApiService.getNewProducts(),
        _productApiService.getHotProducts(),
        _productApiService.getHotProducts(limit: 20), // 推荐商品暂时使用热门商品
      ]);

      final banners = results[0].isSuccess ? results[0].data! : <Banner>[];
      final categories = results[1].isSuccess ? results[1].data! : <CategoryNode>[];
      final newProducts = results[2].isSuccess ? results[2].data! : <Product>[];
      final hotProducts = results[3].isSuccess ? results[3].data! : <Product>[];
      final recommendProducts = results[4].isSuccess ? results[4].data! : <Product>[];

      return HomeData(
        banners: banners,
        categories: categories,
        newProducts: newProducts,
        hotProducts: hotProducts,
        recommendProducts: recommendProducts,
      );
    });
  }

  /// 刷新首页数据
  Future<void> refreshHomeData() async {
    await loadHomeData();
  }
}

/// 轮播图状态管理器
class BannersNotifier extends BaseNotifier<List<Banner>> {
  final ProductApiService _productApiService = ProductApiService();

  /// 获取轮播图
  Future<void> getBanners() async {
    await execute(() async {
      final response = await _productApiService.getBanners();
      if (response.isSuccess && response.data != null) {
        return response.data!;
      }
      throw Exception(response.message);
    });
  }
}

/// 分类状态管理器
class CategoriesNotifier extends BaseNotifier<List<CategoryNode>> {
  final ProductApiService _productApiService = ProductApiService();

  /// 获取分类列表
  Future<void> getCategories() async {
    await execute(() async {
      final response = await _productApiService.getCategoryTree();
      if (response.isSuccess && response.data != null) {
        return response.data!;
      }
      throw Exception(response.message);
    });
  }

  /// 获取ACG分类
  Future<void> getAcgCategories() async {
    await execute(() async {
      final response = await _productApiService.getAcgCategories();
      if (response.isSuccess && response.data != null) {
        return response.data!;
      }
      throw Exception(response.message);
    });
  }
}

/// 新品状态管理器
class NewProductsNotifier extends BaseNotifier<List<Product>> {
  final ProductApiService _productApiService = ProductApiService();

  /// 获取新品列表
  Future<void> getNewProducts({int limit = 10}) async {
    await execute(() async {
      final response = await _productApiService.getNewProducts(limit: limit);
      if (response.isSuccess && response.data != null) {
        return response.data!;
      }
      throw Exception(response.message);
    });
  }
}

/// 热门商品状态管理器
class HotProductsNotifier extends BaseNotifier<List<Product>> {
  final ProductApiService _productApiService = ProductApiService();

  /// 获取热门商品列表
  Future<void> getHotProducts({int limit = 10}) async {
    await execute(() async {
      final response = await _productApiService.getHotProducts(limit: limit);
      if (response.isSuccess && response.data != null) {
        return response.data!;
      }
      throw Exception(response.message);
    });
  }
}

/// 搜索关键词状态提供者
final searchKeywordsProvider = StateNotifierProvider<SearchKeywordsNotifier, List<String>>((ref) {
  return SearchKeywordsNotifier();
});

/// 搜索关键词状态管理器
class SearchKeywordsNotifier extends StateNotifier<List<String>> {
  SearchKeywordsNotifier() : super([]);

  /// 添加搜索关键词
  void addKeyword(String keyword) {
    if (keyword.trim().isEmpty) return;
    
    final trimmedKeyword = keyword.trim();
    final newKeywords = state.where((k) => k != trimmedKeyword).toList();
    newKeywords.insert(0, trimmedKeyword);
    
    // 最多保存10个搜索记录
    if (newKeywords.length > 10) {
      newKeywords.removeRange(10, newKeywords.length);
    }
    
    state = newKeywords;
  }

  /// 删除搜索关键词
  void removeKeyword(String keyword) {
    state = state.where((k) => k != keyword).toList();
  }

  /// 清空搜索记录
  void clearKeywords() {
    state = [];
  }

  /// 获取热门搜索关键词
  List<String> getHotKeywords() {
    return [
      '手办',
      '模型',
      '周边',
      '限定',
      '二手',
      'Figma',
      'Nendoroid',
      'PVC',
      '景品',
      '一番赏',
    ];
  }
}
