import 'package:freezed_annotation/freezed_annotation.dart';

import 'package:soko/core/enums/app_enums.dart';

part 'order_create_request.freezed.dart';
part 'order_create_request.g.dart';

/// 订单创建请求
@freezed
class OrderCreateRequest with _$OrderCreateRequest {
  const factory OrderCreateRequest({
    /// 购物车商品项列表
    required List<OrderCreateItem> items,
    
    /// 收货地址ID
    required String addressId,
    
    /// 支付方式
    required PaymentMethod paymentMethod,
    
    /// 优惠券ID（可选）
    String? couponId,
    
    /// 订单备注（可选）
    String? note,
    
    /// 配送方式
    @Default(DeliveryMethod.standard) DeliveryMethod deliveryMethod,
    
    /// 发票类型（可选）
    InvoiceType? invoiceType,
    
    /// 发票抬头（可选）
    String? invoiceTitle,
  }) = _OrderCreateRequest;

  factory OrderCreateRequest.fromJson(Map<String, dynamic> json) =>
      _$OrderCreateRequestFromJson(json);
}

/// 订单创建商品项
@freezed
class OrderCreateItem with _$OrderCreateItem {
  const factory OrderCreateItem({
    /// 商品ID
    required String productId,
    
    /// SKU ID
    required String skuId,
    
    /// 数量
    required int quantity,
    
    /// 单价
    required double price,
    
    /// 商品名称
    required String productName,
    
    /// SKU名称（规格）
    String? skuName,
    
    /// 商品图片
    String? productImage,
  }) = _OrderCreateItem;

  factory OrderCreateItem.fromJson(Map<String, dynamic> json) =>
      _$OrderCreateItemFromJson(json);
}

/// 配送方式枚举
enum DeliveryMethod {
  @JsonValue('standard')
  standard, // 标准配送
  
  @JsonValue('express')
  express, // 快速配送
  
  @JsonValue('pickup')
  pickup, // 自提
}

/// 发票类型枚举
enum InvoiceType {
  @JsonValue('none')
  none, // 不开发票
  
  @JsonValue('personal')
  personal, // 个人发票
  
  @JsonValue('company')
  company, // 企业发票
}

/// 配送方式扩展
extension DeliveryMethodExtension on DeliveryMethod {
  String get displayName {
    switch (this) {
      case DeliveryMethod.standard:
        return '标准配送';
      case DeliveryMethod.express:
        return '快速配送';
      case DeliveryMethod.pickup:
        return '门店自提';
    }
  }

  String get description {
    switch (this) {
      case DeliveryMethod.standard:
        return '3-5个工作日送达';
      case DeliveryMethod.express:
        return '1-2个工作日送达';
      case DeliveryMethod.pickup:
        return '到店自提，无配送费';
    }
  }

  double get fee {
    switch (this) {
      case DeliveryMethod.standard:
        return 0; // 免费配送
      case DeliveryMethod.express:
        return 10; // 快递费10元
      case DeliveryMethod.pickup:
        return 0; // 自提免费
    }
  }
}

/// 发票类型扩展
extension InvoiceTypeExtension on InvoiceType {
  String get displayName {
    switch (this) {
      case InvoiceType.none:
        return '不开发票';
      case InvoiceType.personal:
        return '个人发票';
      case InvoiceType.company:
        return '企业发票';
    }
  }
}
