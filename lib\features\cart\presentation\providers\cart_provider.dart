import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/features/cart/data/datasources/cart_api_service.dart';
import 'package:soko/features/cart/domain/entities/cart_item.dart';
import 'package:soko/features/cart/domain/entities/coupon.dart';
import 'package:soko/features/cart/domain/services/price_calculation_service.dart';

/// 购物车状态
class CartState {

  const CartState({
    this.items = const [],
    this.isLoading = false,
    this.error,
    required this.summary,
    this.selectedCoupon,
    this.memberDiscount,
    this.priceCalculation,
  });

  /// 创建空状态
  factory CartState.empty() {
    return const CartState(
      summary: CartSummary(
        totalItems: 0,
        selectedItems: 0,
        totalAmount: 0.0,
        totalDiscount: 0.0,
        finalAmount: 0.0,
      ),
    );
  }
  final List<CartItem> items;
  final bool isLoading;
  final String? error;
  final CartSummary summary;
  final Coupon? selectedCoupon;
  final MemberDiscount? memberDiscount;
  final PriceCalculation? priceCalculation;

  CartState copyWith({
    List<CartItem>? items,
    bool? isLoading,
    String? error,
    CartSummary? summary,
    Coupon? selectedCoupon,
    MemberDiscount? memberDiscount,
    PriceCalculation? priceCalculation,
  }) {
    return CartState(
      items: items ?? this.items,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      summary: summary ?? this.summary,
      selectedCoupon: selectedCoupon ?? this.selectedCoupon,
      memberDiscount: memberDiscount ?? this.memberDiscount,
      priceCalculation: priceCalculation ?? this.priceCalculation,
    );
  }
}

/// 购物车状态管理器
class CartNotifier extends StateNotifier<CartState> {

  CartNotifier() : super(CartState.empty()) {
    loadCart();
  }
  final CartApiService _cartApiService = CartApiService();

  /// 加载购物车
  Future<void> loadCart() async {
    state = state.copyWith(isLoading: true);

    try {
      final response = await _cartApiService.getCartList();

      if (response.isSuccess && response.data != null) {
        final items = response.data!;
        final summary = _calculateSummary(items);

        state = state.copyWith(
          items: items,
          summary: summary,
          isLoading: false,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? '加载失败',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 添加商品到购物车
  Future<void> addToCart({
    required String productId,
    String? skuId,
    int quantity = 1,
  }) async {
    try {
      final request = AddToCartRequest(
        productId: productId,
        skuId: skuId,
        quantity: quantity,
      );

      final response = await _cartApiService.addToCart(request: request);

      if (response.isSuccess) {
        // 重新加载购物车
        await loadCart();
      } else {
        state = state.copyWith(error: response.message ?? '添加失败');
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  /// 更新购物车项数量
  Future<void> updateQuantity(String itemId, int quantity) async {
    if (quantity <= 0) {
      await removeItem(itemId);
      return;
    }

    // 乐观更新
    final updatedItems = state.items.map((item) {
      if (item.id == itemId) {
        return item.copyWith(
          quantity: quantity,
          updateTime: DateTime.now().millisecondsSinceEpoch,
        );
      }
      return item;
    }).toList();

    final summary = _calculateSummary(updatedItems);
    state = state.copyWith(items: updatedItems, summary: summary);

    try {
      final request = UpdateCartItemRequest(quantity: quantity);
      final response = await _cartApiService.updateCartItem(
        cartItemId: itemId,
        request: request,
      );

      if (!response.isSuccess) {
        // 回滚状态
        await loadCart();
        state = state.copyWith(error: response.message ?? '更新失败');
      }
    } catch (e) {
      // 回滚状态
      await loadCart();
      state = state.copyWith(error: e.toString());
    }
  }

  /// 切换商品选择状态
  Future<void> toggleItemSelection(String itemId) async {
    final item = state.items.firstWhere((item) => item.id == itemId);
    final newSelected = !item.selected;

    // 乐观更新
    final updatedItems = state.items.map((item) {
      if (item.id == itemId) {
        return item.copyWith(
          selected: newSelected,
          updateTime: DateTime.now().millisecondsSinceEpoch,
        );
      }
      return item;
    }).toList();

    final summary = _calculateSummary(updatedItems);
    state = state.copyWith(items: updatedItems, summary: summary);

    try {
      final request = UpdateCartItemRequest(selected: newSelected);
      final response = await _cartApiService.updateCartItem(
        cartItemId: itemId,
        request: request,
      );

      if (!response.isSuccess) {
        // 回滚状态
        await loadCart();
        state = state.copyWith(error: response.message ?? '更新失败');
      }
    } catch (e) {
      // 回滚状态
      await loadCart();
      state = state.copyWith(error: e.toString());
    }
  }

  /// 全选/取消全选
  Future<void> toggleSelectAll() async {
    final hasUnselected = state.items.any((item) => !item.selected);
    final newSelected = hasUnselected;

    // 乐观更新
    final updatedItems = state.items.map((item) {
      return item.copyWith(
        selected: newSelected,
        updateTime: DateTime.now().millisecondsSinceEpoch,
      );
    }).toList();

    final summary = _calculateSummary(updatedItems);
    state = state.copyWith(items: updatedItems, summary: summary);

    try {
      // 批量更新选择状态
      final futures = state.items.map((item) {
        final request = UpdateCartItemRequest(selected: newSelected);
        return _cartApiService.updateCartItem(
          cartItemId: item.id,
          request: request,
        );
      });

      await Future.wait(futures);
    } catch (e) {
      // 回滚状态
      await loadCart();
      state = state.copyWith(error: e.toString());
    }
  }

  /// 删除购物车项
  Future<void> removeItem(String itemId) async {
    // 乐观更新
    final updatedItems =
        state.items.where((item) => item.id != itemId).toList();
    final summary = _calculateSummary(updatedItems);
    state = state.copyWith(items: updatedItems, summary: summary);

    try {
      final response = await _cartApiService.removeCartItem(cartItemId: itemId);

      if (!response.isSuccess) {
        // 回滚状态
        await loadCart();
        state = state.copyWith(error: response.message ?? '删除失败');
      }
    } catch (e) {
      // 回滚状态
      await loadCart();
      state = state.copyWith(error: e.toString());
    }
  }

  /// 批量删除选中的商品
  Future<void> removeSelectedItems() async {
    final selectedItems = state.items.where((item) => item.selected).toList();
    if (selectedItems.isEmpty) return;

    // 乐观更新
    final updatedItems = state.items.where((item) => !item.selected).toList();
    final summary = _calculateSummary(updatedItems);
    state = state.copyWith(items: updatedItems, summary: summary);

    try {
      final futures = selectedItems.map((item) {
        return _cartApiService.removeCartItem(cartItemId: item.id);
      });

      await Future.wait(futures);
    } catch (e) {
      // 回滚状态
      await loadCart();
      state = state.copyWith(error: e.toString());
    }
  }

  /// 清空购物车
  Future<void> clearCart() async {
    // 乐观更新
    state = CartState.empty();

    try {
      final response = await _cartApiService.clearCart();

      if (!response.isSuccess) {
        // 回滚状态
        await loadCart();
        state = state.copyWith(error: response.message ?? '清空失败');
      }
    } catch (e) {
      // 回滚状态
      await loadCart();
      state = state.copyWith(error: e.toString());
    }
  }

  /// 刷新购物车
  Future<void> refresh() async {
    await loadCart();
  }

  /// 清除错误
  void clearError() {
    state = state.copyWith();
  }

  /// 计算购物车摘要
  CartSummary _calculateSummary(List<CartItem> items) {
    final availableItems = items.where((item) => item.available).toList();
    final selectedItems =
        availableItems.where((item) => item.selected).toList();

    final totalItems =
        availableItems.fold<int>(0, (sum, item) => sum + item.quantity);
    final selectedItemsCount =
        selectedItems.fold<int>(0, (sum, item) => sum + item.quantity);

    final totalAmount =
        selectedItems.fold<double>(0, (sum, item) => sum + item.totalPrice);
    final totalOriginalAmount = selectedItems.fold<double>(0, (sum, item) {
      return sum + (item.totalOriginalPrice ?? item.totalPrice);
    });

    final totalDiscount = totalOriginalAmount - totalAmount;

    // 简单的运费计算逻辑
    final freight = totalAmount >= 99.0 ? 0.0 : 10.0;

    final finalAmount = totalAmount + freight;

    return CartSummary(
      totalItems: totalItems,
      selectedItems: selectedItemsCount,
      totalAmount: totalAmount,
      totalOriginalAmount: totalOriginalAmount,
      totalDiscount: totalDiscount,
      freight: freight,
      finalAmount: finalAmount,
    );
  }

  /// 获取选中的商品项
  List<CartItem> get selectedItems {
    return state.items
        .where((item) => item.selected && item.available)
        .toList();
  }

  /// 是否有选中的商品
  bool get hasSelectedItems => selectedItems.isNotEmpty;

  /// 是否全选
  bool get isAllSelected {
    final availableItems = state.items.where((item) => item.available).toList();
    if (availableItems.isEmpty) return false;
    return availableItems.every((item) => item.selected);
  }

  /// 购物车商品总数量
  int get totalItemCount {
    return state.items.fold<int>(0, (sum, item) => sum + item.quantity);
  }

  /// 选择优惠券
  void selectCoupon(Coupon? coupon) {
    final calculation = PriceCalculationService.calculateCartPrice(
      items: state.items,
      coupon: coupon,
      memberDiscount: state.memberDiscount,
    );

    state = state.copyWith(
      selectedCoupon: coupon,
      priceCalculation: calculation,
    );
  }

  /// 设置会员折扣
  void setMemberDiscount(MemberDiscount? memberDiscount) {
    final calculation = PriceCalculationService.calculateCartPrice(
      items: state.items,
      coupon: state.selectedCoupon,
      memberDiscount: memberDiscount,
    );

    state = state.copyWith(
      memberDiscount: memberDiscount,
      priceCalculation: calculation,
    );
  }

  /// 重新计算价格
  void recalculatePrice() {
    final calculation = PriceCalculationService.calculateCartPrice(
      items: state.items,
      coupon: state.selectedCoupon,
      memberDiscount: state.memberDiscount,
    );

    state = state.copyWith(priceCalculation: calculation);
  }

  /// 获取可用优惠券
  List<Coupon> getAvailableCoupons(List<Coupon> allCoupons) {
    return PriceCalculationService.getAvailableCoupons(
      items: state.items,
      allCoupons: allCoupons,
    );
  }

  /// 获取最优优惠券
  Coupon? getBestCoupon(List<Coupon> availableCoupons) {
    return PriceCalculationService.selectBestCoupon(
      items: state.items,
      availableCoupons: availableCoupons,
      memberDiscount: state.memberDiscount,
    );
  }
}

/// 购物车状态提供者
final cartProvider = StateNotifierProvider<CartNotifier, CartState>(
  (ref) => CartNotifier(),
);

/// 购物车商品数量提供者
final cartItemCountProvider = Provider<int>((ref) {
  final cart = ref.watch(cartProvider);
  return cart.items.fold<int>(0, (sum, item) => sum + item.quantity);
});

/// 选中商品数量提供者
final selectedItemCountProvider = Provider<int>((ref) {
  final cart = ref.watch(cartProvider);
  return cart.items
      .where((item) => item.selected && item.available)
      .fold<int>(0, (sum, item) => sum + item.quantity);
});

/// 是否全选提供者
final isAllSelectedProvider = Provider<bool>((ref) {
  final cartNotifier = ref.read(cartProvider.notifier);
  return cartNotifier.isAllSelected;
});
