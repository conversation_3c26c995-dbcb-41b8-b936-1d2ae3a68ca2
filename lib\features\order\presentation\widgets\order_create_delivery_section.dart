import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/shared/presentation/widgets/custom_card.dart';
import 'package:soko/features/order/domain/entities/order_create_request.dart';

/// 订单创建配送方式组件
class OrderCreateDeliverySection extends StatelessWidget {

  const OrderCreateDeliverySection({
    super.key,
    required this.selectedMethod,
    required this.onMethodSelected,
  });
  final DeliveryMethod selectedMethod;
  final ValueChanged<DeliveryMethod> onMethodSelected;

  @override
  Widget build(BuildContext context) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.local_shipping,
                color: Colors.orange,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                '配送方式',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // 配送方式列表
          ...DeliveryMethod.values.map(_buildMethodOption),
        ],
      ),
    );
  }

  /// 构建配送方式选项
  Widget _buildMethodOption(DeliveryMethod method) {
    final isSelected = selectedMethod == method;

    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: InkWell(
        onTap: () => onMethodSelected(method),
        borderRadius: BorderRadius.circular(8.r),
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            color: isSelected 
                ? Colors.blue.withValues(alpha: 0.05)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(
              color: isSelected 
                  ? Colors.blue 
                  : Colors.grey.withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            children: [
              // 选择指示器
              Container(
                width: 20.w,
                height: 20.w,
                decoration: BoxDecoration(
                  color: isSelected ? Colors.blue : Colors.transparent,
                  border: Border.all(
                    color: isSelected ? Colors.blue : Colors.grey,
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: isSelected
                    ? Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 12.w,
                      )
                    : null,
              ),
              SizedBox(width: 12.w),

              // 配送方式信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          method.displayName,
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                        ),
                        Text(
                          method.fee > 0 
                              ? '¥${method.fee.toStringAsFixed(2)}'
                              : '免费',
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w500,
                            color: method.fee > 0 ? Colors.red : Colors.green,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      method.description,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
