// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'coupon.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Coupon _$CouponFromJson(Map<String, dynamic> json) => Coupon(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      type: json['type'] as String,
      value: (json['value'] as num).toDouble(),
      minAmount: (json['minAmount'] as num?)?.toDouble(),
      maxDiscount: (json['maxDiscount'] as num?)?.toDouble(),
      startTime: (json['startTime'] as num).toInt(),
      endTime: (json['endTime'] as num).toInt(),
      totalCount: (json['totalCount'] as num?)?.toInt(),
      usedCount: (json['usedCount'] as num?)?.toInt(),
      userLimit: (json['userLimit'] as num?)?.toInt(),
      status: json['status'] as String,
      createTime: (json['createTime'] as num).toInt(),
      updateTime: (json['updateTime'] as num).toInt(),
    );

Map<String, dynamic> _$CouponToJson(Coupon instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'type': instance.type,
      'value': instance.value,
      'minAmount': instance.minAmount,
      'maxDiscount': instance.maxDiscount,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'totalCount': instance.totalCount,
      'usedCount': instance.usedCount,
      'userLimit': instance.userLimit,
      'status': instance.status,
      'createTime': instance.createTime,
      'updateTime': instance.updateTime,
    };

UserCoupon _$UserCouponFromJson(Map<String, dynamic> json) => UserCoupon(
      id: json['id'] as String,
      userId: json['userId'] as String,
      couponId: json['couponId'] as String,
      coupon: json['coupon'] == null
          ? null
          : Coupon.fromJson(json['coupon'] as Map<String, dynamic>),
      status: json['status'] as String,
      usedTime: (json['usedTime'] as num?)?.toInt(),
      usedOrderId: json['usedOrderId'] as String?,
      receiveTime: (json['receiveTime'] as num).toInt(),
      expireTime: (json['expireTime'] as num).toInt(),
    );

Map<String, dynamic> _$UserCouponToJson(UserCoupon instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'couponId': instance.couponId,
      'coupon': instance.coupon,
      'status': instance.status,
      'usedTime': instance.usedTime,
      'usedOrderId': instance.usedOrderId,
      'receiveTime': instance.receiveTime,
      'expireTime': instance.expireTime,
    };
