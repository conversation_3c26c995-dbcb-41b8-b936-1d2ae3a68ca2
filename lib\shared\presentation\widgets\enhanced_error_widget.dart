import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 增强的错误处理组件
class EnhancedErrorWidget extends StatefulWidget {
  const EnhancedErrorWidget({
    super.key,
    required this.error,
    this.onRetry,
    this.onReport,
    this.showDetails = false,
    this.customActions,
  });

  final dynamic error;
  final VoidCallback? onRetry;
  final VoidCallback? onReport;
  final bool showDetails;
  final List<ErrorAction>? customActions;

  @override
  State<EnhancedErrorWidget> createState() => _EnhancedErrorWidgetState();
}

class _EnhancedErrorWidgetState extends State<EnhancedErrorWidget>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  bool _showDetails = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: Padding(
                padding: EdgeInsets.all(32.w),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 错误图标
                    _buildErrorIcon(),
                    SizedBox(height: 24.h),
                    
                    // 错误标题
                    Text(
                      _getErrorTitle(),
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[800],
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 8.h),
                    
                    // 错误描述
                    Text(
                      _getErrorDescription(),
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.grey[600],
                        height: 1.4,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    
                    // 错误详情
                    if (widget.showDetails || _showDetails) ...[
                      SizedBox(height: 16.h),
                      _buildErrorDetails(),
                    ],
                    
                    SizedBox(height: 24.h),
                    
                    // 操作按钮
                    _buildActionButtons(),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// 构建错误图标
  Widget _buildErrorIcon() {
    final errorType = _getErrorType();
    
    return Container(
      width: 80.w,
      height: 80.w,
      decoration: BoxDecoration(
        color: _getErrorColor().withValues(alpha: 0.1),
        shape: BoxShape.circle,
      ),
      child: Icon(
        _getErrorIcon(errorType),
        size: 40.w,
        color: _getErrorColor(),
      ),
    );
  }

  /// 构建错误详情
  Widget _buildErrorDetails() {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                size: 16.w,
                color: Colors.grey[600],
              ),
              SizedBox(width: 8.w),
              Text(
                '错误详情',
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            widget.error.toString(),
            style: TextStyle(
              fontSize: 11.sp,
              color: Colors.grey[600],
              fontFamily: 'monospace',
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    final actions = <Widget>[];
    
    // 重试按钮
    if (widget.onRetry != null) {
      actions.add(
        ElevatedButton.icon(
          onPressed: widget.onRetry,
          icon: const Icon(Icons.refresh),
          label: const Text('重试'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).primaryColor,
            foregroundColor: Colors.white,
          ),
        ),
      );
    }
    
    // 自定义操作
    if (widget.customActions != null) {
      for (final action in widget.customActions!) {
        actions.add(
          OutlinedButton.icon(
            onPressed: action.onPressed,
            icon: Icon(action.icon),
            label: Text(action.label),
          ),
        );
      }
    }
    
    // 详情切换按钮
    if (widget.showDetails) {
      actions.add(
        TextButton.icon(
          onPressed: () {
            setState(() {
              _showDetails = !_showDetails;
            });
          },
          icon: Icon(_showDetails ? Icons.expand_less : Icons.expand_more),
          label: Text(_showDetails ? '隐藏详情' : '显示详情'),
        ),
      );
    }
    
    // 报告按钮
    if (widget.onReport != null) {
      actions.add(
        TextButton.icon(
          onPressed: widget.onReport,
          icon: const Icon(Icons.bug_report),
          label: const Text('报告问题'),
        ),
      );
    }
    
    return Wrap(
      spacing: 8.w,
      runSpacing: 8.h,
      alignment: WrapAlignment.center,
      children: actions,
    );
  }

  /// 获取错误类型
  ErrorType _getErrorType() {
    final errorString = widget.error.toString().toLowerCase();
    
    if (errorString.contains('network') || 
        errorString.contains('connection') ||
        errorString.contains('timeout')) {
      return ErrorType.network;
    } else if (errorString.contains('permission') ||
               errorString.contains('unauthorized')) {
      return ErrorType.permission;
    } else if (errorString.contains('not found') ||
               errorString.contains('404')) {
      return ErrorType.notFound;
    } else if (errorString.contains('server') ||
               errorString.contains('500')) {
      return ErrorType.server;
    } else {
      return ErrorType.unknown;
    }
  }

  /// 获取错误图标
  IconData _getErrorIcon(ErrorType type) {
    switch (type) {
      case ErrorType.network:
        return Icons.wifi_off;
      case ErrorType.permission:
        return Icons.lock;
      case ErrorType.notFound:
        return Icons.search_off;
      case ErrorType.server:
        return Icons.dns;
      case ErrorType.unknown:
        return Icons.error_outline;
    }
  }

  /// 获取错误颜色
  Color _getErrorColor() {
    final type = _getErrorType();
    switch (type) {
      case ErrorType.network:
        return Colors.orange;
      case ErrorType.permission:
        return Colors.red;
      case ErrorType.notFound:
        return Colors.blue;
      case ErrorType.server:
        return Colors.purple;
      case ErrorType.unknown:
        return Colors.grey;
    }
  }

  /// 获取错误标题
  String _getErrorTitle() {
    final type = _getErrorType();
    switch (type) {
      case ErrorType.network:
        return '网络连接异常';
      case ErrorType.permission:
        return '权限不足';
      case ErrorType.notFound:
        return '内容未找到';
      case ErrorType.server:
        return '服务器异常';
      case ErrorType.unknown:
        return '出现了一些问题';
    }
  }

  /// 获取错误描述
  String _getErrorDescription() {
    final type = _getErrorType();
    switch (type) {
      case ErrorType.network:
        return '请检查网络连接后重试';
      case ErrorType.permission:
        return '您没有执行此操作的权限';
      case ErrorType.notFound:
        return '请求的内容不存在或已被删除';
      case ErrorType.server:
        return '服务器暂时无法响应，请稍后重试';
      case ErrorType.unknown:
        return '遇到了未知错误，请重试或联系客服';
    }
  }
}

/// 错误类型枚举
enum ErrorType {
  network,
  permission,
  notFound,
  server,
  unknown,
}

/// 错误操作
class ErrorAction {
  const ErrorAction({
    required this.label,
    required this.icon,
    required this.onPressed,
  });

  final String label;
  final IconData icon;
  final VoidCallback onPressed;
}
