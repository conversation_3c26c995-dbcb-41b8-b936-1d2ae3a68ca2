import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 支付安全提示组件
class PaymentSecurityTips extends StatelessWidget {
  const PaymentSecurityTips({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.security,
                size: 20.w,
                color: Colors.blue[600],
              ),
              SizedBox(width: 8.w),
              Text(
                '安全提示',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          
          // 安全提示列表
          ..._buildSecurityTips().map((tip) => _buildTipItem(tip)),
          
          SizedBox(height: 8.h),
          
          // 沙盒环境说明
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Colors.orange[50],
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(color: Colors.orange[200]!),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.science,
                  size: 16.w,
                  color: Colors.orange[600],
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: Text(
                    '当前为沙盒测试环境，不会产生真实扣费',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.orange[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建提示项
  Widget _buildTipItem(String tip) {
    return Padding(
      padding: EdgeInsets.only(bottom: 6.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 4.w,
            height: 4.w,
            margin: EdgeInsets.only(top: 8.h, right: 8.w),
            decoration: BoxDecoration(
              color: Colors.blue[600],
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Text(
              tip,
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.blue[700],
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 获取安全提示列表
  List<String> _buildSecurityTips() {
    return [
      '请确认收款方信息无误后再进行支付',
      '支付过程中请勿关闭页面或退出应用',
      '如遇支付问题，请联系客服获取帮助',
      '支付完成后请保留相关凭证',
    ];
  }
}
