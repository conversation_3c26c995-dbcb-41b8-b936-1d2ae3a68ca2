import 'package:json_annotation/json_annotation.dart';

import 'package:soko/core/enums/app_enums.dart';

part 'product.g.dart';

/// 商品实体类
@JsonSerializable()
class Product {

  const Product({
    required this.id,
    required this.name,
    required this.category,
    this.brand,
    this.acg,
    this.type,
    this.size,
    this.description,
    this.files,
    this.skus,
    required this.minPrice,
    this.maxPrice,
    this.originalPrice,
    this.newable,
    this.domesticFreight,
    this.internationalFreight,
    this.totalSales,
    this.salesType,
    required this.status,
    required this.createTime,
    required this.updateTime,
  });

  factory Product.fromJson(Map<String, dynamic> json) => _$ProductFromJson(json);
  @Json<PERSON>ey(name: 'id')
  final String id;

  @JsonKey(name: 'name')
  final String name;

  @JsonKey(name: 'category')
  final String category;

  @JsonKey(name: 'brand')
  final String? brand;

  @JsonKey(name: 'acg')
  final String? acg;

  @Json<PERSON>ey(name: 'type')
  final String? type;

  @Json<PERSON>ey(name: 'size')
  final String? size;

  @Json<PERSON>ey(name: 'description')
  final String? description;

  @JsonKey(name: 'files')
  final List<ProductFile>? files;

  @JsonKey(name: 'skus')
  final List<ProductSku>? skus;

  @JsonKey(name: 'minPrice')
  final double minPrice;

  @JsonKey(name: 'maxPrice')
  final double? maxPrice;

  @JsonKey(name: 'originalPrice')
  final double? originalPrice;

  @JsonKey(name: 'newable')
  final bool? newable;

  @JsonKey(name: 'domesticFreight')
  final double? domesticFreight;

  @JsonKey(name: 'internationalFreight')
  final double? internationalFreight;

  @JsonKey(name: 'totalSales')
  final int? totalSales;

  @JsonKey(name: 'salesType')
  final String? salesType;

  @JsonKey(name: 'status')
  final String status;

  @JsonKey(name: 'createTime')
  final int createTime;

  @JsonKey(name: 'updateTime')
  final int updateTime;

  Map<String, dynamic> toJson() => _$ProductToJson(this);

  /// 获取主图
  String? get mainImage {
    if (files == null || files!.isEmpty) return null;
    final mainFile = files!.firstWhere(
      (file) => file.isMain,
      orElse: () => files!.first,
    );
    return mainFile.url;
  }

  /// 获取缩略图
  String? get thumbnail {
    if (files == null || files!.isEmpty) return null;
    final mainFile = files!.firstWhere(
      (file) => file.isMain,
      orElse: () => files!.first,
    );
    return mainFile.thumbnailUrl ?? mainFile.url;
  }

  /// 获取价格区间显示
  String get priceRange {
    if (maxPrice != null && maxPrice! > minPrice) {
      return '¥${minPrice.toStringAsFixed(2)} - ¥${maxPrice!.toStringAsFixed(2)}';
    }
    return '¥${minPrice.toStringAsFixed(2)}';
  }

  /// 是否有折扣
  bool get hasDiscount {
    return originalPrice != null && originalPrice! > minPrice;
  }

  /// 折扣率
  double? get discountRate {
    if (!hasDiscount) return null;
    return (originalPrice! - minPrice) / originalPrice!;
  }

  /// 获取商品状态枚举
  ProductStatus get statusEnum {
    switch (status) {
      case 'active':
        return ProductStatus.active;
      case 'inactive':
        return ProductStatus.inactive;
      case 'sold_out':
        return ProductStatus.soldOut;
      case 'deleted':
        return ProductStatus.deleted;
      default:
        return ProductStatus.inactive;
    }
  }

  /// 获取分类枚举
  ProductCategory get categoryEnum {
    switch (category) {
      case 'ultraman':
        return ProductCategory.ultraman;
      case 'kamen_rider':
        return ProductCategory.kamenRider;
      case 'super_sentai':
        return ProductCategory.superSentai;
      case 'gundam':
        return ProductCategory.gundam;
      case 'figure':
        return ProductCategory.figure;
      default:
        return ProductCategory.other;
    }
  }

  /// 是否可购买
  bool get canPurchase {
    return statusEnum == ProductStatus.active && 
           (skus?.any((sku) => sku.stock > 0) ?? true);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Product && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Product(id: $id, name: $name, minPrice: $minPrice)';
  }
}

/// 商品文件
@JsonSerializable()
class ProductFile {

  const ProductFile({
    required this.id,
    required this.url,
    this.thumbnailUrl,
    required this.sort,
    required this.isMain,
  });

  factory ProductFile.fromJson(Map<String, dynamic> json) => _$ProductFileFromJson(json);
  @JsonKey(name: 'id')
  final String id;

  @JsonKey(name: 'url')
  final String url;

  @JsonKey(name: 'thumbnailUrl')
  final String? thumbnailUrl;

  @JsonKey(name: 'sort')
  final int sort;

  @JsonKey(name: 'isMain')
  final bool isMain;

  Map<String, dynamic> toJson() => _$ProductFileToJson(this);
}

/// 商品SKU
@JsonSerializable()
class ProductSku {

  const ProductSku({
    required this.id,
    required this.name,
    required this.price,
    this.originalPrice,
    required this.stock,
    required this.status,
    this.attributes,
  });

  factory ProductSku.fromJson(Map<String, dynamic> json) => _$ProductSkuFromJson(json);
  @JsonKey(name: 'id')
  final String id;

  @JsonKey(name: 'name')
  final String name;

  @JsonKey(name: 'price')
  final double price;

  @JsonKey(name: 'originalPrice')
  final double? originalPrice;

  @JsonKey(name: 'stock')
  final int stock;

  @JsonKey(name: 'status')
  final String status;

  @JsonKey(name: 'attributes')
  final Map<String, String>? attributes;

  Map<String, dynamic> toJson() => _$ProductSkuToJson(this);

  /// 是否有库存
  bool get hasStock => stock > 0;

  /// 是否有折扣
  bool get hasDiscount => originalPrice != null && originalPrice! > price;
}
