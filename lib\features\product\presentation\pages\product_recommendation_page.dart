import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import 'package:soko/shared/presentation/widgets/custom_app_bar.dart';
import 'package:soko/shared/presentation/widgets/loading_widget.dart';
import 'package:soko/shared/presentation/widgets/error_retry_widget.dart';
import 'package:soko/features/product/presentation/providers/product_recommendation_provider.dart';
import 'package:soko/features/product/presentation/widgets/product_card.dart';
import 'package:soko/features/product/presentation/widgets/product_section_header.dart';
import 'package:soko/features/product/domain/entities/product.dart';

/// 商品推荐页面
class ProductRecommendationPage extends ConsumerStatefulWidget {
  const ProductRecommendationPage({super.key});

  @override
  ConsumerState<ProductRecommendationPage> createState() => _ProductRecommendationPageState();
}

class _ProductRecommendationPageState extends ConsumerState<ProductRecommendationPage>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // 页面初始化时加载推荐数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(productRecommendationProvider.notifier).loadRecommendations();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: '为你推荐',
        showBackButton: true,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: '个性推荐'),
            Tab(text: '热门商品'),
            Tab(text: '最新上架'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildRecommendedTab(),
          _buildPopularTab(),
          _buildLatestTab(),
        ],
      ),
    );
  }

  /// 构建个性推荐标签页
  Widget _buildRecommendedTab() {
    final recommendedState = ref.watch(recommendedProductsProvider);
    
    return recommendedState.when(
      data: (products) => _buildProductGrid(products, '个性推荐'),
      loading: () => const LoadingWidget(),
      error: (error, stackTrace) => ErrorRetryWidget(
        message: error.toString(),
        onRetry: () => ref.refresh(recommendedProductsProvider),
      ),
    );
  }

  /// 构建热门商品标签页
  Widget _buildPopularTab() {
    final popularState = ref.watch(popularProductsProvider);
    
    return popularState.when(
      data: (products) => _buildProductGrid(products, '热门商品'),
      loading: () => const LoadingWidget(),
      error: (error, stackTrace) => ErrorRetryWidget(
        message: error.toString(),
        onRetry: () => ref.refresh(popularProductsProvider),
      ),
    );
  }

  /// 构建最新上架标签页
  Widget _buildLatestTab() {
    final latestState = ref.watch(latestProductsProvider);
    
    return latestState.when(
      data: (products) => _buildProductGrid(products, '最新上架'),
      loading: () => const LoadingWidget(),
      error: (error, stackTrace) => ErrorRetryWidget(
        message: error.toString(),
        onRetry: () => ref.refresh(latestProductsProvider),
      ),
    );
  }

  /// 构建商品网格
  Widget _buildProductGrid(List<Product> products, String title) {
    if (products.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.shopping_bag_outlined,
              size: 80.w,
              color: Colors.grey[400],
            ),
            SizedBox(height: 16.h),
            Text(
              '暂无${title}商品',
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => _refreshData(),
      child: CustomScrollView(
        slivers: [
          // 推荐说明
          SliverToBoxAdapter(
            child: Container(
              margin: EdgeInsets.all(16.w),
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.lightbulb_outline,
                    color: Colors.blue[600],
                    size: 20.w,
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      _getRecommendationTip(title),
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.blue[700],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // 商品网格
          SliverPadding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            sliver: SliverGrid(
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.75,
                crossAxisSpacing: 12.w,
                mainAxisSpacing: 12.h,
              ),
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final product = products[index];
                  return ProductCard(
                    product: product,
                    onTap: () => _navigateToProductDetail(product.id),
                    showRecommendationBadge: title == '个性推荐',
                  );
                },
                childCount: products.length,
              ),
            ),
          ),
          
          // 底部间距
          SliverToBoxAdapter(
            child: SizedBox(height: 32.h),
          ),
        ],
      ),
    );
  }

  /// 获取推荐提示文本
  String _getRecommendationTip(String title) {
    switch (title) {
      case '个性推荐':
        return '基于您的浏览历史和偏好为您精选的商品';
      case '热门商品':
        return '当前最受欢迎的热销商品，品质有保障';
      case '最新上架':
        return '最新发布的优质商品，抢先体验';
      default:
        return '为您精心挑选的优质商品';
    }
  }

  /// 刷新数据
  Future<void> _refreshData() async {
    await Future.wait([
      ref.refresh(recommendedProductsProvider.future),
      ref.refresh(popularProductsProvider.future),
      ref.refresh(latestProductsProvider.future),
    ]);
  }

  /// 导航到商品详情
  void _navigateToProductDetail(String productId) {
    context.push('/product/$productId');
  }
}
