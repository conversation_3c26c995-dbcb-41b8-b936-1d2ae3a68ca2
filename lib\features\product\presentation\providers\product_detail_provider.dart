import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/features/product/data/datasources/product_api_service.dart';
import 'package:soko/features/product/domain/entities/product.dart';

/// 商品详情状态
class ProductDetailState {

  const ProductDetailState({
    this.product,
    this.isLoading = false,
    this.error,
    this.isFavorite = false,
    this.selectedSpecId,
    this.quantity = 1,
  });
  final Product? product;
  final bool isLoading;
  final String? error;
  final bool isFavorite;
  final String? selectedSpecId;
  final int quantity;

  ProductDetailState copyWith({
    Product? product,
    bool? isLoading,
    String? error,
    bool? isFavorite,
    String? selectedSpecId,
    int? quantity,
  }) {
    return ProductDetailState(
      product: product ?? this.product,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isFavorite: isFavorite ?? this.isFavorite,
      selectedSpecId: selectedSpecId ?? this.selectedSpecId,
      quantity: quantity ?? this.quantity,
    );
  }
}

/// 商品详情状态管理器
class ProductDetailNotifier extends StateNotifier<ProductDetailState> {

  ProductDetailNotifier(this.productId) : super(const ProductDetailState()) {
    loadProductDetail();
  }
  final ProductApiService _productApiService = ProductApiService();
  final String productId;

  /// 加载商品详情
  Future<void> loadProductDetail() async {
    state = state.copyWith(isLoading: true);

    try {
      final response = await _productApiService.getProductDetail(productId);

      if (response.isSuccess && response.data != null) {
        state = state.copyWith(
          product: response.data,
          isLoading: false,
          // 默认选择第一个规格
          selectedSpecId:
              response.data!.skus != null && response.data!.skus!.isNotEmpty
                  ? response.data!.skus!.first.id
                  : null,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? '加载失败',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 选择规格
  void selectSpec(String specId) {
    if (state.product?.skus?.any((sku) => sku.id == specId) ?? false) {
      state = state.copyWith(selectedSpecId: specId);
    }
  }

  /// 设置数量
  void setQuantity(int quantity) {
    if (quantity > 0) {
      state = state.copyWith(quantity: quantity);
    }
  }

  /// 增加数量
  void increaseQuantity() {
    final currentSpec = getCurrentSpec();
    final maxStock = currentSpec?.stock ?? 999;

    if (state.quantity < maxStock) {
      state = state.copyWith(quantity: state.quantity + 1);
    }
  }

  /// 减少数量
  void decreaseQuantity() {
    if (state.quantity > 1) {
      state = state.copyWith(quantity: state.quantity - 1);
    }
  }

  /// 切换收藏状态
  Future<void> toggleFavorite() async {
    // TODO: 实现收藏API调用
    state = state.copyWith(isFavorite: !state.isFavorite);
  }

  /// 获取当前选中的规格
  ProductSku? getCurrentSpec() {
    if (state.product == null || state.selectedSpecId == null) {
      return null;
    }

    if (state.product!.skus == null || state.product!.skus!.isEmpty) {
      return null;
    }

    try {
      return state.product!.skus!.firstWhere(
        (sku) => sku.id == state.selectedSpecId,
      );
    } catch (e) {
      return state.product!.skus!.first;
    }
  }

  /// 获取当前价格
  double getCurrentPrice() {
    final spec = getCurrentSpec();
    return spec?.price ?? state.product?.minPrice ?? 0.0;
  }

  /// 获取当前库存
  int getCurrentStock() {
    final spec = getCurrentSpec();
    return spec?.stock ?? 0;
  }

  /// 检查是否有库存
  bool get hasStock => getCurrentStock() > 0;

  /// 检查是否可以购买
  bool get canPurchase => hasStock && state.quantity <= getCurrentStock();

  /// 刷新商品详情
  Future<void> refresh() async {
    await loadProductDetail();
  }
}

/// 商品详情状态提供者工厂
final productDetailProvider = StateNotifierProvider.family<
    ProductDetailNotifier, ProductDetailState, String>(
  (ref, productId) => ProductDetailNotifier(productId),
);

/// 当前商品规格提供者
final currentProductSpecProvider =
    Provider.family<ProductSku?, String>((ref, productId) {
  final notifier = ref.read(productDetailProvider(productId).notifier);
  return notifier.getCurrentSpec();
});

/// 当前商品价格提供者
final currentProductPriceProvider =
    Provider.family<double, String>((ref, productId) {
  final notifier = ref.read(productDetailProvider(productId).notifier);
  return notifier.getCurrentPrice();
});

/// 当前商品库存提供者
final currentProductStockProvider =
    Provider.family<int, String>((ref, productId) {
  final notifier = ref.read(productDetailProvider(productId).notifier);
  return notifier.getCurrentStock();
});
