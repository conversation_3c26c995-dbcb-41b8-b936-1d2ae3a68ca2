import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/shared/presentation/widgets/loading_widget.dart';
import 'package:soko/features/recycle/domain/entities/recycle_models.dart';
import 'package:soko/features/recycle/presentation/providers/recycle_create_provider.dart';

/// 回收订单创建 - 成色选择步骤
class RecycleCreateConditionStep extends ConsumerStatefulWidget {

  const RecycleCreateConditionStep({
    super.key,
    this.selectedConditionId,
    required this.onConditionSelected,
  });
  final String? selectedConditionId;
  final ValueChanged<String> onConditionSelected;

  @override
  ConsumerState<RecycleCreateConditionStep> createState() => _RecycleCreateConditionStepState();
}

class _RecycleCreateConditionStepState extends ConsumerState<RecycleCreateConditionStep> {
  @override
  void initState() {
    super.initState();
    // 加载成色选项
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(recycleCreateProvider.notifier).loadConditions();
    });
  }

  @override
  Widget build(BuildContext context) {
    final createState = ref.watch(recycleCreateProvider);

    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 步骤标题
          Text(
            '选择商品成色',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '请根据商品实际情况选择对应成色',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 24.h),

          // 成色选项列表
          Expanded(
            child: createState.conditionsState.when(
              idle: () => const SizedBox.shrink(),
              loading: () => const LoadingWidget(),
              success: _buildConditionsList,
              error: _buildErrorWidget,
            ),
          ),

          // 价格估算提示
          if (createState.priceEstimateState.isLoading) ...[
            SizedBox(height: 16.h),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Row(
                children: [
                  SizedBox(
                    width: 20.w,
                    height: 20.w,
                    child: const CircularProgressIndicator(strokeWidth: 2),
                  ),
                  SizedBox(width: 12.w),
                  Text(
                    '正在估算价格...',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.blue[700],
                    ),
                  ),
                ],
              ),
            ),
          ],

          if (createState.priceEstimateState.hasValue) ...[
            SizedBox(height: 16.h),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.monetization_on,
                    color: Colors.green,
                    size: 20.w,
                  ),
                  SizedBox(width: 12.w),
                  Text(
                    '预估价格: ',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.green[700],
                    ),
                  ),
                  Text(
                    '¥${createState.priceEstimateState.value!.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.green[700],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建成色选项列表
  Widget _buildConditionsList(List<ConditionOption> conditions) {
    return ListView.builder(
      itemCount: conditions.length,
      itemBuilder: (context, index) {
        final condition = conditions[index];
        return _buildConditionCard(condition);
      },
    );
  }

  /// 构建成色卡片
  Widget _buildConditionCard(ConditionOption condition) {
    final isSelected = widget.selectedConditionId == condition.id;

    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: InkWell(
        onTap: () => widget.onConditionSelected(condition.id),
        borderRadius: BorderRadius.circular(12.r),
        child: Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: isSelected 
                ? Colors.blue.withValues(alpha: 0.1)
                : Colors.white,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: isSelected 
                  ? Colors.blue 
                  : Colors.grey.withValues(alpha: 0.3),
              width: isSelected ? 2 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // 成色图标
              Container(
                width: 48.w,
                height: 48.w,
                decoration: BoxDecoration(
                  color: _getConditionColor(condition.name).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(24.r),
                ),
                child: Icon(
                  _getConditionIcon(condition.name),
                  color: _getConditionColor(condition.name),
                  size: 24.w,
                ),
              ),
              SizedBox(width: 16.w),

              // 成色信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 成色名称
                    Text(
                      condition.name,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: isSelected ? Colors.blue : Colors.black87,
                      ),
                    ),
                    SizedBox(height: 4.h),

                    // 成色描述
                    if (condition.description?.isNotEmpty == true)
                      Text(
                        condition.description!,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                    SizedBox(height: 4.h),

                    // 价格系数
                    Text(
                      '价格系数: ${(condition.priceMultiplier * 100).toInt()}%',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.orange[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),

              // 选中指示器
              if (isSelected)
                Icon(
                  Icons.check_circle,
                  color: Colors.blue,
                  size: 24.w,
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建错误组件
  Widget _buildErrorWidget(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64.w,
            color: Colors.red[300],
          ),
          SizedBox(height: 16.h),
          Text(
            '加载失败',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            error,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24.h),
          ElevatedButton(
            onPressed: () {
              ref.read(recycleCreateProvider.notifier).loadConditions();
            },
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  /// 获取成色图标
  IconData _getConditionIcon(String conditionName) {
    switch (conditionName.toLowerCase()) {
      case '全新':
      case 'excellent':
        return Icons.star;
      case '良好':
      case 'good':
        return Icons.thumb_up;
      case '一般':
      case 'fair':
        return Icons.horizontal_rule;
      case '较差':
      case 'poor':
        return Icons.thumb_down;
      default:
        return Icons.help_outline;
    }
  }

  /// 获取成色颜色
  Color _getConditionColor(String conditionName) {
    switch (conditionName.toLowerCase()) {
      case '全新':
      case 'excellent':
        return Colors.green;
      case '良好':
      case 'good':
        return Colors.blue;
      case '一般':
      case 'fair':
        return Colors.orange;
      case '较差':
      case 'poor':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
