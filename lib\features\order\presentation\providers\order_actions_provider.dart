import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/state/base_state.dart';
import 'package:soko/features/order/domain/usecases/cancel_order_usecase.dart';
import 'package:soko/features/order/domain/usecases/confirm_received_usecase.dart';
import 'package:soko/features/order/domain/usecases/delete_order_usecase.dart';
import 'package:soko/features/order/domain/usecases/request_refund_usecase.dart';

/// 订单操作状态
enum OrderActionType {
  cancel,
  confirmReceived,
  requestRefund,
  delete,
}

/// 订单操作状态管理器
class OrderActionsNotifier extends StateNotifier<Map<String, AsyncState<bool>>> {

  OrderActionsNotifier(
    this._cancelOrderUseCase,
    this._confirmReceivedUseCase,
    this._requestRefundUseCase,
    this._deleteOrderUseCase,
  ) : super({});
  final CancelOrderUseCase _cancelOrderUseCase;
  final ConfirmReceivedUseCase _confirmReceivedUseCase;
  final RequestRefundUseCase _requestRefundUseCase;
  final DeleteOrderUseCase _deleteOrderUseCase;

  /// 获取订单操作状态
  AsyncState<bool> getOrderActionState(String orderId, OrderActionType actionType) {
    final key = '${orderId}_${actionType.name}';
    return state[key] ?? const AsyncState.idle();
  }

  /// 设置订单操作状态
  void _setOrderActionState(String orderId, OrderActionType actionType, AsyncState<bool> actionState) {
    final key = '${orderId}_${actionType.name}';
    state = {...state, key: actionState};
  }

  /// 取消订单
  Future<bool> cancelOrder(String orderId) async {
    _setOrderActionState(orderId, OrderActionType.cancel, const AsyncState.loading());

    try {
      final result = await _cancelOrderUseCase(orderId);

      return result.fold(
        (failure) {
          _setOrderActionState(orderId, OrderActionType.cancel, AsyncState.error(failure.message));
          return false;
        },
        (success) {
          _setOrderActionState(orderId, OrderActionType.cancel, AsyncState.success(success));
          return success;
        },
      );
    } catch (e) {
      _setOrderActionState(orderId, OrderActionType.cancel, AsyncState.error(e.toString()));
      return false;
    }
  }

  /// 确认收货
  Future<bool> confirmReceived(String orderId) async {
    _setOrderActionState(orderId, OrderActionType.confirmReceived, const AsyncState.loading());

    try {
      final result = await _confirmReceivedUseCase(orderId);

      return result.fold(
        (failure) {
          _setOrderActionState(orderId, OrderActionType.confirmReceived, AsyncState.error(failure.message));
          return false;
        },
        (success) {
          _setOrderActionState(orderId, OrderActionType.confirmReceived, AsyncState.success(success));
          return success;
        },
      );
    } catch (e) {
      _setOrderActionState(orderId, OrderActionType.confirmReceived, AsyncState.error(e.toString()));
      return false;
    }
  }

  /// 申请退款
  Future<bool> requestRefund(RefundRequest request) async {
    _setOrderActionState(request.orderId, OrderActionType.requestRefund, const AsyncState.loading());

    try {
      final result = await _requestRefundUseCase(request);

      return result.fold(
        (failure) {
          _setOrderActionState(request.orderId, OrderActionType.requestRefund, AsyncState.error(failure.message));
          return false;
        },
        (success) {
          _setOrderActionState(request.orderId, OrderActionType.requestRefund, AsyncState.success(success));
          return success;
        },
      );
    } catch (e) {
      _setOrderActionState(request.orderId, OrderActionType.requestRefund, AsyncState.error(e.toString()));
      return false;
    }
  }

  /// 删除订单
  Future<bool> deleteOrder(String orderId) async {
    _setOrderActionState(orderId, OrderActionType.delete, const AsyncState.loading());

    try {
      final result = await _deleteOrderUseCase(orderId);

      return result.fold(
        (failure) {
          _setOrderActionState(orderId, OrderActionType.delete, AsyncState.error(failure.message));
          return false;
        },
        (success) {
          _setOrderActionState(orderId, OrderActionType.delete, AsyncState.success(success));
          return success;
        },
      );
    } catch (e) {
      _setOrderActionState(orderId, OrderActionType.delete, AsyncState.error(e.toString()));
      return false;
    }
  }

  /// 清除订单操作状态
  void clearOrderActionState(String orderId, OrderActionType actionType) {
    final key = '${orderId}_${actionType.name}';
    final newState = Map<String, AsyncState<bool>>.from(state);
    newState.remove(key);
    state = newState;
  }

  /// 清除所有订单操作状态
  void clearAllActionStates() {
    state = {};
  }
}

/// 订单操作状态管理器提供者
final orderActionsProvider = StateNotifierProvider<OrderActionsNotifier, Map<String, AsyncState<bool>>>((ref) {
  final cancelOrderUseCase = ref.read(cancelOrderUseCaseProvider);
  final confirmReceivedUseCase = ref.read(confirmReceivedUseCaseProvider);
  final requestRefundUseCase = ref.read(requestRefundUseCaseProvider);
  final deleteOrderUseCase = ref.read(deleteOrderUseCaseProvider);

  return OrderActionsNotifier(
    cancelOrderUseCase,
    confirmReceivedUseCase,
    requestRefundUseCase,
    deleteOrderUseCase,
  );
});

/// 获取特定订单操作状态的提供者
final orderActionStateProvider = Provider.family<AsyncState<bool>, (String, OrderActionType)>((ref, params) {
  final (orderId, actionType) = params;
  final actionsNotifier = ref.watch(orderActionsProvider.notifier);
  return actionsNotifier.getOrderActionState(orderId, actionType);
});
