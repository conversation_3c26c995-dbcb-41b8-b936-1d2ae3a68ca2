import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/core/theme/app_colors.dart';
import 'package:soko/core/theme/app_text_styles.dart';
import 'package:soko/shared/presentation/widgets/custom_app_bar.dart';
import 'package:soko/shared/presentation/widgets/loading_widget.dart';
import 'package:soko/features/home/<USER>/providers/home_provider.dart';
import 'package:soko/features/home/<USER>/widgets/banner_carousel.dart';
import 'package:soko/features/home/<USER>/widgets/category_grid.dart';
import 'package:soko/features/home/<USER>/widgets/product_section.dart';

/// 首页
class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CustomAppBar(
        title: '中古虾',
        showBackButton: false,
        actions: [
          IconButton(
            onPressed: () {
              // TODO: 跳转到搜索页面
            },
            icon: Icon(Icons.search, color: AppColors.textPrimary, size: 24.w),
          ),
          IconButton(
            onPressed: () {
              // TODO: 跳转到消息中心
            },
            icon: Icon(
              Icons.notifications_outlined,
              color: AppColors.textPrimary,
              size: 24.w,
            ),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _onRefresh,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            children: [
              // 轮播图区域
              _buildBannerSection(),

              // 分类导航
              _buildCategorySection(),

              // 新品推荐
              _buildNewProductsSection(),

              // 热门商品
              _buildHotProductsSection(),

              // 底部间距
              SizedBox(height: 20.h),
            ],
          ),
        ),
      ),
    );
  }

  /// 下拉刷新
  Future<void> _onRefresh() async {
    // TODO: 实现刷新逻辑
    await Future.delayed(const Duration(seconds: 1));
  }

  /// 构建轮播图区域
  Widget _buildBannerSection() {
    return Container(
      height: 180.h,
      margin: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.image, size: 48.w, color: AppColors.primary),
            SizedBox(height: 8.h),
            Text(
              '轮播图区域',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.primary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建分类导航区域
  Widget _buildCategorySection() {
    final categories = ['奥特曼', '假面骑士', '超级战队', '高达模型', '手办', '更多'];

    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(vertical: 20.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Text('ACG分类', style: AppTextStyles.titleLarge),
          ),
          SizedBox(height: 16.h),
          SizedBox(
            height: 80.h,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              itemCount: categories.length,
              itemBuilder: (context, index) {
                return Container(
                  width: 60.w,
                  margin: EdgeInsets.only(right: 16.w),
                  child: Column(
                    children: [
                      Container(
                        width: 48.w,
                        height: 48.w,
                        decoration: BoxDecoration(
                          color: AppColors.primary.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(24.r),
                        ),
                        child: Icon(
                          Icons.category,
                          color: AppColors.primary,
                          size: 24.w,
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Text(
                        categories[index],
                        style: AppTextStyles.labelSmall,
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建新品推荐区域
  Widget _buildNewProductsSection() {
    return Container(
      color: Colors.white,
      margin: EdgeInsets.only(top: 8.h),
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('新品推荐', style: AppTextStyles.titleLarge),
              TextButton(
                onPressed: () {
                  // TODO: 跳转到新品列表
                },
                child: Text(
                  '查看更多',
                  style: AppTextStyles.labelMedium.copyWith(
                    color: AppColors.primary,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          SizedBox(
            height: 200.h,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: 5,
              itemBuilder: (context, index) {
                return Container(
                  width: 140.w,
                  margin: EdgeInsets.only(right: 12.w),
                  decoration: BoxDecoration(
                    color: AppColors.background,
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        height: 120.h,
                        decoration: BoxDecoration(
                          color: AppColors.border,
                          borderRadius: BorderRadius.vertical(
                            top: Radius.circular(8.r),
                          ),
                        ),
                        child: Center(
                          child: Icon(
                            Icons.image,
                            size: 32.w,
                            color: AppColors.textTertiary,
                          ),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.all(8.w),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '商品名称 ${index + 1}',
                              style: AppTextStyles.bodySmall,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            SizedBox(height: 4.h),
                            Text(
                              '¥99.99',
                              style: AppTextStyles.labelMedium.copyWith(
                                color: AppColors.price,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建热门商品区域
  Widget _buildHotProductsSection() {
    return Container(
      color: Colors.white,
      margin: EdgeInsets.only(top: 8.h),
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('热门商品', style: AppTextStyles.titleLarge),
          SizedBox(height: 12.h),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 12.w,
              mainAxisSpacing: 12.h,
              childAspectRatio: 0.75,
            ),
            itemCount: 4,
            itemBuilder: (context, index) {
              return Container(
                decoration: BoxDecoration(
                  color: AppColors.background,
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          color: AppColors.border,
                          borderRadius: BorderRadius.vertical(
                            top: Radius.circular(8.r),
                          ),
                        ),
                        child: Center(
                          child: Icon(
                            Icons.image,
                            size: 32.w,
                            color: AppColors.textTertiary,
                          ),
                        ),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.all(8.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '热门商品 ${index + 1}',
                            style: AppTextStyles.bodySmall,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            '¥199.99',
                            style: AppTextStyles.labelMedium.copyWith(
                              color: AppColors.price,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}
