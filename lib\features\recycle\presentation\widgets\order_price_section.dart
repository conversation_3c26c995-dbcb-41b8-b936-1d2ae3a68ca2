import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/features/recycle/domain/entities/recycle_order.dart';
import 'package:soko/core/enums/app_enums.dart';

/// 订单价格部分组件
class OrderPriceSection extends StatelessWidget {
  const OrderPriceSection({
    super.key,
    required this.order,
  });

  final RecycleOrder order;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.attach_money,
                size: 20.w,
                color: Theme.of(context).primaryColor,
              ),
              SizedBox(width: 8.w),
              Text(
                '价格信息',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          
          // 价格详情
          _buildPriceDetails(context),
        ],
      ),
    );
  }

  /// 构建价格详情
  Widget _buildPriceDetails(BuildContext context) {
    return Column(
      children: [
        // 预估价格
        _buildPriceRow(
          context,
          '预估价格',
          order.estimatedPrice,
          subtitle: '基于设备信息的初步评估',
          isEstimate: true,
        ),
        
        // 审核价格（如果有）
        if (order.reviewedPrice != null) ...[
          SizedBox(height: 12.h),
          _buildPriceRow(
            context,
            '审核价格',
            order.reviewedPrice!,
            subtitle: '专业评估师审核后的价格',
            isReviewed: true,
          ),
        ],
        
        // 最终价格（如果有）
        if (order.finalPrice != null) ...[
          SizedBox(height: 12.h),
          _buildPriceRow(
            context,
            '最终价格',
            order.finalPrice!,
            subtitle: '实际检测后的最终价格',
            isFinal: true,
          ),
        ],
        
        // 价格说明
        if (_shouldShowPriceNote()) ...[
          SizedBox(height: 16.h),
          _buildPriceNote(context),
        ],
      ],
    );
  }

  /// 构建价格行
  Widget _buildPriceRow(
    BuildContext context,
    String label,
    double price, {
    String? subtitle,
    bool isEstimate = false,
    bool isReviewed = false,
    bool isFinal = false,
  }) {
    Color priceColor;
    FontWeight fontWeight;
    
    if (isFinal) {
      priceColor = Theme.of(context).primaryColor;
      fontWeight = FontWeight.bold;
    } else if (isReviewed) {
      priceColor = Colors.blue[600]!;
      fontWeight = FontWeight.w600;
    } else {
      priceColor = Colors.grey[700]!;
      fontWeight = FontWeight.w500;
    }

    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: isFinal 
            ? Theme.of(context).primaryColor.withValues(alpha: 0.05)
            : Colors.grey[50],
        borderRadius: BorderRadius.circular(8.r),
        border: isFinal 
            ? Border.all(color: Theme.of(context).primaryColor.withValues(alpha: 0.2))
            : Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey[600],
                  ),
                ),
                if (subtitle != null) ...[
                  SizedBox(height: 2.h),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 11.sp,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ],
            ),
          ),
          Text(
            '¥${price.toStringAsFixed(2)}',
            style: TextStyle(
              fontSize: isFinal ? 18.sp : 16.sp,
              fontWeight: fontWeight,
              color: priceColor,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建价格说明
  Widget _buildPriceNote(BuildContext context) {
    String noteText = '';
    Color noteColor = Colors.blue[600]!;
    IconData noteIcon = Icons.info_outline;

    switch (order.statusEnum) {
      case RecycleOrderStatus.created:
        noteText = '预估价格仅供参考，实际价格以专业评估师审核为准';
        break;
      case RecycleOrderStatus.confirmed:
        noteText = '审核价格已确定，请确认是否同意此价格';
        noteColor = Colors.orange[600]!;
        noteIcon = Icons.warning_amber;
        break;
      case RecycleOrderStatus.picked:
        noteText = '设备已寄送，等待最终检测确认价格';
        break;
      case RecycleOrderStatus.evaluated:
        noteText = '最终检测完成，请确认价格无误';
        noteColor = Colors.green[600]!;
        noteIcon = Icons.check_circle_outline;
        break;
      case RecycleOrderStatus.completed:
        noteText = '交易已完成，款项已转账到您的账户';
        noteColor = Colors.green[600]!;
        noteIcon = Icons.check_circle;
        break;
      case RecycleOrderStatus.cancelled:
        noteText = '订单已取消，如有疑问请联系客服';
        noteColor = Colors.red[600]!;
        noteIcon = Icons.cancel_outlined;
        break;
    }

    if (noteText.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: noteColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: noteColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            noteIcon,
            size: 16.w,
            color: noteColor,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              noteText,
              style: TextStyle(
                fontSize: 12.sp,
                color: noteColor,
                height: 1.3,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 是否应该显示价格说明
  bool _shouldShowPriceNote() {
    return order.statusEnum != RecycleOrderStatus.created || 
           order.reviewedPrice != null || 
           order.finalPrice != null;
  }
}
