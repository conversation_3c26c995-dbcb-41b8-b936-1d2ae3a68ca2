import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/error/failures.dart';
import 'package:soko/core/usecases/usecase.dart';
import 'package:soko/features/order/data/repositories/order_repository_impl.dart';
import 'package:soko/features/order/domain/repositories/order_repository.dart';

/// 申请退款请求参数
class RefundRequest {

  RefundRequest({
    required this.orderId,
    required this.reason,
    this.description,
    this.images,
  });
  final String orderId;
  final String reason;
  final String? description;
  final List<String>? images;

  Map<String, dynamic> toJson() {
    return {
      'reason': reason,
      if (description != null) 'description': description,
      if (images != null) 'images': images,
    };
  }
}

/// 申请退款用例
class RequestRefundUseCase implements UseCase<bool, RefundRequest> {

  RequestRefundUseCase(this.repository);
  final OrderRepository repository;

  @override
  Future<Either<Failure, bool>> call(RefundRequest request) async {
    return repository.requestRefund(request.orderId, request.reason);
  }
}

/// RequestRefundUseCase 提供者
final requestRefundUseCaseProvider = Provider<RequestRefundUseCase>((ref) {
  final repository = ref.read(orderRepositoryProvider);
  return RequestRefundUseCase(repository);
});
