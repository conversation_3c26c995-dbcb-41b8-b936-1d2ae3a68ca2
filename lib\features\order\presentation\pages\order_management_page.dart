import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import 'package:soko/shared/presentation/widgets/custom_app_bar.dart';
import 'package:soko/shared/presentation/widgets/loading_widget.dart';
import 'package:soko/shared/presentation/widgets/error_retry_widget.dart';
import 'package:soko/shared/presentation/widgets/empty_state_widget.dart';
import 'package:soko/features/order/presentation/providers/order_management_provider.dart';
import 'package:soko/features/order/presentation/widgets/order_management_card.dart';
import 'package:soko/features/order/presentation/widgets/order_filter_panel.dart';
import 'package:soko/features/order/presentation/widgets/order_search_bar.dart';
import 'package:soko/features/order/presentation/widgets/order_batch_actions.dart';
import 'package:soko/features/order/presentation/widgets/order_statistics_card.dart';
import 'package:soko/features/order/domain/entities/order.dart';
import 'package:soko/core/enums/app_enums.dart';

/// 订单管理页面
class OrderManagementPage extends ConsumerStatefulWidget {
  const OrderManagementPage({super.key});

  @override
  ConsumerState<OrderManagementPage> createState() => _OrderManagementPageState();
}

class _OrderManagementPageState extends ConsumerState<OrderManagementPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();
  
  // 选择模式
  bool _isSelectionMode = false;
  final Set<String> _selectedOrderIds = <String>{};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
    _scrollController.addListener(_onScroll);
    
    // 页面初始化时加载订单数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(orderManagementProvider.notifier).loadOrders();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      ref.read(orderManagementProvider.notifier).loadMoreOrders();
    }
  }

  @override
  Widget build(BuildContext context) {
    final orderState = ref.watch(orderManagementProvider);

    return Scaffold(
      appBar: CustomAppBar(
        title: _isSelectionMode ? '已选择${_selectedOrderIds.length}个订单' : '订单管理',
        showBackButton: !_isSelectionMode,
        leading: _isSelectionMode ? IconButton(
          onPressed: _exitSelectionMode,
          icon: const Icon(Icons.close),
        ) : null,
        actions: [
          if (!_isSelectionMode) ...[
            IconButton(
              onPressed: _showFilterPanel,
              icon: const Icon(Icons.filter_list),
            ),
            IconButton(
              onPressed: _toggleSelectionMode,
              icon: const Icon(Icons.checklist),
            ),
          ],
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          onTap: (index) => _onTabChanged(index),
          tabs: const [
            Tab(text: '全部'),
            Tab(text: '待付款'),
            Tab(text: '待发货'),
            Tab(text: '已发货'),
            Tab(text: '已完成'),
            Tab(text: '已取消'),
          ],
        ),
      ),
      body: Column(
        children: [
          // 搜索栏
          OrderSearchBar(
            onSearch: (keyword) => _onSearch(keyword),
            onFilterTap: _showFilterPanel,
          ),
          
          // 统计卡片
          if (!_isSelectionMode)
            OrderStatisticsCard(),
          
          // 批量操作栏
          if (_isSelectionMode && _selectedOrderIds.isNotEmpty)
            OrderBatchActions(
              selectedCount: _selectedOrderIds.length,
              onBatchCancel: _batchCancelOrders,
              onBatchExport: _batchExportOrders,
              onBatchPrint: _batchPrintOrders,
            ),
          
          // 订单列表
          Expanded(
            child: orderState.when(
              data: (data) => _buildOrderList(data),
              loading: () => const LoadingWidget(),
              error: (error, stackTrace) => ErrorRetryWidget(
                message: error.toString(),
                onRetry: () => _retryLoad(),
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: !_isSelectionMode ? FloatingActionButton.extended(
        onPressed: _createNewOrder,
        icon: const Icon(Icons.add),
        label: const Text('新建订单'),
      ) : null,
    );
  }

  /// 构建订单列表
  Widget _buildOrderList(OrderManagementState data) {
    if (data.orders.isEmpty && !data.isLoading) {
      return EmptyStateWidget(
        icon: Icons.receipt_long_outlined,
        title: '暂无订单',
        subtitle: '当前条件下没有找到相关订单',
        actionText: '创建订单',
        onAction: _createNewOrder,
      );
    }

    return RefreshIndicator(
      onRefresh: () => _refreshOrders(),
      child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // 订单列表
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final order = data.orders[index];
                final isSelected = _selectedOrderIds.contains(order.id);
                
                return OrderManagementCard(
                  order: order,
                  isSelectionMode: _isSelectionMode,
                  isSelected: isSelected,
                  onTap: () => _onOrderTap(order),
                  onSelectionChanged: (selected) => _onOrderSelectionChanged(order.id, selected),
                  onStatusChanged: (newStatus) => _onOrderStatusChanged(order.id, newStatus),
                );
              },
              childCount: data.orders.length,
            ),
          ),
          
          // 加载更多指示器
          if (data.isLoadingMore)
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.all(16.w),
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              ),
            ),
          
          // 没有更多数据提示
          if (!data.hasMore && data.orders.isNotEmpty)
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.all(16.w),
                child: Center(
                  child: Text(
                    '已显示全部订单',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 标签页变化
  void _onTabChanged(int index) {
    OrderStatus? status;
    switch (index) {
      case 0: status = null; break; // 全部
      case 1: status = OrderStatus.pending; break; // 待付款
      case 2: status = OrderStatus.paid; break; // 待发货
      case 3: status = OrderStatus.shipped; break; // 已发货
      case 4: status = OrderStatus.completed; break; // 已完成
      case 5: status = OrderStatus.cancelled; break; // 已取消
    }
    
    ref.read(orderManagementProvider.notifier).filterByStatus(status);
  }

  /// 搜索订单
  void _onSearch(String keyword) {
    ref.read(orderManagementProvider.notifier).searchOrders(keyword);
  }

  /// 显示筛选面板
  void _showFilterPanel() {
    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => OrderFilterPanel(
        onFilterApplied: (filters) {
          ref.read(orderManagementProvider.notifier).applyFilters(filters);
        },
      ),
    );
  }

  /// 切换选择模式
  void _toggleSelectionMode() {
    setState(() {
      _isSelectionMode = !_isSelectionMode;
      if (!_isSelectionMode) {
        _selectedOrderIds.clear();
      }
    });
  }

  /// 退出选择模式
  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedOrderIds.clear();
    });
  }

  /// 订单点击
  void _onOrderTap(Order order) {
    if (_isSelectionMode) {
      _onOrderSelectionChanged(order.id, !_selectedOrderIds.contains(order.id));
    } else {
      context.push('/order/${order.id}');
    }
  }

  /// 订单选择状态变化
  void _onOrderSelectionChanged(String orderId, bool selected) {
    setState(() {
      if (selected) {
        _selectedOrderIds.add(orderId);
      } else {
        _selectedOrderIds.remove(orderId);
      }
    });
  }

  /// 订单状态变化
  void _onOrderStatusChanged(String orderId, OrderStatus newStatus) {
    ref.read(orderManagementProvider.notifier).updateOrderStatus(orderId, newStatus);
  }

  /// 批量取消订单
  void _batchCancelOrders() {
    ref.read(orderManagementProvider.notifier).batchCancelOrders(_selectedOrderIds.toList());
    _exitSelectionMode();
  }

  /// 批量导出订单
  void _batchExportOrders() {
    ref.read(orderManagementProvider.notifier).batchExportOrders(_selectedOrderIds.toList());
  }

  /// 批量打印订单
  void _batchPrintOrders() {
    ref.read(orderManagementProvider.notifier).batchPrintOrders(_selectedOrderIds.toList());
  }

  /// 重试加载
  void _retryLoad() {
    ref.read(orderManagementProvider.notifier).loadOrders();
  }

  /// 刷新订单列表
  Future<void> _refreshOrders() async {
    await ref.read(orderManagementProvider.notifier).refreshOrders();
  }

  /// 创建新订单
  void _createNewOrder() {
    context.push('/order/create');
  }
}
