import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/core/enums/app_enums.dart';
import 'package:soko/core/utils/color_utils.dart';
import 'package:soko/core/utils/date_utils.dart' as app_date_utils;
import 'package:soko/features/order/domain/entities/order.dart';
import 'package:soko/features/order/presentation/providers/order_status_provider.dart';

/// 订单状态时间线项
class OrderStatusTimelineItem {

  const OrderStatusTimelineItem({
    required this.status,
    required this.title,
    this.description,
    this.timestamp,
    required this.isActive,
    required this.isCompleted,
  });
  final OrderStatus status;
  final String title;
  final String? description;
  final DateTime? timestamp;
  final bool isActive;
  final bool isCompleted;
}

/// 订单状态时间线组件
class OrderStatusTimeline extends ConsumerWidget {

  const OrderStatusTimeline({
    super.key,
    required this.order,
    this.showTimestamp = true,
  });
  final Order order;
  final bool showTimestamp;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final statusInfo = ref.watch(orderStatusFromOrderProvider(order));
    final timelineItems = _buildTimelineItems(order, statusInfo);

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Text(
            '订单状态',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: 16.h),

          // 时间线
          ...timelineItems.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            final isLast = index == timelineItems.length - 1;

            return _buildTimelineItem(item, isLast, statusInfo);
          }),
        ],
      ),
    );
  }

  /// 构建时间线项
  Widget _buildTimelineItem(
    OrderStatusTimelineItem item,
    bool isLast,
    OrderStatusInfo statusInfo,
  ) {
    final color = item.isCompleted || item.isActive
        ? ColorUtils.hexToColor(statusInfo.color)
        : Colors.grey[400]!;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 时间线指示器
        Column(
          children: [
            Container(
              width: 12.w,
              height: 12.w,
              decoration: BoxDecoration(
                color: item.isCompleted
                    ? color
                    : item.isActive
                        ? Colors.white
                        : Colors.grey[300],
                border: Border.all(
                  color: color,
                  width: item.isActive ? 3 : 2,
                ),
                borderRadius: BorderRadius.circular(6.r),
              ),
              child: item.isCompleted
                  ? Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 8.w,
                    )
                  : null,
            ),
            if (!isLast)
              Container(
                width: 2.w,
                height: 40.h,
                color: item.isCompleted ? color : Colors.grey[300],
              ),
          ],
        ),

        SizedBox(width: 12.w),

        // 内容
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(bottom: isLast ? 0 : 16.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题和时间
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      item.title,
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: item.isActive ? FontWeight.w600 : FontWeight.w500,
                        color: item.isCompleted || item.isActive
                            ? Colors.black87
                            : Colors.grey[600],
                      ),
                    ),
                    if (showTimestamp && item.timestamp != null)
                      Text(
                        app_date_utils.DateUtils.formatDateTime(item.timestamp!),
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Colors.grey[500],
                        ),
                      ),
                  ],
                ),

                // 描述
                if (item.description != null) ...[
                  SizedBox(height: 4.h),
                  Text(
                    item.description!,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 构建时间线项列表
  List<OrderStatusTimelineItem> _buildTimelineItems(
    Order order,
    OrderStatusInfo statusInfo,
  ) {
    final items = <OrderStatusTimelineItem>[];
    final currentStatus = order.statusEnum;

    // 下单
    items.add(OrderStatusTimelineItem(
      status: OrderStatus.pending,
      title: '订单已创建',
      description: '您的订单已成功创建',
      timestamp: order.createdAt,
      isActive: currentStatus == OrderStatus.pending,
      isCompleted: _isStatusCompleted(OrderStatus.pending, currentStatus),
    ),);

    // 付款
    if (_shouldShowStatus(OrderStatus.paid, currentStatus)) {
      items.add(OrderStatusTimelineItem(
        status: OrderStatus.paid,
        title: '订单已付款',
        description: '感谢您的付款，商家正在准备发货',
        timestamp: order.payTime != null
            ? DateTime.fromMillisecondsSinceEpoch(order.payTime!)
            : null,
        isActive: currentStatus == OrderStatus.paid,
        isCompleted: _isStatusCompleted(OrderStatus.paid, currentStatus),
      ),);
    }

    // 发货
    if (_shouldShowStatus(OrderStatus.shipped, currentStatus)) {
      items.add(OrderStatusTimelineItem(
        status: OrderStatus.shipped,
        title: '订单已发货',
        description: order.trackingNo != null
            ? '快递单号：${order.trackingNo}'
            : '商品正在配送中，请耐心等待',
        timestamp: order.shipTime != null
            ? DateTime.fromMillisecondsSinceEpoch(order.shipTime!)
            : null,
        isActive: currentStatus == OrderStatus.shipped,
        isCompleted: _isStatusCompleted(OrderStatus.shipped, currentStatus),
      ),);
    }

    // 送达
    if (_shouldShowStatus(OrderStatus.delivered, currentStatus)) {
      items.add(OrderStatusTimelineItem(
        status: OrderStatus.delivered,
        title: '订单已送达',
        description: '商品已送达，请确认收货',
        isActive: currentStatus == OrderStatus.delivered,
        isCompleted: _isStatusCompleted(OrderStatus.delivered, currentStatus),
      ),);
    }

    // 完成
    if (_shouldShowStatus(OrderStatus.completed, currentStatus)) {
      items.add(OrderStatusTimelineItem(
        status: OrderStatus.completed,
        title: '订单已完成',
        description: '交易已完成，感谢您的购买',
        timestamp: order.receiveTime != null
            ? DateTime.fromMillisecondsSinceEpoch(order.receiveTime!)
            : null,
        isActive: currentStatus == OrderStatus.completed,
        isCompleted: _isStatusCompleted(OrderStatus.completed, currentStatus),
      ),);
    }

    // 取消
    if (currentStatus == OrderStatus.cancelled) {
      items.add(const OrderStatusTimelineItem(
        status: OrderStatus.cancelled,
        title: '订单已取消',
        description: '订单已被取消',
        isActive: true,
        isCompleted: true,
      ),);
    }

    // 退款
    if (currentStatus == OrderStatus.refunded) {
      items.add(const OrderStatusTimelineItem(
        status: OrderStatus.refunded,
        title: '订单已退款',
        description: '退款已处理完成',
        isActive: true,
        isCompleted: true,
      ),);
    }

    return items;
  }

  /// 判断是否应该显示某个状态
  bool _shouldShowStatus(OrderStatus status, OrderStatus currentStatus) {
    // 取消和退款状态不显示正常流程
    if (currentStatus == OrderStatus.cancelled || currentStatus == OrderStatus.refunded) {
      return false;
    }

    // 根据当前状态决定显示哪些状态
    switch (status) {
      case OrderStatus.paid:
        return currentStatus != OrderStatus.pending;
      case OrderStatus.shipped:
        return currentStatus == OrderStatus.shipped ||
               currentStatus == OrderStatus.delivered ||
               currentStatus == OrderStatus.completed;
      case OrderStatus.delivered:
        return currentStatus == OrderStatus.delivered ||
               currentStatus == OrderStatus.completed;
      case OrderStatus.completed:
        return currentStatus == OrderStatus.completed;
      default:
        return true;
    }
  }

  /// 判断状态是否已完成
  bool _isStatusCompleted(OrderStatus status, OrderStatus currentStatus) {
    final statusPriority = _getStatusPriority(status);
    final currentPriority = _getStatusPriority(currentStatus);
    return statusPriority < currentPriority;
  }

  /// 获取状态优先级
  int _getStatusPriority(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 1;
      case OrderStatus.paid:
        return 2;
      case OrderStatus.shipped:
        return 3;
      case OrderStatus.delivered:
        return 4;
      case OrderStatus.completed:
        return 5;
      case OrderStatus.cancelled:
        return 99;
      case OrderStatus.refunded:
        return 99;
    }
  }
}
