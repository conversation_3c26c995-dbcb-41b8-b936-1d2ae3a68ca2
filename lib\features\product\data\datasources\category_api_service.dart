import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

import 'package:soko/core/network/api_response.dart';
import 'package:soko/features/product/domain/entities/category.dart';

part 'category_api_service.g.dart';

/// 分类API服务
@RestApi()
abstract class CategoryApiService {
  factory CategoryApiService(Dio dio, {String baseUrl}) = _CategoryApiService;

  /// 获取分类树
  @GET('/categories/tree')
  Future<ApiResponse<List<ProductCategory>>> getCategoryTree({
    @Query('level') int? level,
    @Query('includeInactive') bool includeInactive = false,
  });

  /// 获取分类列表
  @GET('/categories')
  Future<ApiResponse<List<ProductCategory>>> getCategoryList({
    @Query('parentId') String? parentId,
    @Query('level') int? level,
    @Query('acgType') String? acgType,
    @Query('hasProducts') bool? hasProducts,
    @Query('includeInactive') bool includeInactive = false,
  });

  /// 获取分类详情
  @GET('/categories/{id}')
  Future<ApiResponse<ProductCategory>> getCategoryDetail(
    @Path('id') String id,
  );

  /// 获取热门分类
  @GET('/categories/hot')
  Future<ApiResponse<List<ProductCategory>>> getHotCategories({
    @Query('limit') int limit = 10,
  });

  /// 获取ACG分类
  @GET('/categories/acg')
  Future<ApiResponse<List<ProductCategory>>> getAcgCategories();

  /// 搜索分类
  @GET('/categories/search')
  Future<ApiResponse<List<ProductCategory>>> searchCategories({
    @Query('keyword') required String keyword,
    @Query('limit') int limit = 20,
  });
}

/// 分类API服务实现（模拟数据）
class MockCategoryApiService implements CategoryApiService {
  @override
  Dio get dio => throw UnimplementedError();

  @override
  Future<ApiResponse<List<ProductCategory>>> getCategoryTree({
    int? level,
    bool includeInactive = false,
  }) async {
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 500));

    final categories = _getMockCategories();
    return ApiResponse.success(categories);
  }

  @override
  Future<ApiResponse<List<ProductCategory>>> getCategoryList({
    String? parentId,
    int? level,
    String? acgType,
    bool? hasProducts,
    bool includeInactive = false,
  }) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final allCategories = _getMockCategories();
    var filteredCategories = allCategories;

    if (parentId != null) {
      filteredCategories = allCategories
          .where((cat) => cat.parentId == parentId)
          .toList();
    }

    if (level != null) {
      filteredCategories = filteredCategories
          .where((cat) => cat.level == level)
          .toList();
    }

    if (acgType != null) {
      filteredCategories = filteredCategories
          .where((cat) => cat.code == acgType)
          .toList();
    }

    if (hasProducts == true) {
      filteredCategories = filteredCategories
          .where((cat) => (cat.productCount ?? 0) > 0)
          .toList();
    }

    return ApiResponse.success(filteredCategories);
  }

  @override
  Future<ApiResponse<ProductCategory>> getCategoryDetail(String id) async {
    await Future.delayed(const Duration(milliseconds: 200));

    final categories = _getMockCategories();
    final category = categories.firstWhere(
      (cat) => cat.id == id,
      orElse: () => throw Exception('Category not found'),
    );

    return ApiResponse.success(category);
  }

  @override
  Future<ApiResponse<List<ProductCategory>>> getHotCategories({
    int limit = 10,
  }) async {
    await Future.delayed(const Duration(milliseconds: 300));

    final hotCategories = _getMockCategories()
        .where((cat) => (cat.productCount ?? 0) > 50)
        .take(limit)
        .toList();

    return ApiResponse.success(hotCategories);
  }

  @override
  Future<ApiResponse<List<ProductCategory>>> getAcgCategories() async {
    await Future.delayed(const Duration(milliseconds: 200));

    final acgCategories = _getMockCategories()
        .where((cat) => cat.isAcgCategory)
        .toList();

    return ApiResponse.success(acgCategories);
  }

  @override
  Future<ApiResponse<List<ProductCategory>>> searchCategories({
    required String keyword,
    int limit = 20,
  }) async {
    await Future.delayed(const Duration(milliseconds: 400));

    final searchResults = _getMockCategories()
        .where((cat) => cat.name.contains(keyword) || 
                       cat.description?.contains(keyword) == true,)
        .take(limit)
        .toList();

    return ApiResponse.success(searchResults);
  }

  /// 获取模拟分类数据
  List<ProductCategory> _getMockCategories() {
    final now = DateTime.now().millisecondsSinceEpoch;
    
    return [
      // 根分类
      ProductCategory(
        id: '1',
        name: '奥特曼',
        code: 'ultraman',
        description: '奥特曼系列商品',
        icon: '🦸‍♂️',
        level: 0,
        sort: 1,
        isActive: true,
        productCount: 150,
        createTime: now,
        updateTime: now,
        children: [
          ProductCategory(
            id: '11',
            name: '奥特曼手办',
            code: 'ultraman_figure',
            parentId: '1',
            level: 1,
            sort: 1,
            isActive: true,
            productCount: 80,
            createTime: now,
            updateTime: now,
          ),
          ProductCategory(
            id: '12',
            name: '变身器',
            code: 'ultraman_transformer',
            parentId: '1',
            level: 1,
            sort: 2,
            isActive: true,
            productCount: 45,
            createTime: now,
            updateTime: now,
          ),
        ],
      ),
      ProductCategory(
        id: '2',
        name: '假面骑士',
        code: 'kamen_rider',
        description: '假面骑士系列商品',
        icon: '🏍️',
        level: 0,
        sort: 2,
        isActive: true,
        productCount: 120,
        createTime: now,
        updateTime: now,
        children: [
          ProductCategory(
            id: '21',
            name: '腰带',
            code: 'kamen_rider_belt',
            parentId: '2',
            level: 1,
            sort: 1,
            isActive: true,
            productCount: 60,
            createTime: now,
            updateTime: now,
          ),
          ProductCategory(
            id: '22',
            name: '手办模型',
            code: 'kamen_rider_figure',
            parentId: '2',
            level: 1,
            sort: 2,
            isActive: true,
            productCount: 40,
            createTime: now,
            updateTime: now,
          ),
        ],
      ),
      ProductCategory(
        id: '3',
        name: '高达',
        code: 'gundam',
        description: '高达模型系列',
        icon: '🚀',
        level: 0,
        sort: 3,
        isActive: true,
        productCount: 200,
        createTime: now,
        updateTime: now,
        children: [
          ProductCategory(
            id: '31',
            name: 'RG高达',
            code: 'rg_gundam',
            parentId: '3',
            level: 1,
            sort: 1,
            isActive: true,
            productCount: 80,
            createTime: now,
            updateTime: now,
          ),
          ProductCategory(
            id: '32',
            name: 'MG高达',
            code: 'mg_gundam',
            parentId: '3',
            level: 1,
            sort: 2,
            isActive: true,
            productCount: 70,
            createTime: now,
            updateTime: now,
          ),
        ],
      ),
      ProductCategory(
        id: '4',
        name: '手办',
        code: 'figure',
        description: '各类手办模型',
        icon: '🎭',
        level: 0,
        sort: 4,
        isActive: true,
        productCount: 300,
        createTime: now,
        updateTime: now,
      ),
    ];
  }
}
