import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Widget扩展方法
extension WidgetExtension on Widget {
  /// 添加内边距
  Widget padding(EdgeInsetsGeometry padding) {
    return Padding(padding: padding, child: this);
  }

  /// 添加对称内边距
  Widget paddingSymmetric({double horizontal = 0, double vertical = 0}) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: horizontal.w,
        vertical: vertical.h,
      ),
      child: this,
    );
  }

  /// 添加所有方向相同的内边距
  Widget paddingAll(double padding) {
    return Padding(
      padding: EdgeInsets.all(padding.w),
      child: this,
    );
  }

  /// 添加指定方向的内边距
  Widget paddingOnly({
    double left = 0,
    double top = 0,
    double right = 0,
    double bottom = 0,
  }) {
    return Padding(
      padding: EdgeInsets.only(
        left: left.w,
        top: top.h,
        right: right.w,
        bottom: bottom.h,
      ),
      child: this,
    );
  }

  /// 添加外边距
  Widget margin(EdgeInsetsGeometry margin) {
    return Container(margin: margin, child: this);
  }

  /// 添加对称外边距
  Widget marginSymmetric({double horizontal = 0, double vertical = 0}) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: horizontal.w,
        vertical: vertical.h,
      ),
      child: this,
    );
  }

  /// 添加所有方向相同的外边距
  Widget marginAll(double margin) {
    return Container(
      margin: EdgeInsets.all(margin.w),
      child: this,
    );
  }

  /// 添加指定方向的外边距
  Widget marginOnly({
    double left = 0,
    double top = 0,
    double right = 0,
    double bottom = 0,
  }) {
    return Container(
      margin: EdgeInsets.only(
        left: left.w,
        top: top.h,
        right: right.w,
        bottom: bottom.h,
      ),
      child: this,
    );
  }

  /// 居中对齐
  Widget center() {
    return Center(child: this);
  }

  /// 左对齐
  Widget alignLeft() {
    return Align(alignment: Alignment.centerLeft, child: this);
  }

  /// 右对齐
  Widget alignRight() {
    return Align(alignment: Alignment.centerRight, child: this);
  }

  /// 顶部对齐
  Widget alignTop() {
    return Align(alignment: Alignment.topCenter, child: this);
  }

  /// 底部对齐
  Widget alignBottom() {
    return Align(alignment: Alignment.bottomCenter, child: this);
  }

  /// 自定义对齐
  Widget align(Alignment alignment) {
    return Align(alignment: alignment, child: this);
  }

  /// 扩展填充
  Widget expanded({int flex = 1}) {
    return Expanded(flex: flex, child: this);
  }

  /// 灵活布局
  Widget flexible({int flex = 1, FlexFit fit = FlexFit.loose}) {
    return Flexible(flex: flex, fit: fit, child: this);
  }

  /// 设置固定大小
  Widget size({double? width, double? height}) {
    return SizedBox(
      width: width?.w,
      height: height?.h,
      child: this,
    );
  }

  /// 设置宽度
  Widget width(double width) {
    return SizedBox(width: width.w, child: this);
  }

  /// 设置高度
  Widget height(double height) {
    return SizedBox(height: height.h, child: this);
  }

  /// 设置最小约束
  Widget constrained({
    double? minWidth,
    double? maxWidth,
    double? minHeight,
    double? maxHeight,
  }) {
    return ConstrainedBox(
      constraints: BoxConstraints(
        minWidth: minWidth?.w ?? 0,
        maxWidth: maxWidth?.w ?? double.infinity,
        minHeight: minHeight?.h ?? 0,
        maxHeight: maxHeight?.h ?? double.infinity,
      ),
      child: this,
    );
  }

  /// 添加圆角
  Widget rounded({double radius = 8}) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(radius.r),
      child: this,
    );
  }

  /// 添加圆形裁剪
  Widget circular() {
    return ClipOval(child: this);
  }

  /// 添加阴影
  Widget shadow({
    Color color = Colors.black26,
    double blurRadius = 4,
    Offset offset = const Offset(0, 2),
  }) {
    return Container(
      decoration: BoxDecoration(
        boxShadow: [
          BoxShadow(
            color: color,
            blurRadius: blurRadius,
            offset: offset,
          ),
        ],
      ),
      child: this,
    );
  }

  /// 添加背景色
  Widget backgroundColor(Color color) {
    return ColoredBox(
      color: color,
      child: this,
    );
  }

  /// 添加装饰容器
  Widget decorated({
    Color? color,
    DecorationImage? image,
    Border? border,
    BorderRadiusGeometry? borderRadius,
    List<BoxShadow>? boxShadow,
    Gradient? gradient,
    BlendMode? backgroundBlendMode,
    BoxShape shape = BoxShape.rectangle,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: color,
        image: image,
        border: border,
        borderRadius: borderRadius,
        boxShadow: boxShadow,
        gradient: gradient,
        backgroundBlendMode: backgroundBlendMode,
        shape: shape,
      ),
      child: this,
    );
  }

  /// 添加手势检测
  Widget onTap(VoidCallback? onTap) {
    return GestureDetector(onTap: onTap, child: this);
  }

  /// 添加长按手势
  Widget onLongPress(VoidCallback? onLongPress) {
    return GestureDetector(onLongPress: onLongPress, child: this);
  }

  /// 添加双击手势
  Widget onDoubleTap(VoidCallback? onDoubleTap) {
    return GestureDetector(onDoubleTap: onDoubleTap, child: this);
  }

  /// 添加涟漪效果
  Widget ripple({VoidCallback? onTap, Color? splashColor}) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        splashColor: splashColor,
        child: this,
      ),
    );
  }

  /// 添加透明度
  Widget opacity(double opacity) {
    return Opacity(opacity: opacity, child: this);
  }

  /// 添加旋转
  Widget rotate(double angle) {
    return Transform.rotate(angle: angle, child: this);
  }

  /// 添加缩放
  Widget scale(double scale) {
    return Transform.scale(scale: scale, child: this);
  }

  /// 添加平移
  Widget translate({double x = 0, double y = 0}) {
    return Transform.translate(
      offset: Offset(x.w, y.h),
      child: this,
    );
  }

  /// 条件显示
  Widget visible(bool visible) {
    return Visibility(visible: visible, child: this);
  }

  /// 条件显示（保持空间）
  Widget invisible(bool invisible) {
    return Visibility(
      visible: !invisible,
      maintainSize: true,
      maintainAnimation: true,
      maintainState: true,
      child: this,
    );
  }

  /// 安全区域
  Widget safeArea({
    bool top = true,
    bool bottom = true,
    bool left = true,
    bool right = true,
  }) {
    return SafeArea(
      top: top,
      bottom: bottom,
      left: left,
      right: right,
      child: this,
    );
  }

  /// 滚动视图
  Widget scrollable({
    Axis scrollDirection = Axis.vertical,
    bool reverse = false,
    ScrollPhysics? physics,
  }) {
    return SingleChildScrollView(
      scrollDirection: scrollDirection,
      reverse: reverse,
      physics: physics,
      child: this,
    );
  }

  /// 英雄动画
  Widget hero(String tag) {
    return Hero(tag: tag, child: this);
  }
}
