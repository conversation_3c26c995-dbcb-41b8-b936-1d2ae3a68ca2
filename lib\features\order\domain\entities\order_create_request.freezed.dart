// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'order_create_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OrderCreateRequest _$OrderCreateRequestFromJson(Map<String, dynamic> json) {
  return _OrderCreateRequest.fromJson(json);
}

/// @nodoc
mixin _$OrderCreateRequest {
  /// 购物车商品项列表
  List<OrderCreateItem> get items => throw _privateConstructorUsedError;

  /// 收货地址ID
  String get addressId => throw _privateConstructorUsedError;

  /// 支付方式
  PaymentMethod get paymentMethod => throw _privateConstructorUsedError;

  /// 优惠券ID（可选）
  String? get couponId => throw _privateConstructorUsedError;

  /// 订单备注（可选）
  String? get note => throw _privateConstructorUsedError;

  /// 配送方式
  DeliveryMethod get deliveryMethod => throw _privateConstructorUsedError;

  /// 发票类型（可选）
  InvoiceType? get invoiceType => throw _privateConstructorUsedError;

  /// 发票抬头（可选）
  String? get invoiceTitle => throw _privateConstructorUsedError;

  /// Serializes this OrderCreateRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrderCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrderCreateRequestCopyWith<OrderCreateRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderCreateRequestCopyWith<$Res> {
  factory $OrderCreateRequestCopyWith(
          OrderCreateRequest value, $Res Function(OrderCreateRequest) then) =
      _$OrderCreateRequestCopyWithImpl<$Res, OrderCreateRequest>;
  @useResult
  $Res call(
      {List<OrderCreateItem> items,
      String addressId,
      PaymentMethod paymentMethod,
      String? couponId,
      String? note,
      DeliveryMethod deliveryMethod,
      InvoiceType? invoiceType,
      String? invoiceTitle});
}

/// @nodoc
class _$OrderCreateRequestCopyWithImpl<$Res, $Val extends OrderCreateRequest>
    implements $OrderCreateRequestCopyWith<$Res> {
  _$OrderCreateRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrderCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = null,
    Object? addressId = null,
    Object? paymentMethod = null,
    Object? couponId = freezed,
    Object? note = freezed,
    Object? deliveryMethod = null,
    Object? invoiceType = freezed,
    Object? invoiceTitle = freezed,
  }) {
    return _then(_value.copyWith(
      items: null == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<OrderCreateItem>,
      addressId: null == addressId
          ? _value.addressId
          : addressId // ignore: cast_nullable_to_non_nullable
              as String,
      paymentMethod: null == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as PaymentMethod,
      couponId: freezed == couponId
          ? _value.couponId
          : couponId // ignore: cast_nullable_to_non_nullable
              as String?,
      note: freezed == note
          ? _value.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
      deliveryMethod: null == deliveryMethod
          ? _value.deliveryMethod
          : deliveryMethod // ignore: cast_nullable_to_non_nullable
              as DeliveryMethod,
      invoiceType: freezed == invoiceType
          ? _value.invoiceType
          : invoiceType // ignore: cast_nullable_to_non_nullable
              as InvoiceType?,
      invoiceTitle: freezed == invoiceTitle
          ? _value.invoiceTitle
          : invoiceTitle // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OrderCreateRequestImplCopyWith<$Res>
    implements $OrderCreateRequestCopyWith<$Res> {
  factory _$$OrderCreateRequestImplCopyWith(_$OrderCreateRequestImpl value,
          $Res Function(_$OrderCreateRequestImpl) then) =
      __$$OrderCreateRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<OrderCreateItem> items,
      String addressId,
      PaymentMethod paymentMethod,
      String? couponId,
      String? note,
      DeliveryMethod deliveryMethod,
      InvoiceType? invoiceType,
      String? invoiceTitle});
}

/// @nodoc
class __$$OrderCreateRequestImplCopyWithImpl<$Res>
    extends _$OrderCreateRequestCopyWithImpl<$Res, _$OrderCreateRequestImpl>
    implements _$$OrderCreateRequestImplCopyWith<$Res> {
  __$$OrderCreateRequestImplCopyWithImpl(_$OrderCreateRequestImpl _value,
      $Res Function(_$OrderCreateRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = null,
    Object? addressId = null,
    Object? paymentMethod = null,
    Object? couponId = freezed,
    Object? note = freezed,
    Object? deliveryMethod = null,
    Object? invoiceType = freezed,
    Object? invoiceTitle = freezed,
  }) {
    return _then(_$OrderCreateRequestImpl(
      items: null == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<OrderCreateItem>,
      addressId: null == addressId
          ? _value.addressId
          : addressId // ignore: cast_nullable_to_non_nullable
              as String,
      paymentMethod: null == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as PaymentMethod,
      couponId: freezed == couponId
          ? _value.couponId
          : couponId // ignore: cast_nullable_to_non_nullable
              as String?,
      note: freezed == note
          ? _value.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
      deliveryMethod: null == deliveryMethod
          ? _value.deliveryMethod
          : deliveryMethod // ignore: cast_nullable_to_non_nullable
              as DeliveryMethod,
      invoiceType: freezed == invoiceType
          ? _value.invoiceType
          : invoiceType // ignore: cast_nullable_to_non_nullable
              as InvoiceType?,
      invoiceTitle: freezed == invoiceTitle
          ? _value.invoiceTitle
          : invoiceTitle // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrderCreateRequestImpl implements _OrderCreateRequest {
  const _$OrderCreateRequestImpl(
      {required final List<OrderCreateItem> items,
      required this.addressId,
      required this.paymentMethod,
      this.couponId,
      this.note,
      this.deliveryMethod = DeliveryMethod.standard,
      this.invoiceType,
      this.invoiceTitle})
      : _items = items;

  factory _$OrderCreateRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderCreateRequestImplFromJson(json);

  /// 购物车商品项列表
  final List<OrderCreateItem> _items;

  /// 购物车商品项列表
  @override
  List<OrderCreateItem> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  /// 收货地址ID
  @override
  final String addressId;

  /// 支付方式
  @override
  final PaymentMethod paymentMethod;

  /// 优惠券ID（可选）
  @override
  final String? couponId;

  /// 订单备注（可选）
  @override
  final String? note;

  /// 配送方式
  @override
  @JsonKey()
  final DeliveryMethod deliveryMethod;

  /// 发票类型（可选）
  @override
  final InvoiceType? invoiceType;

  /// 发票抬头（可选）
  @override
  final String? invoiceTitle;

  @override
  String toString() {
    return 'OrderCreateRequest(items: $items, addressId: $addressId, paymentMethod: $paymentMethod, couponId: $couponId, note: $note, deliveryMethod: $deliveryMethod, invoiceType: $invoiceType, invoiceTitle: $invoiceTitle)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderCreateRequestImpl &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            (identical(other.addressId, addressId) ||
                other.addressId == addressId) &&
            (identical(other.paymentMethod, paymentMethod) ||
                other.paymentMethod == paymentMethod) &&
            (identical(other.couponId, couponId) ||
                other.couponId == couponId) &&
            (identical(other.note, note) || other.note == note) &&
            (identical(other.deliveryMethod, deliveryMethod) ||
                other.deliveryMethod == deliveryMethod) &&
            (identical(other.invoiceType, invoiceType) ||
                other.invoiceType == invoiceType) &&
            (identical(other.invoiceTitle, invoiceTitle) ||
                other.invoiceTitle == invoiceTitle));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_items),
      addressId,
      paymentMethod,
      couponId,
      note,
      deliveryMethod,
      invoiceType,
      invoiceTitle);

  /// Create a copy of OrderCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderCreateRequestImplCopyWith<_$OrderCreateRequestImpl> get copyWith =>
      __$$OrderCreateRequestImplCopyWithImpl<_$OrderCreateRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderCreateRequestImplToJson(
      this,
    );
  }
}

abstract class _OrderCreateRequest implements OrderCreateRequest {
  const factory _OrderCreateRequest(
      {required final List<OrderCreateItem> items,
      required final String addressId,
      required final PaymentMethod paymentMethod,
      final String? couponId,
      final String? note,
      final DeliveryMethod deliveryMethod,
      final InvoiceType? invoiceType,
      final String? invoiceTitle}) = _$OrderCreateRequestImpl;

  factory _OrderCreateRequest.fromJson(Map<String, dynamic> json) =
      _$OrderCreateRequestImpl.fromJson;

  /// 购物车商品项列表
  @override
  List<OrderCreateItem> get items;

  /// 收货地址ID
  @override
  String get addressId;

  /// 支付方式
  @override
  PaymentMethod get paymentMethod;

  /// 优惠券ID（可选）
  @override
  String? get couponId;

  /// 订单备注（可选）
  @override
  String? get note;

  /// 配送方式
  @override
  DeliveryMethod get deliveryMethod;

  /// 发票类型（可选）
  @override
  InvoiceType? get invoiceType;

  /// 发票抬头（可选）
  @override
  String? get invoiceTitle;

  /// Create a copy of OrderCreateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderCreateRequestImplCopyWith<_$OrderCreateRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

OrderCreateItem _$OrderCreateItemFromJson(Map<String, dynamic> json) {
  return _OrderCreateItem.fromJson(json);
}

/// @nodoc
mixin _$OrderCreateItem {
  /// 商品ID
  String get productId => throw _privateConstructorUsedError;

  /// SKU ID
  String get skuId => throw _privateConstructorUsedError;

  /// 数量
  int get quantity => throw _privateConstructorUsedError;

  /// 单价
  double get price => throw _privateConstructorUsedError;

  /// 商品名称
  String get productName => throw _privateConstructorUsedError;

  /// SKU名称（规格）
  String? get skuName => throw _privateConstructorUsedError;

  /// 商品图片
  String? get productImage => throw _privateConstructorUsedError;

  /// Serializes this OrderCreateItem to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OrderCreateItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OrderCreateItemCopyWith<OrderCreateItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OrderCreateItemCopyWith<$Res> {
  factory $OrderCreateItemCopyWith(
          OrderCreateItem value, $Res Function(OrderCreateItem) then) =
      _$OrderCreateItemCopyWithImpl<$Res, OrderCreateItem>;
  @useResult
  $Res call(
      {String productId,
      String skuId,
      int quantity,
      double price,
      String productName,
      String? skuName,
      String? productImage});
}

/// @nodoc
class _$OrderCreateItemCopyWithImpl<$Res, $Val extends OrderCreateItem>
    implements $OrderCreateItemCopyWith<$Res> {
  _$OrderCreateItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OrderCreateItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = null,
    Object? skuId = null,
    Object? quantity = null,
    Object? price = null,
    Object? productName = null,
    Object? skuName = freezed,
    Object? productImage = freezed,
  }) {
    return _then(_value.copyWith(
      productId: null == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String,
      skuId: null == skuId
          ? _value.skuId
          : skuId // ignore: cast_nullable_to_non_nullable
              as String,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      productName: null == productName
          ? _value.productName
          : productName // ignore: cast_nullable_to_non_nullable
              as String,
      skuName: freezed == skuName
          ? _value.skuName
          : skuName // ignore: cast_nullable_to_non_nullable
              as String?,
      productImage: freezed == productImage
          ? _value.productImage
          : productImage // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OrderCreateItemImplCopyWith<$Res>
    implements $OrderCreateItemCopyWith<$Res> {
  factory _$$OrderCreateItemImplCopyWith(_$OrderCreateItemImpl value,
          $Res Function(_$OrderCreateItemImpl) then) =
      __$$OrderCreateItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String productId,
      String skuId,
      int quantity,
      double price,
      String productName,
      String? skuName,
      String? productImage});
}

/// @nodoc
class __$$OrderCreateItemImplCopyWithImpl<$Res>
    extends _$OrderCreateItemCopyWithImpl<$Res, _$OrderCreateItemImpl>
    implements _$$OrderCreateItemImplCopyWith<$Res> {
  __$$OrderCreateItemImplCopyWithImpl(
      _$OrderCreateItemImpl _value, $Res Function(_$OrderCreateItemImpl) _then)
      : super(_value, _then);

  /// Create a copy of OrderCreateItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = null,
    Object? skuId = null,
    Object? quantity = null,
    Object? price = null,
    Object? productName = null,
    Object? skuName = freezed,
    Object? productImage = freezed,
  }) {
    return _then(_$OrderCreateItemImpl(
      productId: null == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String,
      skuId: null == skuId
          ? _value.skuId
          : skuId // ignore: cast_nullable_to_non_nullable
              as String,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as int,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      productName: null == productName
          ? _value.productName
          : productName // ignore: cast_nullable_to_non_nullable
              as String,
      skuName: freezed == skuName
          ? _value.skuName
          : skuName // ignore: cast_nullable_to_non_nullable
              as String?,
      productImage: freezed == productImage
          ? _value.productImage
          : productImage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OrderCreateItemImpl implements _OrderCreateItem {
  const _$OrderCreateItemImpl(
      {required this.productId,
      required this.skuId,
      required this.quantity,
      required this.price,
      required this.productName,
      this.skuName,
      this.productImage});

  factory _$OrderCreateItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$OrderCreateItemImplFromJson(json);

  /// 商品ID
  @override
  final String productId;

  /// SKU ID
  @override
  final String skuId;

  /// 数量
  @override
  final int quantity;

  /// 单价
  @override
  final double price;

  /// 商品名称
  @override
  final String productName;

  /// SKU名称（规格）
  @override
  final String? skuName;

  /// 商品图片
  @override
  final String? productImage;

  @override
  String toString() {
    return 'OrderCreateItem(productId: $productId, skuId: $skuId, quantity: $quantity, price: $price, productName: $productName, skuName: $skuName, productImage: $productImage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OrderCreateItemImpl &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.skuId, skuId) || other.skuId == skuId) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.productName, productName) ||
                other.productName == productName) &&
            (identical(other.skuName, skuName) || other.skuName == skuName) &&
            (identical(other.productImage, productImage) ||
                other.productImage == productImage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, productId, skuId, quantity,
      price, productName, skuName, productImage);

  /// Create a copy of OrderCreateItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OrderCreateItemImplCopyWith<_$OrderCreateItemImpl> get copyWith =>
      __$$OrderCreateItemImplCopyWithImpl<_$OrderCreateItemImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OrderCreateItemImplToJson(
      this,
    );
  }
}

abstract class _OrderCreateItem implements OrderCreateItem {
  const factory _OrderCreateItem(
      {required final String productId,
      required final String skuId,
      required final int quantity,
      required final double price,
      required final String productName,
      final String? skuName,
      final String? productImage}) = _$OrderCreateItemImpl;

  factory _OrderCreateItem.fromJson(Map<String, dynamic> json) =
      _$OrderCreateItemImpl.fromJson;

  /// 商品ID
  @override
  String get productId;

  /// SKU ID
  @override
  String get skuId;

  /// 数量
  @override
  int get quantity;

  /// 单价
  @override
  double get price;

  /// 商品名称
  @override
  String get productName;

  /// SKU名称（规格）
  @override
  String? get skuName;

  /// 商品图片
  @override
  String? get productImage;

  /// Create a copy of OrderCreateItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OrderCreateItemImplCopyWith<_$OrderCreateItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
