/// 应用程序配置类
class AppConfig {
  // 应用信息
  static const String appName = '中古虾';
  static const String appVersion = '1.0.0';
  static const String appDescription = '特摄模玩主题电商平台';
  
  // API配置
  static const String baseUrl = 'https://api.soko.com/api';
  static const String devBaseUrl = 'http://localhost:8080';
  static const int connectTimeout = 60000;
  static const int receiveTimeout = 60000;
  
  // 存储键名
  static const String tokenKey = 'token';
  static const String userInfoKey = 'userInfo';
  static const String languageKey = 'language';
  static const String themeKey = 'theme';
  
  // 分页配置
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // 图片配置
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const List<String> supportedImageTypes = ['jpg', 'jpeg', 'png', 'webp'];
  
  // 缓存配置
  static const int imageCacheMaxAge = 7 * 24 * 60 * 60; // 7天
  static const int apiCacheMaxAge = 5 * 60; // 5分钟
  
  // 业务配置
  static const double minOrderAmount = 0.01;
  static const double maxOrderAmount = 99999.99;
  static const int maxCartItems = 99;
  
  // 获取当前环境的API地址
  static String get apiBaseUrl {
    const isProduction = bool.fromEnvironment('dart.vm.product');
    return isProduction ? baseUrl : devBaseUrl;
  }
  
  // 是否为调试模式
  static bool get isDebug {
    return !const bool.fromEnvironment('dart.vm.product');
  }
}
