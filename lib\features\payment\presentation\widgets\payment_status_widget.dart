import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/shared/presentation/widgets/custom_button.dart';
import 'package:soko/features/payment/domain/entities/payment_request.dart';

/// 支付状态组件
class PaymentStatusWidget extends StatelessWidget {

  const PaymentStatusWidget({
    super.key,
    required this.paymentResponse,
    this.onRetry,
    this.onBackToOrder,
  });
  final PaymentResponse paymentResponse;
  final VoidCallback? onRetry;
  final VoidCallback? onBackToOrder;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(24.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 状态图标
          _buildStatusIcon(),
          SizedBox(height: 24.h),

          // 状态标题
          Text(
            _getStatusTitle(),
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.w600,
              color: _getStatusColor(),
            ),
          ),
          SizedBox(height: 12.h),

          // 状态描述
          Text(
            _getStatusDescription(),
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.grey[600],
              height: 1.5,
            ),
          ),
          SizedBox(height: 8.h),

          // 支付金额
          if (paymentResponse.amount > 0) ...[
            Text(
              '支付金额：¥${(paymentResponse.amount / 100).toStringAsFixed(2)}',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            SizedBox(height: 8.h),
          ],

          // 支付订单号
          Text(
            '支付单号：${paymentResponse.paymentOrderNo}',
            style: TextStyle(
              fontSize: 12.sp,
              color: Colors.grey[500],
            ),
          ),
          SizedBox(height: 32.h),

          // 操作按钮
          _buildActionButtons(),

          // 支付中的加载指示器
          if (paymentResponse.status == PaymentStatus.processing ||
              paymentResponse.status == PaymentStatus.pending) ...[
            SizedBox(height: 24.h),
            const CircularProgressIndicator(),
            SizedBox(height: 12.h),
            Text(
              '正在查询支付结果...',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[600],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建状态图标
  Widget _buildStatusIcon() {
    IconData iconData;
    Color iconColor;

    switch (paymentResponse.status) {
      case PaymentStatus.success:
        iconData = Icons.check_circle;
        iconColor = Colors.green;
      case PaymentStatus.failed:
        iconData = Icons.error;
        iconColor = Colors.red;
      case PaymentStatus.cancelled:
        iconData = Icons.cancel;
        iconColor = Colors.orange;
      case PaymentStatus.timeout:
        iconData = Icons.access_time;
        iconColor = Colors.grey;
      case PaymentStatus.processing:
      case PaymentStatus.pending:
        iconData = Icons.hourglass_empty;
        iconColor = Colors.blue;
    }

    return Container(
      width: 80.w,
      height: 80.w,
      decoration: BoxDecoration(
        color: iconColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(40.r),
      ),
      child: Icon(
        iconData,
        size: 48.w,
        color: iconColor,
      ),
    );
  }

  /// 获取状态标题
  String _getStatusTitle() {
    switch (paymentResponse.status) {
      case PaymentStatus.success:
        return '支付成功';
      case PaymentStatus.failed:
        return '支付失败';
      case PaymentStatus.cancelled:
        return '支付取消';
      case PaymentStatus.timeout:
        return '支付超时';
      case PaymentStatus.processing:
        return '支付处理中';
      case PaymentStatus.pending:
        return '等待支付';
    }
  }

  /// 获取状态描述
  String _getStatusDescription() {
    switch (paymentResponse.status) {
      case PaymentStatus.success:
        return '恭喜您，支付已完成！\n订单将尽快为您处理。';
      case PaymentStatus.failed:
        return '支付失败，请重试或选择其他支付方式。\n${paymentResponse.errorMessage ?? ''}';
      case PaymentStatus.cancelled:
        return '您已取消支付，可以重新选择支付方式。';
      case PaymentStatus.timeout:
        return '支付超时，请重新发起支付。';
      case PaymentStatus.processing:
        return '正在处理您的支付请求，请稍候...';
      case PaymentStatus.pending:
        return '等待您完成支付操作...';
    }
  }

  /// 获取状态颜色
  Color _getStatusColor() {
    switch (paymentResponse.status) {
      case PaymentStatus.success:
        return Colors.green;
      case PaymentStatus.failed:
        return Colors.red;
      case PaymentStatus.cancelled:
        return Colors.orange;
      case PaymentStatus.timeout:
        return Colors.grey;
      case PaymentStatus.processing:
      case PaymentStatus.pending:
        return Colors.blue;
    }
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    final buttons = <Widget>[];

    switch (paymentResponse.status) {
      case PaymentStatus.success:
        // 支付成功，显示返回订单按钮
        buttons.add(
          SizedBox(
            width: double.infinity,
            child: CustomButton(
              text: '查看订单',
              onPressed: onBackToOrder,
            ),
          ),
        );

      case PaymentStatus.failed:
      case PaymentStatus.cancelled:
      case PaymentStatus.timeout:
        // 支付失败/取消/超时，显示重试和返回按钮
        buttons.addAll([
          SizedBox(
            width: double.infinity,
            child: CustomButton(
              text: '重新支付',
              onPressed: onRetry,
            ),
          ),
          SizedBox(height: 12.h),
          SizedBox(
            width: double.infinity,
            child: CustomButton(
              text: '返回订单',
              onPressed: onBackToOrder,
              type: ButtonType.outline,
            ),
          ),
        ]);

      case PaymentStatus.processing:
      case PaymentStatus.pending:
        // 支付中/等待中，显示返回按钮
        buttons.add(
          SizedBox(
            width: double.infinity,
            child: CustomButton(
              text: '返回订单',
              onPressed: onBackToOrder,
              type: ButtonType.outline,
            ),
          ),
        );
    }

    return Column(children: buttons);
  }
}
