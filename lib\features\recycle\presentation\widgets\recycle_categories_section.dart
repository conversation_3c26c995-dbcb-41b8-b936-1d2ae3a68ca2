import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/shared/presentation/widgets/custom_card.dart';
import 'package:soko/features/recycle/domain/entities/recycle_models.dart';

/// 回收分类选择组件
class RecycleCategoriesSection extends StatelessWidget {

  const RecycleCategoriesSection({
    super.key,
    required this.categories,
    required this.onCategoryTap,
  });
  final List<CategoryItem> categories;
  final ValueChanged<CategoryItem> onCategoryTap;

  @override
  Widget build(BuildContext context) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.category,
                color: Colors.teal,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                '回收分类',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const Spacer(),
              Text(
                '选择要回收的商品类型',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // 分类网格
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              crossAxisSpacing: 12.w,
              mainAxisSpacing: 12.h,
              childAspectRatio: 0.8,
            ),
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              return _buildCategoryItem(category);
            },
          ),
        ],
      ),
    );
  }

  /// 构建分类项
  Widget _buildCategoryItem(CategoryItem category) {
    return InkWell(
      onTap: () => onCategoryTap(category),
      borderRadius: BorderRadius.circular(8.r),
      child: Container(
        padding: EdgeInsets.all(8.w),
        decoration: BoxDecoration(
          color: Colors.grey.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 图标
            Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: _getCategoryColor(category.name).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20.r),
              ),
              child: Icon(
                _getCategoryIcon(category.name),
                color: _getCategoryColor(category.name),
                size: 20.w,
              ),
            ),
            SizedBox(height: 8.h),

            // 名称
            Text(
              category.name,
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  /// 获取分类图标
  IconData _getCategoryIcon(String categoryName) {
    switch (categoryName.toLowerCase()) {
      case '手机':
      case 'phone':
        return Icons.smartphone;
      case '笔记本':
      case 'laptop':
        return Icons.laptop;
      case '平板':
      case 'tablet':
        return Icons.tablet;
      case '手表':
      case 'watch':
        return Icons.watch;
      case '相机':
      case 'camera':
        return Icons.camera_alt;
      case '游戏机':
      case 'game':
        return Icons.games;
      case '耳机':
      case 'headphone':
        return Icons.headphones;
      case '其他':
      case 'other':
        return Icons.more_horiz;
      default:
        return Icons.devices;
    }
  }

  /// 获取分类颜色
  Color _getCategoryColor(String categoryName) {
    switch (categoryName.toLowerCase()) {
      case '手机':
      case 'phone':
        return Colors.blue;
      case '笔记本':
      case 'laptop':
        return Colors.green;
      case '平板':
      case 'tablet':
        return Colors.orange;
      case '手表':
      case 'watch':
        return Colors.purple;
      case '相机':
      case 'camera':
        return Colors.red;
      case '游戏机':
      case 'game':
        return Colors.indigo;
      case '耳机':
      case 'headphone':
        return Colors.teal;
      case '其他':
      case 'other':
        return Colors.grey;
      default:
        return Colors.blueGrey;
    }
  }
}
