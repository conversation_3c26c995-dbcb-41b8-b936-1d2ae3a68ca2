// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'query_params.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BaseQueryParams _$BaseQueryParamsFromJson(Map<String, dynamic> json) =>
    BaseQueryParams(
      page: (json['page'] as num).toInt(),
      size: (json['size'] as num).toInt(),
    );

Map<String, dynamic> _$BaseQueryParamsToJson(BaseQueryParams instance) =>
    <String, dynamic>{
      'page': instance.page,
      'size': instance.size,
    };

OrderQueryParams _$OrderQueryParamsFromJson(Map<String, dynamic> json) =>
    OrderQueryParams(
      page: (json['page'] as num).toInt(),
      size: (json['size'] as num).toInt(),
      orderStatus: json['orderStatus'] as String?,
      startDate: json['startDate'] as String?,
      endDate: json['endDate'] as String?,
      orderNo: json['orderNo'] as String?,
    );

Map<String, dynamic> _$OrderQueryParamsToJson(OrderQueryParams instance) =>
    <String, dynamic>{
      'page': instance.page,
      'size': instance.size,
      'orderStatus': instance.orderStatus,
      'startDate': instance.startDate,
      'endDate': instance.endDate,
      'orderNo': instance.orderNo,
    };

RecycleOrderQueryParams _$RecycleOrderQueryParamsFromJson(
        Map<String, dynamic> json) =>
    RecycleOrderQueryParams(
      page: (json['page'] as num).toInt(),
      size: (json['size'] as num).toInt(),
      orderStatus: json['orderStatus'] as String?,
      productName: json['productName'] as String?,
      startDate: json['startDate'] as String?,
      endDate: json['endDate'] as String?,
    );

Map<String, dynamic> _$RecycleOrderQueryParamsToJson(
        RecycleOrderQueryParams instance) =>
    <String, dynamic>{
      'page': instance.page,
      'size': instance.size,
      'orderStatus': instance.orderStatus,
      'productName': instance.productName,
      'startDate': instance.startDate,
      'endDate': instance.endDate,
    };

ProductQueryParams _$ProductQueryParamsFromJson(Map<String, dynamic> json) =>
    ProductQueryParams(
      page: (json['page'] as num).toInt(),
      size: (json['size'] as num).toInt(),
      keyword: json['keyword'] as String?,
      category: json['category'] as String?,
      acg: json['acg'] as String?,
      brand: json['brand'] as String?,
      minPrice: (json['minPrice'] as num?)?.toDouble(),
      maxPrice: (json['maxPrice'] as num?)?.toDouble(),
      condition: json['condition'] as String?,
      sortBy: json['sortBy'] as String?,
      sortOrder: json['sortOrder'] as String?,
    );

Map<String, dynamic> _$ProductQueryParamsToJson(ProductQueryParams instance) =>
    <String, dynamic>{
      'page': instance.page,
      'size': instance.size,
      'keyword': instance.keyword,
      'category': instance.category,
      'acg': instance.acg,
      'brand': instance.brand,
      'minPrice': instance.minPrice,
      'maxPrice': instance.maxPrice,
      'condition': instance.condition,
      'sortBy': instance.sortBy,
      'sortOrder': instance.sortOrder,
    };

CouponQueryParams _$CouponQueryParamsFromJson(Map<String, dynamic> json) =>
    CouponQueryParams(
      page: (json['page'] as num).toInt(),
      size: (json['size'] as num).toInt(),
      type: json['type'] as String?,
      status: json['status'] as String?,
      available: json['available'] as bool?,
    );

Map<String, dynamic> _$CouponQueryParamsToJson(CouponQueryParams instance) =>
    <String, dynamic>{
      'page': instance.page,
      'size': instance.size,
      'type': instance.type,
      'status': instance.status,
      'available': instance.available,
    };

SearchParams _$SearchParamsFromJson(Map<String, dynamic> json) => SearchParams(
      keyword: json['keyword'] as String,
      page: (json['page'] as num).toInt(),
      size: (json['size'] as num).toInt(),
      filters: json['filters'] as Map<String, dynamic>?,
      sortBy: json['sortBy'] as String?,
      sortOrder: json['sortOrder'] as String?,
    );

Map<String, dynamic> _$SearchParamsToJson(SearchParams instance) =>
    <String, dynamic>{
      'keyword': instance.keyword,
      'page': instance.page,
      'size': instance.size,
      'filters': instance.filters,
      'sortBy': instance.sortBy,
      'sortOrder': instance.sortOrder,
    };

FilterParams _$FilterParamsFromJson(Map<String, dynamic> json) => FilterParams(
      categories: (json['categories'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      brands:
          (json['brands'] as List<dynamic>?)?.map((e) => e as String).toList(),
      priceRange: json['priceRange'] == null
          ? null
          : PriceRange.fromJson(json['priceRange'] as Map<String, dynamic>),
      conditions: (json['conditions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      locations: (json['locations'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$FilterParamsToJson(FilterParams instance) =>
    <String, dynamic>{
      'categories': instance.categories,
      'brands': instance.brands,
      'priceRange': instance.priceRange,
      'conditions': instance.conditions,
      'locations': instance.locations,
    };

PriceRange _$PriceRangeFromJson(Map<String, dynamic> json) => PriceRange(
      min: (json['min'] as num?)?.toDouble(),
      max: (json['max'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$PriceRangeToJson(PriceRange instance) =>
    <String, dynamic>{
      'min': instance.min,
      'max': instance.max,
    };

SortParams _$SortParamsFromJson(Map<String, dynamic> json) => SortParams(
      field: json['field'] as String,
      order: json['order'] as String,
    );

Map<String, dynamic> _$SortParamsToJson(SortParams instance) =>
    <String, dynamic>{
      'field': instance.field,
      'order': instance.order,
    };
