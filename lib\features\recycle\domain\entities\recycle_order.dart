import 'package:json_annotation/json_annotation.dart';

import 'package:soko/core/enums/app_enums.dart';

part 'recycle_order.g.dart';

/// 回收订单实体类
@JsonSerializable()
class RecycleOrder {

  const RecycleOrder({
    required this.id,
    required this.userId,
    this.userPhone,
    required this.brandName,
    required this.model,
    required this.categoryName,
    this.productDesc,
    required this.conditionDescription,
    required this.estimatedPrice,
    this.finalPrice,
    this.reviewedPrice,
    required this.orderStatus,
    required this.orderStatusDesc,
    this.shippingInfo,
    this.buyerAddress,
    this.contactPerson,
    required this.createTime,
    required this.updateTime,
    this.mainImage,
    this.files,
    this.reviews,
  });

  factory RecycleOrder.fromJson(Map<String, dynamic> json) => _$RecycleOrderFromJson(json);
  @JsonKey(name: 'id')
  final String id;

  @JsonKey(name: 'userId')
  final String userId;

  @<PERSON>son<PERSON>ey(name: 'userPhone')
  final String? userPhone;

  @Json<PERSON>ey(name: 'brandName')
  final String brandName;

  @JsonKey(name: 'model')
  final String model;

  @JsonKey(name: 'categoryName')
  final String categoryName;

  @JsonKey(name: 'productDesc')
  final String? productDesc;

  @JsonKey(name: 'conditionDescription')
  final String conditionDescription;

  @JsonKey(name: 'estimatedPrice')
  final double estimatedPrice;

  @JsonKey(name: 'finalPrice')
  final double? finalPrice;

  @JsonKey(name: 'reviewedPrice')
  final double? reviewedPrice;

  @JsonKey(name: 'orderStatus')
  final String orderStatus;

  @JsonKey(name: 'orderStatusDesc')
  final String orderStatusDesc;

  @JsonKey(name: 'shippingInfo')
  final String? shippingInfo;

  @JsonKey(name: 'buyerAddress')
  final String? buyerAddress;

  @JsonKey(name: 'contactPerson')
  final String? contactPerson;

  @JsonKey(name: 'createTime')
  final int createTime;

  @JsonKey(name: 'updateTime')
  final int updateTime;

  @JsonKey(name: 'mainImage')
  final String? mainImage;

  @JsonKey(name: 'files')
  final List<RecycleOrderFile>? files;

  @JsonKey(name: 'reviews')
  final List<OrderReview>? reviews;

  Map<String, dynamic> toJson() => _$RecycleOrderToJson(this);

  /// 获取主图
  String? get primaryImage {
    if (mainImage != null) return mainImage;
    if (files == null || files!.isEmpty) return null;
    final mainFile = files!.firstWhere(
      (file) => file.isMain,
      orElse: () => files!.first,
    );
    return mainFile.url;
  }

  /// 获取回收订单状态枚举
  RecycleOrderStatus get statusEnum {
    switch (orderStatus.toUpperCase()) {
      case 'DRAFT':
        return RecycleOrderStatus.created;
      case 'PENDING_APPROVAL':
        return RecycleOrderStatus.created;
      case 'PRICE_QUOTED':
        return RecycleOrderStatus.confirmed;
      case 'SHIPPING_CONFIRMED':
        return RecycleOrderStatus.picked;
      case 'RECEIVED':
        return RecycleOrderStatus.evaluated;
      case 'PAYMENT_CONFIRMED':
      case 'COMPLETED':
        return RecycleOrderStatus.completed;
      case 'CANCELLED':
      case 'RETURN_REQUESTED':
      case 'RETURNED':
        return RecycleOrderStatus.cancelled;
      default:
        return RecycleOrderStatus.created;
    }
  }

  /// 获取最终价格（优先使用finalPrice，其次reviewedPrice，最后estimatedPrice）
  double get actualPrice {
    return finalPrice ?? reviewedPrice ?? estimatedPrice;
  }

  /// 是否可以取消
  bool get canCancel {
    return statusEnum == RecycleOrderStatus.created || 
           statusEnum == RecycleOrderStatus.confirmed;
  }

  /// 是否可以确认寄送
  bool get canConfirmShipment {
    return statusEnum == RecycleOrderStatus.confirmed;
  }

  /// 是否可以完成退货
  bool get canCompleteReturn {
    return statusEnum == RecycleOrderStatus.evaluated;
  }

  /// 是否已完成
  bool get isCompleted {
    return statusEnum == RecycleOrderStatus.completed;
  }

  /// 是否已取消
  bool get isCancelled {
    return statusEnum == RecycleOrderStatus.cancelled;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RecycleOrder && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'RecycleOrder(id: $id, brandName: $brandName, model: $model, status: $orderStatus)';
  }
}

/// 回收订单文件
@JsonSerializable()
class RecycleOrderFile {

  const RecycleOrderFile({
    required this.id,
    required this.recyclingOrderId,
    required this.fileId,
    required this.url,
    this.thumbnailUrl,
    required this.sort,
    required this.isMain,
  });

  factory RecycleOrderFile.fromJson(Map<String, dynamic> json) => _$RecycleOrderFileFromJson(json);
  @JsonKey(name: 'id')
  final String id;

  @JsonKey(name: 'recyclingOrderId')
  final String recyclingOrderId;

  @JsonKey(name: 'fileId')
  final String fileId;

  @JsonKey(name: 'url')
  final String url;

  @JsonKey(name: 'thumbnailUrl')
  final String? thumbnailUrl;

  @JsonKey(name: 'sort')
  final int sort;

  @JsonKey(name: 'isMain')
  final bool isMain;

  Map<String, dynamic> toJson() => _$RecycleOrderFileToJson(this);
}

/// 订单评价
@JsonSerializable()
class OrderReview {

  const OrderReview({
    required this.id,
    required this.orderId,
    required this.reviewerId,
    required this.reviewComments,
    required this.createTime,
  });

  factory OrderReview.fromJson(Map<String, dynamic> json) => _$OrderReviewFromJson(json);
  @JsonKey(name: 'id')
  final String id;

  @JsonKey(name: 'orderId')
  final String orderId;

  @JsonKey(name: 'reviewerId')
  final String reviewerId;

  @JsonKey(name: 'reviewComments')
  final String reviewComments;

  @JsonKey(name: 'createTime')
  final int createTime;

  Map<String, dynamic> toJson() => _$OrderReviewToJson(this);
}

/// 创建回收订单请求
@JsonSerializable()
class CreateRecycleOrderRequest {

  const CreateRecycleOrderRequest({
    required this.productName,
    required this.productDesc,
    this.productModel,
    required this.productCategory,
    required this.contactPerson,
    required this.contactPhone,
    required this.expectedPrice,
    required this.condition,
    required this.imageFiles,
  });

  factory CreateRecycleOrderRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateRecycleOrderRequestFromJson(json);
  @JsonKey(name: 'productName')
  final String productName;

  @JsonKey(name: 'productDesc')
  final String productDesc;

  @JsonKey(name: 'productModel')
  final String? productModel;

  @JsonKey(name: 'productCategory')
  final String productCategory;

  @JsonKey(name: 'contactPerson')
  final String contactPerson;

  @JsonKey(name: 'contactPhone')
  final String contactPhone;

  @JsonKey(name: 'expectedPrice')
  final double expectedPrice;

  @JsonKey(name: 'condition')
  final String condition;

  @JsonKey(name: 'imageFiles')
  final List<String> imageFiles;

  Map<String, dynamic> toJson() => _$CreateRecycleOrderRequestToJson(this);
}

/// 物流信息
@JsonSerializable()
class ShippingInfo {

  const ShippingInfo({
    required this.courierCompany,
    required this.trackingNumber,
    required this.senderName,
    required this.senderPhone,
    required this.senderAddress,
  });

  factory ShippingInfo.fromJson(Map<String, dynamic> json) => _$ShippingInfoFromJson(json);
  @JsonKey(name: 'courierCompany')
  final String courierCompany;

  @JsonKey(name: 'trackingNumber')
  final String trackingNumber;

  @JsonKey(name: 'senderName')
  final String senderName;

  @JsonKey(name: 'senderPhone')
  final String senderPhone;

  @JsonKey(name: 'senderAddress')
  final String senderAddress;

  Map<String, dynamic> toJson() => _$ShippingInfoToJson(this);
}
