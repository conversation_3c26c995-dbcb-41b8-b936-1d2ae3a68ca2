import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';

import 'package:soko/features/recycle/domain/entities/recycle_order.dart';
import 'package:soko/core/enums/app_enums.dart';
import 'package:soko/shared/presentation/widgets/status_badge.dart';

/// 回收订单卡片组件
class RecycleOrderCard extends StatelessWidget {
  const RecycleOrderCard({
    super.key,
    required this.order,
    this.onTap,
    this.onCancel,
    this.onConfirmShipment,
  });

  final RecycleOrder order;
  final VoidCallback? onTap;
  final VoidCallback? onCancel;
  final VoidCallback? onConfirmShipment;

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 订单头部信息
              _buildOrderHeader(context),
              SizedBox(height: 12.h),
              
              // 商品信息
              _buildProductInfo(context),
              SizedBox(height: 12.h),
              
              // 价格信息
              _buildPriceInfo(context),
              SizedBox(height: 12.h),
              
              // 操作按钮
              if (_hasActions()) _buildActionButtons(context),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建订单头部
  Widget _buildOrderHeader(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '订单号：${order.id}',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.grey[600],
                ),
              ),
              SizedBox(height: 4.h),
              Text(
                _formatDateTime(order.createTime),
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        StatusBadge(
          status: order.orderStatusDesc,
          type: _getStatusBadgeType(order.statusEnum),
        ),
      ],
    );
  }

  /// 构建商品信息
  Widget _buildProductInfo(BuildContext context) {
    return Row(
      children: [
        // 商品图片
        ClipRRect(
          borderRadius: BorderRadius.circular(8.r),
          child: SizedBox(
            width: 60.w,
            height: 60.w,
            child: order.primaryImage != null
                ? CachedNetworkImage(
                    imageUrl: order.primaryImage!,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: Colors.grey[200],
                      child: Icon(
                        Icons.image,
                        color: Colors.grey[400],
                        size: 24.w,
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: Colors.grey[200],
                      child: Icon(
                        Icons.broken_image,
                        color: Colors.grey[400],
                        size: 24.w,
                      ),
                    ),
                  )
                : Container(
                    color: Colors.grey[200],
                    child: Icon(
                      Icons.phone_android,
                      color: Colors.grey[400],
                      size: 24.w,
                    ),
                  ),
          ),
        ),
        SizedBox(width: 12.w),
        
        // 商品详情
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${order.brandName} ${order.model}',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 4.h),
              Text(
                order.categoryName,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.grey[600],
                ),
              ),
              SizedBox(height: 4.h),
              Text(
                order.conditionDescription,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.grey[600],
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建价格信息
  Widget _buildPriceInfo(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '预估价格',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  '¥${order.estimatedPrice.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[700],
                  ),
                ),
              ],
            ),
          ),
          if (order.finalPrice != null || order.reviewedPrice != null)
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '实际价格',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                  Text(
                    '¥${order.actualPrice.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        if (onCancel != null)
          Expanded(
            child: OutlinedButton(
              onPressed: onCancel,
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: Colors.red[300]!),
                foregroundColor: Colors.red[600],
              ),
              child: const Text('取消订单'),
            ),
          ),
        if (onCancel != null && onConfirmShipment != null) SizedBox(width: 12.w),
        if (onConfirmShipment != null)
          Expanded(
            child: ElevatedButton(
              onPressed: onConfirmShipment,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('确认寄送'),
            ),
          ),
      ],
    );
  }

  /// 是否有操作按钮
  bool _hasActions() {
    return onCancel != null || onConfirmShipment != null;
  }

  /// 获取状态徽章类型
  StatusBadgeType _getStatusBadgeType(RecycleOrderStatus status) {
    switch (status) {
      case RecycleOrderStatus.created:
        return StatusBadgeType.warning;
      case RecycleOrderStatus.confirmed:
        return StatusBadgeType.info;
      case RecycleOrderStatus.picked:
        return StatusBadgeType.primary;
      case RecycleOrderStatus.evaluated:
        return StatusBadgeType.primary;
      case RecycleOrderStatus.completed:
        return StatusBadgeType.success;
      case RecycleOrderStatus.cancelled:
        return StatusBadgeType.error;
    }
  }

  /// 格式化日期时间
  String _formatDateTime(int timestamp) {
    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
