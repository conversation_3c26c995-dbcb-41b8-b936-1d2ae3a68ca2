import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/features/order/domain/entities/order.dart';
import 'package:soko/features/order/domain/repositories/order_repository.dart';
import 'package:soko/features/order/data/repositories/order_repository_impl.dart';
import 'package:soko/core/enums/app_enums.dart';

/// 订单管理状态
class OrderManagementState {
  const OrderManagementState({
    required this.orders,
    this.isLoading = false,
    this.isLoadingMore = false,
    this.hasMore = true,
    this.currentPage = 1,
    this.error,
    this.filters,
    this.searchKeyword,
    this.selectedStatus,
    this.statistics,
  });

  final List<Order> orders;
  final bool isLoading;
  final bool isLoadingMore;
  final bool hasMore;
  final int currentPage;
  final String? error;
  final Map<String, dynamic>? filters;
  final String? searchKeyword;
  final OrderStatus? selectedStatus;
  final OrderStatistics? statistics;

  OrderManagementState copyWith({
    List<Order>? orders,
    bool? isLoading,
    bool? isLoadingMore,
    bool? hasMore,
    int? currentPage,
    String? error,
    Map<String, dynamic>? filters,
    String? searchKeyword,
    OrderStatus? selectedStatus,
    OrderStatistics? statistics,
  }) {
    return OrderManagementState(
      orders: orders ?? this.orders,
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      hasMore: hasMore ?? this.hasMore,
      currentPage: currentPage ?? this.currentPage,
      error: error,
      filters: filters ?? this.filters,
      searchKeyword: searchKeyword ?? this.searchKeyword,
      selectedStatus: selectedStatus ?? this.selectedStatus,
      statistics: statistics ?? this.statistics,
    );
  }
}

/// 订单统计数据
class OrderStatistics {
  const OrderStatistics({
    required this.totalOrders,
    required this.pendingOrders,
    required this.paidOrders,
    required this.shippedOrders,
    required this.completedOrders,
    required this.cancelledOrders,
    required this.totalAmount,
    required this.todayOrders,
    required this.todayAmount,
  });

  final int totalOrders;
  final int pendingOrders;
  final int paidOrders;
  final int shippedOrders;
  final int completedOrders;
  final int cancelledOrders;
  final double totalAmount;
  final int todayOrders;
  final double todayAmount;
}

/// 订单管理状态管理
class OrderManagementNotifier extends StateNotifier<AsyncValue<OrderManagementState>> {
  OrderManagementNotifier(this._orderRepository) 
      : super(const AsyncValue.data(OrderManagementState(orders: [])));

  final OrderRepository _orderRepository;
  static const int pageSize = 20;

  /// 加载订单列表
  Future<void> loadOrders({bool refresh = false}) async {
    final currentState = state.value;
    if (currentState == null) return;

    if (refresh) {
      state = AsyncValue.data(currentState.copyWith(
        isLoading: true,
        currentPage: 1,
        hasMore: true,
      ));
    } else {
      state = AsyncValue.data(currentState.copyWith(isLoading: true));
    }

    try {
      final result = await _orderRepository.getOrders(
        page: refresh ? 1 : currentState.currentPage,
        pageSize: pageSize,
        status: currentState.selectedStatus,
        keyword: currentState.searchKeyword,
        filters: currentState.filters,
      );

      final newOrders = refresh ? result.data : [...currentState.orders, ...result.data];
      
      // 同时加载统计数据
      final statistics = await _orderRepository.getOrderStatistics();

      state = AsyncValue.data(currentState.copyWith(
        orders: newOrders,
        isLoading: false,
        hasMore: result.hasNextPage,
        currentPage: result.currentPage,
        statistics: statistics,
      ));
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// 加载更多订单
  Future<void> loadMoreOrders() async {
    final currentState = state.value;
    if (currentState == null || !currentState.hasMore || currentState.isLoadingMore) {
      return;
    }

    state = AsyncValue.data(currentState.copyWith(isLoadingMore: true));

    try {
      final result = await _orderRepository.getOrders(
        page: currentState.currentPage + 1,
        pageSize: pageSize,
        status: currentState.selectedStatus,
        keyword: currentState.searchKeyword,
        filters: currentState.filters,
      );

      final newOrders = [...currentState.orders, ...result.data];

      state = AsyncValue.data(currentState.copyWith(
        orders: newOrders,
        isLoadingMore: false,
        hasMore: result.hasNextPage,
        currentPage: result.currentPage,
      ));
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// 刷新订单列表
  Future<void> refreshOrders() async {
    await loadOrders(refresh: true);
  }

  /// 按状态筛选
  Future<void> filterByStatus(OrderStatus? status) async {
    final currentState = state.value;
    if (currentState == null) return;

    state = AsyncValue.data(currentState.copyWith(
      selectedStatus: status,
      currentPage: 1,
      hasMore: true,
    ));

    await loadOrders(refresh: true);
  }

  /// 搜索订单
  Future<void> searchOrders(String keyword) async {
    final currentState = state.value;
    if (currentState == null) return;

    state = AsyncValue.data(currentState.copyWith(
      searchKeyword: keyword.isEmpty ? null : keyword,
      currentPage: 1,
      hasMore: true,
    ));

    await loadOrders(refresh: true);
  }

  /// 应用筛选条件
  Future<void> applyFilters(Map<String, dynamic> filters) async {
    final currentState = state.value;
    if (currentState == null) return;

    state = AsyncValue.data(currentState.copyWith(
      filters: filters,
      currentPage: 1,
      hasMore: true,
    ));

    await loadOrders(refresh: true);
  }

  /// 更新订单状态
  Future<void> updateOrderStatus(String orderId, OrderStatus newStatus) async {
    try {
      await _orderRepository.updateOrderStatus(orderId, newStatus);
      
      // 更新本地状态
      final currentState = state.value;
      if (currentState != null) {
        final updatedOrders = currentState.orders.map((order) {
          if (order.id == orderId) {
            return order.copyWith(orderStatus: newStatus);
          }
          return order;
        }).toList();

        state = AsyncValue.data(currentState.copyWith(orders: updatedOrders));
      }
    } catch (error) {
      // 显示错误提示
      rethrow;
    }
  }

  /// 批量取消订单
  Future<void> batchCancelOrders(List<String> orderIds) async {
    try {
      await _orderRepository.batchCancelOrders(orderIds);
      
      // 刷新订单列表
      await refreshOrders();
    } catch (error) {
      rethrow;
    }
  }

  /// 批量导出订单
  Future<void> batchExportOrders(List<String> orderIds) async {
    try {
      await _orderRepository.exportOrders(orderIds);
    } catch (error) {
      rethrow;
    }
  }

  /// 批量打印订单
  Future<void> batchPrintOrders(List<String> orderIds) async {
    try {
      await _orderRepository.printOrders(orderIds);
    } catch (error) {
      rethrow;
    }
  }

  /// 获取订单详情
  Future<Order> getOrderDetail(String orderId) async {
    return await _orderRepository.getOrderDetail(orderId);
  }
}

/// 订单管理Provider
final orderManagementProvider = StateNotifierProvider<OrderManagementNotifier, AsyncValue<OrderManagementState>>((ref) {
  final orderRepository = ref.watch(orderRepositoryProvider);
  return OrderManagementNotifier(orderRepository);
});

/// 订单统计Provider
final orderStatisticsProvider = FutureProvider<OrderStatistics>((ref) async {
  final orderRepository = ref.watch(orderRepositoryProvider);
  return orderRepository.getOrderStatistics();
});
