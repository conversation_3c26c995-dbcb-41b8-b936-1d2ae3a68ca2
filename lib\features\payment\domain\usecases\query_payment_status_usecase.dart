import 'package:dartz/dartz.dart' hide Order;
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/error/failures.dart';
import 'package:soko/core/usecases/usecase.dart';
import 'package:soko/features/payment/data/repositories/payment_repository_impl.dart';
import 'package:soko/features/payment/domain/entities/payment_request.dart';
import 'package:soko/features/payment/domain/repositories/payment_repository.dart';

/// 查询支付状态用例
class QueryPaymentStatusUseCase implements UseCase<PaymentResponse, String> {

  QueryPaymentStatusUseCase(this.repository);
  final PaymentRepository repository;

  @override
  Future<Either<Failure, PaymentResponse>> call(String paymentOrderNo) async {
    if (paymentOrderNo.isEmpty) {
      return const Left(ValidationFailure('支付订单号不能为空'));
    }

    return repository.queryPaymentStatus(paymentOrderNo);
  }
}

/// QueryPaymentStatusUseCase 提供者
final queryPaymentStatusUseCaseProvider = Provider<QueryPaymentStatusUseCase>((ref) {
  final repository = ref.read(paymentRepositoryProvider);
  return QueryPaymentStatusUseCase(repository);
});
