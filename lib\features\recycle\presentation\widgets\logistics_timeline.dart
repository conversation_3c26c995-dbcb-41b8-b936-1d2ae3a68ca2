import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/features/recycle/domain/entities/recycle_models.dart';

/// 物流时间线组件
class LogisticsTimeline extends StatelessWidget {
  const LogisticsTimeline({
    super.key,
    required this.tracks,
  });

  final List<LogisticsTrack> tracks;

  @override
  Widget build(BuildContext context) {
    // 按时间倒序排列（最新的在前面）
    final sortedTracks = List<LogisticsTrack>.from(tracks)
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.timeline,
                size: 20.w,
                color: Theme.of(context).primaryColor,
              ),
              SizedBox(width: 8.w),
              Text(
                '物流轨迹',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Text(
                '共${tracks.length}条记录',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          
          // 时间线
          Column(
            children: sortedTracks.asMap().entries.map((entry) {
              final index = entry.key;
              final track = entry.value;
              final isFirst = index == 0;
              final isLast = index == sortedTracks.length - 1;

              return _buildTimelineItem(
                context: context,
                track: track,
                isFirst: isFirst,
                isLast: isLast,
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  /// 构建时间线项目
  Widget _buildTimelineItem({
    required BuildContext context,
    required LogisticsTrack track,
    required bool isFirst,
    required bool isLast,
  }) {
    final primaryColor = Theme.of(context).primaryColor;
    final greyColor = Colors.grey[400]!;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 左侧时间线
        Column(
          children: [
            // 状态点
            Container(
              width: 12.w,
              height: 12.w,
              decoration: BoxDecoration(
                color: isFirst ? primaryColor : greyColor,
                shape: BoxShape.circle,
                border: Border.all(
                  color: isFirst ? primaryColor : greyColor,
                  width: 2,
                ),
              ),
            ),
            
            // 连接线
            if (!isLast)
              Container(
                width: 2.w,
                height: 60.h,
                color: greyColor,
              ),
          ],
        ),
        SizedBox(width: 16.w),
        
        // 右侧内容
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(bottom: isLast ? 0 : 16.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 状态描述
                Text(
                  track.statusDesc,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: isFirst ? FontWeight.w600 : FontWeight.normal,
                    color: isFirst ? Colors.grey[800] : Colors.grey[700],
                  ),
                ),
                SizedBox(height: 4.h),
                
                // 位置信息
                Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      size: 12.w,
                      color: Colors.grey[500],
                    ),
                    SizedBox(width: 4.w),
                    Expanded(
                      child: Text(
                        track.location,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                  ],
                ),
                
                // 详细描述
                if (track.description != null && track.description!.isNotEmpty) ...[
                  SizedBox(height: 4.h),
                  Text(
                    track.description!,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
                
                // 时间和操作员
                SizedBox(height: 6.h),
                Row(
                  children: [
                    Text(
                      _formatDateTime(track.timestamp),
                      style: TextStyle(
                        fontSize: 11.sp,
                        color: Colors.grey[500],
                      ),
                    ),
                    if (track.operatorName != null) ...[
                      SizedBox(width: 8.w),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                        child: Text(
                          track.operatorName!,
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: Colors.grey[600],
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 格式化日期时间
  String _formatDateTime(int timestamp) {
    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final trackDate = DateTime(dateTime.year, dateTime.month, dateTime.day);
    
    String dateStr;
    if (trackDate == today) {
      dateStr = '今天';
    } else if (trackDate == today.subtract(const Duration(days: 1))) {
      dateStr = '昨天';
    } else {
      dateStr = '${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
    }
    
    final timeStr = '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    
    return '$dateStr $timeStr';
  }
}
