import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/shared/presentation/widgets/custom_card.dart';
import 'package:soko/features/recycle/data/datasources/recycle_api_service.dart';

/// 回收统计信息卡片
class RecycleStatsCard extends StatelessWidget {

  const RecycleStatsCard({
    super.key,
    required this.stats,
  });
  final RecycleStats stats;

  @override
  Widget build(BuildContext context) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.analytics,
                color: Colors.purple,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                '回收统计',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // 统计数据
          Row(
            children: [
              // 总订单数
              Expanded(
                child: _buildStatItem(
                  title: '总订单',
                  value: stats.totalOrders.toString(),
                  subtitle: '累计订单数',
                  color: Colors.blue,
                  icon: Icons.receipt_long,
                ),
              ),
              SizedBox(width: 12.w),

              // 已完成订单
              Expanded(
                child: _buildStatItem(
                  title: '已完成',
                  value: stats.completedOrders.toString(),
                  subtitle: '完成订单数',
                  color: Colors.green,
                  icon: Icons.check_circle,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),

          Row(
            children: [
              // 总金额
              Expanded(
                child: _buildStatItem(
                  title: '总金额',
                  value: '¥${stats.totalAmount.toStringAsFixed(2)}',
                  subtitle: '累计回收金额',
                  color: Colors.orange,
                  icon: Icons.monetization_on,
                ),
              ),
              SizedBox(width: 12.w),

              // 本月订单
              Expanded(
                child: _buildStatItem(
                  title: '本月订单',
                  value: stats.thisMonthOrders.toString(),
                  subtitle: '本月新增订单',
                  color: Colors.purple,
                  icon: Icons.trending_up,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建统计项
  Widget _buildStatItem({
    required String title,
    required String value,
    required String subtitle,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 图标和标题
          Row(
            children: [
              Icon(
                icon,
                color: color,
                size: 16.w,
              ),
              SizedBox(width: 6.w),
              Text(
                title,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),

          // 数值
          Text(
            value,
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
          SizedBox(height: 4.h),

          // 副标题
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 10.sp,
              color: Colors.grey[500],
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
