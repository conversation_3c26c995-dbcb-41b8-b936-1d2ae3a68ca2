import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

/// 性能监控器
class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  final Map<String, Stopwatch> _timers = {};
  final List<PerformanceMetric> _metrics = [];
  Timer? _memoryTimer;

  /// 初始化性能监控
  void initialize() {
    if (kDebugMode) {
      _startMemoryMonitoring();
      developer.log('Performance monitoring initialized');
    }
  }

  /// 开始计时
  void startTimer(String name) {
    if (!kDebugMode) return;
    
    _timers[name] = Stopwatch()..start();
  }

  /// 结束计时
  void endTimer(String name) {
    if (!kDebugMode) return;
    
    final timer = _timers[name];
    if (timer != null) {
      timer.stop();
      final duration = timer.elapsedMilliseconds;
      
      _addMetric(PerformanceMetric(
        name: name,
        type: MetricType.timing,
        value: duration.toDouble(),
        timestamp: DateTime.now(),
      ));
      
      _timers.remove(name);
      
      if (duration > 1000) {
        developer.log('⚠️ Slow operation: $name took ${duration}ms');
      }
    }
  }

  /// 记录自定义指标
  void recordMetric(String name, double value, MetricType type) {
    if (!kDebugMode) return;
    
    _addMetric(PerformanceMetric(
      name: name,
      type: type,
      value: value,
      timestamp: DateTime.now(),
    ));
  }

  /// 记录页面加载时间
  void recordPageLoad(String pageName, Duration duration) {
    recordMetric(
      'page_load_$pageName',
      duration.inMilliseconds.toDouble(),
      MetricType.pageLoad,
    );
  }

  /// 记录API调用时间
  void recordApiCall(String endpoint, Duration duration, bool success) {
    recordMetric(
      'api_call_$endpoint',
      duration.inMilliseconds.toDouble(),
      MetricType.apiCall,
    );
    
    if (!success) {
      recordMetric(
        'api_error_$endpoint',
        1,
        MetricType.error,
      );
    }
  }

  /// 记录内存使用
  void recordMemoryUsage() {
    if (!kDebugMode) return;
    
    // 这里可以集成更详细的内存监控
    recordMetric(
      'memory_usage',
      0, // 实际项目中应该获取真实的内存使用量
      MetricType.memory,
    );
  }

  /// 记录用户操作
  void recordUserAction(String action) {
    recordMetric(
      'user_action_$action',
      1,
      MetricType.userAction,
    );
  }

  /// 获取性能报告
  PerformanceReport getReport() {
    final now = DateTime.now();
    final last24Hours = now.subtract(const Duration(hours: 24));
    
    final recentMetrics = _metrics
        .where((metric) => metric.timestamp.isAfter(last24Hours))
        .toList();
    
    return PerformanceReport(
      totalMetrics: recentMetrics.length,
      averagePageLoadTime: _calculateAverage(
        recentMetrics.where((m) => m.type == MetricType.pageLoad),
      ),
      averageApiCallTime: _calculateAverage(
        recentMetrics.where((m) => m.type == MetricType.apiCall),
      ),
      errorCount: recentMetrics
          .where((m) => m.type == MetricType.error)
          .length,
      userActions: recentMetrics
          .where((m) => m.type == MetricType.userAction)
          .length,
      slowOperations: recentMetrics
          .where((m) => m.type == MetricType.timing && m.value > 1000)
          .toList(),
    );
  }

  /// 清理旧数据
  void cleanup() {
    final cutoff = DateTime.now().subtract(const Duration(days: 7));
    _metrics.removeWhere((metric) => metric.timestamp.isBefore(cutoff));
  }

  /// 添加指标
  void _addMetric(PerformanceMetric metric) {
    _metrics.add(metric);
    
    // 限制内存中的指标数量
    if (_metrics.length > 10000) {
      _metrics.removeRange(0, 1000);
    }
  }

  /// 计算平均值
  double _calculateAverage(Iterable<PerformanceMetric> metrics) {
    if (metrics.isEmpty) return 0;
    
    final sum = metrics.fold<double>(0, (sum, metric) => sum + metric.value);
    return sum / metrics.length;
  }

  /// 开始内存监控
  void _startMemoryMonitoring() {
    _memoryTimer = Timer.periodic(const Duration(minutes: 1), (_) {
      recordMemoryUsage();
    });
  }

  /// 停止监控
  void dispose() {
    _memoryTimer?.cancel();
    _timers.clear();
    _metrics.clear();
  }
}

/// 性能指标
class PerformanceMetric {
  const PerformanceMetric({
    required this.name,
    required this.type,
    required this.value,
    required this.timestamp,
  });

  final String name;
  final MetricType type;
  final double value;
  final DateTime timestamp;
}

/// 性能报告
class PerformanceReport {
  const PerformanceReport({
    required this.totalMetrics,
    required this.averagePageLoadTime,
    required this.averageApiCallTime,
    required this.errorCount,
    required this.userActions,
    required this.slowOperations,
  });

  final int totalMetrics;
  final double averagePageLoadTime;
  final double averageApiCallTime;
  final int errorCount;
  final int userActions;
  final List<PerformanceMetric> slowOperations;

  @override
  String toString() {
    return '''
Performance Report:
- Total Metrics: $totalMetrics
- Average Page Load: ${averagePageLoadTime.toStringAsFixed(2)}ms
- Average API Call: ${averageApiCallTime.toStringAsFixed(2)}ms
- Errors: $errorCount
- User Actions: $userActions
- Slow Operations: ${slowOperations.length}
''';
  }
}

/// 指标类型枚举
enum MetricType {
  timing,
  pageLoad,
  apiCall,
  memory,
  error,
  userAction,
}

/// 性能监控装饰器
class PerformanceTracker {
  /// 跟踪函数执行时间
  static Future<T> track<T>(
    String name,
    Future<T> Function() function,
  ) async {
    final monitor = PerformanceMonitor();
    monitor.startTimer(name);
    
    try {
      final result = await function();
      monitor.endTimer(name);
      return result;
    } catch (error) {
      monitor.endTimer(name);
      monitor.recordMetric('error_$name', 1, MetricType.error);
      rethrow;
    }
  }

  /// 跟踪同步函数执行时间
  static T trackSync<T>(
    String name,
    T Function() function,
  ) {
    final monitor = PerformanceMonitor();
    monitor.startTimer(name);
    
    try {
      final result = function();
      monitor.endTimer(name);
      return result;
    } catch (error) {
      monitor.endTimer(name);
      monitor.recordMetric('error_$name', 1, MetricType.error);
      rethrow;
    }
  }
}

/// 页面性能监控Mixin
mixin PagePerformanceMixin {
  late final Stopwatch _pageStopwatch;
  
  void startPageLoad() {
    _pageStopwatch = Stopwatch()..start();
  }
  
  void endPageLoad(String pageName) {
    _pageStopwatch.stop();
    PerformanceMonitor().recordPageLoad(
      pageName,
      Duration(milliseconds: _pageStopwatch.elapsedMilliseconds),
    );
  }
}
