import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/shared/presentation/widgets/custom_card.dart';
import 'package:soko/features/recycle/data/datasources/recycle_api_service.dart';

/// 回收流程说明组件
class RecycleProcessSection extends StatelessWidget {

  const RecycleProcessSection({
    super.key,
    required this.processSteps,
  });
  final List<ProcessStep> processSteps;

  @override
  Widget build(BuildContext context) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.timeline,
                color: Colors.indigo,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                '回收流程',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const Spacer(),
              Text(
                '简单4步完成回收',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // 流程步骤
          ...processSteps.asMap().entries.map((entry) {
            final index = entry.key;
            final step = entry.value;
            final isLast = index == processSteps.length - 1;
            return _buildProcessStep(step, isLast);
          }),
        ],
      ),
    );
  }

  /// 构建流程步骤
  Widget _buildProcessStep(ProcessStep step, bool isLast) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 步骤指示器
        Column(
          children: [
            Container(
              width: 32.w,
              height: 32.w,
              decoration: BoxDecoration(
                color: Colors.indigo,
                borderRadius: BorderRadius.circular(16.r),
              ),
              child: Center(
                child: Text(
                  step.step.toString(),
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            if (!isLast)
              Container(
                width: 2.w,
                height: 40.h,
                color: Colors.indigo.withValues(alpha: 0.3),
                margin: EdgeInsets.symmetric(vertical: 8.h),
              ),
          ],
        ),
        SizedBox(width: 16.w),

        // 步骤内容
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(bottom: isLast ? 0 : 24.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题
                Text(
                  step.title,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                SizedBox(height: 4.h),

                // 描述
                Text(
                  step.description,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey[600],
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
