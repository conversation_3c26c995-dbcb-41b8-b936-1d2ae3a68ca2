import 'package:flutter/material.dart';

/// 应用颜色配置
class AppColors {
  // 私有构造函数
  AppColors._();
  
  // 主色调 - 基于中古虾品牌色
  static const Color primary = Color(0xFFFF6B6B);
  static const Color primaryLight = Color(0xFFFF9999);
  static const Color primaryDark = Color(0xFFE55555);
  
  // 辅助色
  static const Color secondary = Color(0xFF4ECDC4);
  static const Color secondaryLight = Color(0xFF7EDDD6);
  static const Color secondaryDark = Color(0xFF3BA99C);
  
  // 功能色
  static const Color success = Color(0xFF52C41A);
  static const Color warning = Color(0xFFFAAD14);
  static const Color error = Color(0xFFFF4D4F);
  static const Color info = Color(0xFF1890FF);
  
  // 中性色
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color background = Color(0xFFF5F5F5);
  static const Color surface = Color(0xFFFFFFFF);
  
  // 文本色
  static const Color textPrimary = Color(0xFF262626);
  static const Color textSecondary = Color(0xFF595959);
  static const Color textTertiary = Color(0xFF8C8C8C);
  static const Color textDisabled = Color(0xFFBFBFBF);
  static const Color textWhite = Color(0xFFFFFFFF);
  
  // 边框色
  static const Color border = Color(0xFFD9D9D9);
  static const Color borderLight = Color(0xFFF0F0F0);
  static const Color borderDark = Color(0xFFBFBFBF);
  
  // 分割线
  static const Color divider = Color(0xFFF0F0F0);
  
  // 阴影色
  static const Color shadow = Color(0x1A000000);
  static const Color shadowLight = Color(0x0D000000);
  
  // 特殊业务色
  static const Color price = Color(0xFFFF4D4F);
  static const Color originalPrice = Color(0xFF8C8C8C);
  static const Color discount = Color(0xFFFF6B35);
  static const Color vip = Color(0xFFFFD700);
  static const Color svip = Color(0xFFFF1493);
  
  // 状态色
  static const Color statusPending = Color(0xFFFAAD14);
  static const Color statusProcessing = Color(0xFF1890FF);
  static const Color statusSuccess = Color(0xFF52C41A);
  static const Color statusCancelled = Color(0xFF8C8C8C);
  static const Color statusFailed = Color(0xFFFF4D4F);
  
  // 渐变色
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondary, secondaryLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // 获取状态对应的颜色
  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
      case 'waiting':
        return statusPending;
      case 'processing':
      case 'shipping':
        return statusProcessing;
      case 'completed':
      case 'success':
        return statusSuccess;
      case 'cancelled':
        return statusCancelled;
      case 'failed':
      case 'error':
        return statusFailed;
      default:
        return textSecondary;
    }
  }
  
  // 获取会员等级对应的颜色
  static Color getMemberLevelColor(String level) {
    switch (level.toLowerCase()) {
      case 'vip':
        return vip;
      case 'svip':
        return svip;
      default:
        return textSecondary;
    }
  }
}
