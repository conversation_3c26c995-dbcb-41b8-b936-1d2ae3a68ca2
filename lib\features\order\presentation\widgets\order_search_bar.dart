import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 订单搜索栏组件
class OrderSearchBar extends StatefulWidget {
  const OrderSearchBar({
    super.key,
    this.onSearch,
    this.onFilterTap,
    this.hintText = '搜索订单号、收货人、手机号',
  });

  final ValueChanged<String>? onSearch;
  final VoidCallback? onFilterTap;
  final String hintText;

  @override
  State<OrderSearchBar> createState() => _OrderSearchBarState();
}

class _OrderSearchBarState extends State<OrderSearchBar> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      color: Colors.white,
      child: Row(
        children: [
          // 搜索框
          Expanded(
            child: Container(
              height: 40.h,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(20.r),
              ),
              child: TextField(
                controller: _controller,
                focusNode: _focusNode,
                onSubmitted: (value) => widget.onSearch?.call(value),
                decoration: InputDecoration(
                  hintText: widget.hintText,
                  hintStyle: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey[500],
                  ),
                  prefixIcon: Icon(
                    Icons.search,
                    size: 20.w,
                    color: Colors.grey[500],
                  ),
                  suffixIcon: _controller.text.isNotEmpty
                      ? GestureDetector(
                          onTap: _clearSearch,
                          child: Icon(
                            Icons.clear,
                            size: 20.w,
                            color: Colors.grey[500],
                          ),
                        )
                      : null,
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 16.w,
                    vertical: 10.h,
                  ),
                ),
                onChanged: (value) {
                  setState(() {});
                  // 实时搜索（可选）
                  if (value.isEmpty) {
                    widget.onSearch?.call('');
                  }
                },
              ),
            ),
          ),
          
          SizedBox(width: 12.w),
          
          // 筛选按钮
          GestureDetector(
            onTap: widget.onFilterTap,
            child: Container(
              width: 40.w,
              height: 40.h,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20.r),
              ),
              child: Icon(
                Icons.tune,
                size: 20.w,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 清空搜索
  void _clearSearch() {
    _controller.clear();
    _focusNode.unfocus();
    widget.onSearch?.call('');
    setState(() {});
  }
}
