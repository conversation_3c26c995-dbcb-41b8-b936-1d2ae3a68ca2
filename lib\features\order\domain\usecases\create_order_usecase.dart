import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/error/failures.dart';
import 'package:soko/core/usecases/usecase.dart';
import 'package:soko/features/order/data/repositories/order_repository_impl.dart';
import 'package:soko/features/order/domain/entities/order.dart';
import 'package:soko/features/order/domain/entities/order_create_request.dart';
import 'package:soko/features/order/domain/repositories/order_repository.dart';

/// 创建订单用例
class CreateOrderUseCase implements UseCase<Order, OrderCreateRequest> {

  CreateOrderUseCase(this.repository);
  final OrderRepository repository;

  @override
  Future<Either<Failure, Order>> call(OrderCreateRequest request) async {
    // 验证订单创建请求
    final validationResult = _validateOrderRequest(request);
    if (validationResult != null) {
      return Left(ValidationFailure(validationResult));
    }

    return repository.createOrderFromRequest(request);
  }

  /// 验证订单创建请求
  String? _validateOrderRequest(OrderCreateRequest request) {
    // 检查商品项是否为空
    if (request.items.isEmpty) {
      return '订单商品不能为空';
    }

    // 检查商品项数量
    for (final item in request.items) {
      if (item.quantity <= 0) {
        return '商品数量必须大于0';
      }
      if (item.price < 0) {
        return '商品价格不能为负数';
      }
    }

    // 检查地址ID
    if (request.addressId.isEmpty) {
      return '收货地址不能为空';
    }

    return null;
  }
}

/// CreateOrderUseCase 提供者
final createOrderUseCaseProvider = Provider<CreateOrderUseCase>((ref) {
  final repository = ref.read(orderRepositoryProvider);
  return CreateOrderUseCase(repository);
});
