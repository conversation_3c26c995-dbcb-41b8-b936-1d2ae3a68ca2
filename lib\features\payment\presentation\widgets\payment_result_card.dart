import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/services.dart';

/// 支付结果卡片组件
class PaymentResultCard extends StatelessWidget {
  const PaymentResultCard({
    super.key,
    required this.success,
    required this.paymentId,
    required this.orderId,
    required this.amount,
    this.errorMessage,
    this.tradeNo,
  });

  final bool success;
  final String paymentId;
  final String orderId;
  final double amount;
  final String? errorMessage;
  final String? tradeNo;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // 结果图标
          Container(
            width: 80.w,
            height: 80.w,
            decoration: BoxDecoration(
              color: success 
                  ? Colors.green.withValues(alpha: 0.1)
                  : Colors.red.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              success ? Icons.check_circle : Icons.error,
              size: 48.w,
              color: success ? Colors.green : Colors.red,
            ),
          ),
          SizedBox(height: 20.h),
          
          // 结果标题
          Text(
            success ? '支付成功' : '支付失败',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
              color: success ? Colors.green : Colors.red,
            ),
          ),
          SizedBox(height: 8.h),
          
          // 结果描述
          Text(
            success 
                ? '您的订单已支付成功，我们将尽快为您处理'
                : errorMessage ?? '支付过程中出现问题，请重试',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 24.h),
          
          // 支付信息
          _buildPaymentInfo(context),
          
          // 沙盒环境提示
          if (success) ...[
            SizedBox(height: 16.h),
            Container(
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: Colors.orange[50],
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: Colors.orange[200]!),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.science,
                    size: 16.w,
                    color: Colors.orange[600],
                  ),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      '这是沙盒环境测试，未产生真实扣费',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.orange[700],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建支付信息
  Widget _buildPaymentInfo(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: [
          _buildInfoRow('支付金额', '¥${amount.toStringAsFixed(2)}'),
          _buildInfoRow('订单号', orderId, copyable: true),
          _buildInfoRow('支付单号', paymentId, copyable: true),
          if (tradeNo != null)
            _buildInfoRow('交易号', tradeNo!, copyable: true),
          _buildInfoRow('支付时间', _formatDateTime(DateTime.now())),
        ],
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value, {bool copyable = false}) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        children: [
          SizedBox(
            width: 80.w,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    value,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey[800],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                if (copyable)
                  GestureDetector(
                    onTap: () => _copyToClipboard(value),
                    child: Icon(
                      Icons.copy,
                      size: 16.w,
                      color: Colors.grey[600],
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 复制到剪贴板
  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}:${dateTime.second.toString().padLeft(2, '0')}';
  }
}
