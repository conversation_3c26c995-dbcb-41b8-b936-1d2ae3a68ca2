import 'package:json_annotation/json_annotation.dart';

part 'category.g.dart';

/// 商品分类实体类
@JsonSerializable()
class ProductCategory {

  const ProductCategory({
    required this.id,
    required this.name,
    required this.code,
    this.description,
    this.icon,
    this.image,
    this.parentId,
    required this.level,
    required this.sort,
    required this.isActive,
    this.children,
    this.productCount,
    required this.createTime,
    required this.updateTime,
  });

  factory ProductCategory.fromJson(Map<String, dynamic> json) => 
      _$ProductCategoryFromJson(json);
  @Json<PERSON>ey(name: 'id')
  final String id;

  @Json<PERSON>ey(name: 'name')
  final String name;

  @Json<PERSON>ey(name: 'code')
  final String code;

  @Json<PERSON><PERSON>(name: 'description')
  final String? description;

  @JsonKey(name: 'icon')
  final String? icon;

  @Json<PERSON>ey(name: 'image')
  final String? image;

  @JsonKey(name: 'parentId')
  final String? parentId;

  @<PERSON>son<PERSON>ey(name: 'level')
  final int level;

  @<PERSON>sonKey(name: 'sort')
  final int sort;

  @Json<PERSON>ey(name: 'isActive')
  final bool isActive;

  @JsonKey(name: 'children')
  final List<ProductCategory>? children;

  @JsonKey(name: 'productCount')
  final int? productCount;

  @JsonKey(name: 'createTime')
  final int createTime;

  @JsonKey(name: 'updateTime')
  final int updateTime;

  Map<String, dynamic> toJson() => _$ProductCategoryToJson(this);

  /// 是否为根分类
  bool get isRoot => level == 0;

  /// 是否有子分类
  bool get hasChildren => children != null && children!.isNotEmpty;

  /// 获取所有子分类ID
  List<String> get allChildrenIds {
    if (!hasChildren) return [];
    
    final ids = <String>[];
    for (final child in children!) {
      ids.add(child.id);
      ids.addAll(child.allChildrenIds);
    }
    return ids;
  }

  /// 获取分类路径
  List<ProductCategory> getCategoryPath(List<ProductCategory> allCategories) {
    final path = <ProductCategory>[this];
    
    var currentParentId = parentId;
    while (currentParentId != null) {
      final parent = allCategories.firstWhere(
        (cat) => cat.id == currentParentId,
        orElse: () => throw Exception('Parent category not found'),
      );
      path.insert(0, parent);
      currentParentId = parent.parentId;
    }
    
    return path;
  }

  /// 是否为ACG分类
  bool get isAcgCategory {
    const acgCodes = ['ultraman', 'kamen_rider', 'super_sentai', 'gundam'];
    return acgCodes.contains(code);
  }

  /// 获取ACG类型
  String? get acgType {
    if (!isAcgCategory) return null;
    return code;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductCategory && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ProductCategory(id: $id, name: $name, code: $code, level: $level)';
  }
}

/// ACG分类枚举
enum AcgCategory {
  ultraman('ultraman', '奥特曼', '🦸‍♂️'),
  kamenRider('kamen_rider', '假面骑士', '🏍️'),
  superSentai('super_sentai', '超级战队', '🤖'),
  gundam('gundam', '高达', '🚀');

  const AcgCategory(this.code, this.name, this.emoji);

  final String code;
  final String name;
  final String emoji;

  /// 从代码获取ACG分类
  static AcgCategory? fromCode(String code) {
    for (final category in AcgCategory.values) {
      if (category.code == code) return category;
    }
    return null;
  }

  /// 获取显示名称
  String get displayName => '$emoji $name';
}

/// 分类筛选条件
class CategoryFilter {

  const CategoryFilter({
    this.categoryId,
    this.acgType,
    this.level,
    this.hasProducts,
  });
  final String? categoryId;
  final String? acgType;
  final int? level;
  final bool? hasProducts;

  CategoryFilter copyWith({
    String? categoryId,
    String? acgType,
    int? level,
    bool? hasProducts,
  }) {
    return CategoryFilter(
      categoryId: categoryId ?? this.categoryId,
      acgType: acgType ?? this.acgType,
      level: level ?? this.level,
      hasProducts: hasProducts ?? this.hasProducts,
    );
  }

  /// 是否为空筛选条件
  bool get isEmpty {
    return categoryId == null &&
           acgType == null &&
           level == null &&
           hasProducts == null;
  }

  /// 转换为查询参数
  Map<String, dynamic> toQueryParams() {
    final params = <String, dynamic>{};
    
    if (categoryId != null) params['categoryId'] = categoryId;
    if (acgType != null) params['acgType'] = acgType;
    if (level != null) params['level'] = level;
    if (hasProducts != null) params['hasProducts'] = hasProducts;
    
    return params;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CategoryFilter &&
           other.categoryId == categoryId &&
           other.acgType == acgType &&
           other.level == level &&
           other.hasProducts == hasProducts;
  }

  @override
  int get hashCode {
    return Object.hash(categoryId, acgType, level, hasProducts);
  }

  @override
  String toString() {
    return 'CategoryFilter(categoryId: $categoryId, acgType: $acgType, level: $level, hasProducts: $hasProducts)';
  }
}
