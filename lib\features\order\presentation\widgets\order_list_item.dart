import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';

import 'package:soko/core/enums/app_enums.dart';
import 'package:soko/core/utils/date_utils.dart' as app_date_utils;
import 'package:soko/shared/presentation/widgets/custom_button.dart';
import 'package:soko/features/order/domain/entities/order.dart';

/// 订单列表项组件
class OrderListItem extends StatelessWidget {

  const OrderListItem({
    super.key,
    required this.order,
    this.onTap,
    this.onCancel,
    this.onPay,
    this.onConfirm,
    this.onRefund,
    this.onDelete,
  });
  final Order order;
  final VoidCallback? onTap;
  final Function(String orderId)? onCancel;
  final Function(String orderId)? onPay;
  final Function(String orderId)? onConfirm;
  final Function(String orderId)? onRefund;
  final Function(String orderId)? onDelete;

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              SizedBox(height: 12.h),
              _buildItems(),
              SizedBox(height: 12.h),
              _buildFooter(),
              if (_shouldShowActions()) ...[
                SizedBox(height: 12.h),
                _buildActions(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// 构建头部信息
  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          '订单号：${order.orderNo}',
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.grey[600],
          ),
        ),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
          decoration: BoxDecoration(
            color: _getStatusColor().withOpacity(0.1),
            borderRadius: BorderRadius.circular(4.r),
          ),
          child: Text(
            _getStatusText(),
            style: TextStyle(
              fontSize: 12.sp,
              color: _getStatusColor(),
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建商品列表
  Widget _buildItems() {
    return Column(
      children: order.items.map(_buildItem).toList(),
    );
  }

  /// 构建单个商品项
  Widget _buildItem(OrderItem item) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8.r),
            child: item.productImage?.isNotEmpty == true
                ? CachedNetworkImage(
                    imageUrl: item.productImage!,
                    width: 60.w,
                    height: 60.w,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      width: 60.w,
                      height: 60.w,
                      color: Colors.grey[200],
                      child: Icon(Icons.image, color: Colors.grey[400]),
                    ),
                    errorWidget: (context, url, error) => Container(
                      width: 60.w,
                      height: 60.w,
                      color: Colors.grey[200],
                      child: Icon(Icons.broken_image, color: Colors.grey[400]),
                    ),
                  )
                : Container(
                    width: 60.w,
                    height: 60.w,
                    color: Colors.grey[200],
                    child: Icon(Icons.image, color: Colors.grey[400]),
                  ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.productName,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                if (item.skuName?.isNotEmpty == true) ...[
                  SizedBox(height: 4.h),
                  Text(
                    item.skuName!,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
                SizedBox(height: 4.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '¥${item.price.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.red,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      'x${item.quantity}',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建底部信息
  Widget _buildFooter() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          app_date_utils.DateUtils.formatDateTime(order.createdAt),
          style: TextStyle(
            fontSize: 12.sp,
            color: Colors.grey[600],
          ),
        ),
        RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: '共${order.items.length}件商品 总计：',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.grey[600],
                ),
              ),
              TextSpan(
                text: '¥${order.totalAmount.toStringAsFixed(2)}',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.red,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建操作按钮
  Widget _buildActions() {
    final actions = <Widget>[];

    switch (order.statusEnum) {
      case OrderStatus.pending:
        actions.addAll([
          CustomButton(
            text: '取消订单',
            onPressed: () => onCancel?.call(order.id),
            type: ButtonType.outline,
            size: ButtonSize.small,
          ),
          SizedBox(width: 8.w),
          CustomButton(
            text: '立即支付',
            onPressed: () => onPay?.call(order.id),
            size: ButtonSize.small,
          ),
        ]);
      case OrderStatus.paid:
        // 已支付，等待发货，暂无操作
        break;
      case OrderStatus.shipped:
        actions.add(
          CustomButton(
            text: '确认收货',
            onPressed: () => onConfirm?.call(order.id),
            size: ButtonSize.small,
          ),
        );
      case OrderStatus.completed:
        actions.add(
          CustomButton(
            text: '申请退款',
            onPressed: () => onRefund?.call(order.id),
            type: ButtonType.outline,
            size: ButtonSize.small,
          ),
        );
      case OrderStatus.cancelled:
        actions.add(
          CustomButton(
            text: '删除订单',
            onPressed: () => onDelete?.call(order.id),
            type: ButtonType.outline,
            size: ButtonSize.small,
          ),
        );
      case OrderStatus.refunded:
        // 已退款，暂无操作
        break;
      case OrderStatus.delivered:
        // 已送达，暂无操作
        break;
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: actions,
    );
  }

  /// 获取状态文本
  String _getStatusText() {
    switch (order.statusEnum) {
      case OrderStatus.pending:
        return '待付款';
      case OrderStatus.paid:
        return '已付款';
      case OrderStatus.shipped:
        return '已发货';
      case OrderStatus.delivered:
        return '已送达';
      case OrderStatus.completed:
        return '已完成';
      case OrderStatus.cancelled:
        return '已取消';
      case OrderStatus.refunded:
        return '已退款';
    }
  }

  /// 获取状态颜色
  Color _getStatusColor() {
    switch (order.statusEnum) {
      case OrderStatus.pending:
        return Colors.orange;
      case OrderStatus.paid:
        return Colors.blue;
      case OrderStatus.shipped:
        return Colors.green;
      case OrderStatus.delivered:
        return Colors.green;
      case OrderStatus.completed:
        return Colors.green;
      case OrderStatus.cancelled:
        return Colors.grey;
      case OrderStatus.refunded:
        return Colors.purple;
    }
  }

  /// 是否显示操作按钮
  bool _shouldShowActions() {
    return order.statusEnum != OrderStatus.paid &&
        order.statusEnum != OrderStatus.refunded;
  }
}
