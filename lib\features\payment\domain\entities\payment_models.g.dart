// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PaymentRequest _$PaymentRequestFromJson(Map<String, dynamic> json) =>
    PaymentRequest(
      orderId: json['orderId'] as String,
      amount: (json['amount'] as num).toDouble(),
      subject: json['subject'] as String,
      body: json['body'] as String,
      paymentMethod: $enumDecode(_$PaymentMethodEnumMap, json['paymentMethod']),
      notifyUrl: json['notifyUrl'] as String?,
      returnUrl: json['returnUrl'] as String?,
      timeoutExpress: json['timeoutExpress'] as String?,
      passbackParams: json['passbackParams'] as String?,
    );

Map<String, dynamic> _$PaymentRequestToJson(PaymentRequest instance) =>
    <String, dynamic>{
      'orderId': instance.orderId,
      'amount': instance.amount,
      'subject': instance.subject,
      'body': instance.body,
      'paymentMethod': _$PaymentMethodEnumMap[instance.paymentMethod]!,
      'notifyUrl': instance.notifyUrl,
      'returnUrl': instance.returnUrl,
      'timeoutExpress': instance.timeoutExpress,
      'passbackParams': instance.passbackParams,
    };

const _$PaymentMethodEnumMap = {
  PaymentMethod.alipay: 'alipay',
  PaymentMethod.wechat: 'wechat',
  PaymentMethod.bankCard: 'bankCard',
  PaymentMethod.balance: 'balance',
};

PaymentResponse _$PaymentResponseFromJson(Map<String, dynamic> json) =>
    PaymentResponse(
      success: json['success'] as bool,
      paymentId: json['paymentId'] as String,
      orderString: json['orderString'] as String?,
      qrCode: json['qrCode'] as String?,
      redirectUrl: json['redirectUrl'] as String?,
      errorCode: json['errorCode'] as String?,
      errorMessage: json['errorMessage'] as String?,
      tradeNo: json['tradeNo'] as String?,
    );

Map<String, dynamic> _$PaymentResponseToJson(PaymentResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'paymentId': instance.paymentId,
      'orderString': instance.orderString,
      'qrCode': instance.qrCode,
      'redirectUrl': instance.redirectUrl,
      'errorCode': instance.errorCode,
      'errorMessage': instance.errorMessage,
      'tradeNo': instance.tradeNo,
    };

PaymentResult _$PaymentResultFromJson(Map<String, dynamic> json) =>
    PaymentResult(
      success: json['success'] as bool,
      paymentId: json['paymentId'] as String,
      orderId: json['orderId'] as String,
      tradeNo: json['tradeNo'] as String?,
      totalAmount: json['totalAmount'] as String?,
      receiptAmount: json['receiptAmount'] as String?,
      buyerPayAmount: json['buyerPayAmount'] as String?,
      pointAmount: json['pointAmount'] as String?,
      invoiceAmount: json['invoiceAmount'] as String?,
      gmtPayment: json['gmtPayment'] as String?,
      fundBillList: (json['fundBillList'] as List<dynamic>?)
          ?.map((e) => FundBill.fromJson(e as Map<String, dynamic>))
          .toList(),
      errorCode: json['errorCode'] as String?,
      errorMessage: json['errorMessage'] as String?,
      memo: json['memo'] as String?,
    );

Map<String, dynamic> _$PaymentResultToJson(PaymentResult instance) =>
    <String, dynamic>{
      'success': instance.success,
      'paymentId': instance.paymentId,
      'orderId': instance.orderId,
      'tradeNo': instance.tradeNo,
      'totalAmount': instance.totalAmount,
      'receiptAmount': instance.receiptAmount,
      'buyerPayAmount': instance.buyerPayAmount,
      'pointAmount': instance.pointAmount,
      'invoiceAmount': instance.invoiceAmount,
      'gmtPayment': instance.gmtPayment,
      'fundBillList': instance.fundBillList,
      'errorCode': instance.errorCode,
      'errorMessage': instance.errorMessage,
      'memo': instance.memo,
    };

FundBill _$FundBillFromJson(Map<String, dynamic> json) => FundBill(
      fundChannel: json['fundChannel'] as String,
      amount: json['amount'] as String,
      realAmount: json['realAmount'] as String?,
    );

Map<String, dynamic> _$FundBillToJson(FundBill instance) => <String, dynamic>{
      'fundChannel': instance.fundChannel,
      'amount': instance.amount,
      'realAmount': instance.realAmount,
    };

PaymentRecord _$PaymentRecordFromJson(Map<String, dynamic> json) =>
    PaymentRecord(
      id: json['id'] as String,
      orderId: json['orderId'] as String,
      paymentId: json['paymentId'] as String,
      amount: (json['amount'] as num).toDouble(),
      paymentMethod: $enumDecode(_$PaymentMethodEnumMap, json['paymentMethod']),
      status: $enumDecode(_$PaymentStatusEnumMap, json['status']),
      createTime: (json['createTime'] as num).toInt(),
      payTime: (json['payTime'] as num?)?.toInt(),
      tradeNo: json['tradeNo'] as String?,
      errorCode: json['errorCode'] as String?,
      errorMessage: json['errorMessage'] as String?,
      notifyData: json['notifyData'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$PaymentRecordToJson(PaymentRecord instance) =>
    <String, dynamic>{
      'id': instance.id,
      'orderId': instance.orderId,
      'paymentId': instance.paymentId,
      'amount': instance.amount,
      'paymentMethod': _$PaymentMethodEnumMap[instance.paymentMethod]!,
      'status': _$PaymentStatusEnumMap[instance.status]!,
      'createTime': instance.createTime,
      'payTime': instance.payTime,
      'tradeNo': instance.tradeNo,
      'errorCode': instance.errorCode,
      'errorMessage': instance.errorMessage,
      'notifyData': instance.notifyData,
    };

const _$PaymentStatusEnumMap = {
  PaymentStatus.pending: 'pending',
  PaymentStatus.success: 'success',
  PaymentStatus.failed: 'failed',
  PaymentStatus.cancelled: 'cancelled',
  PaymentStatus.timeout: 'timeout',
};
