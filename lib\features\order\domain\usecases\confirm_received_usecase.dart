import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/error/failures.dart';
import 'package:soko/core/usecases/usecase.dart';
import 'package:soko/features/order/data/repositories/order_repository_impl.dart';
import 'package:soko/features/order/domain/repositories/order_repository.dart';

/// 确认收货用例
class ConfirmReceivedUseCase implements UseCase<bool, String> {

  ConfirmReceivedUseCase(this.repository);
  final OrderRepository repository;

  @override
  Future<Either<Failure, bool>> call(String orderId) async {
    return repository.confirmReceived(orderId);
  }
}

/// ConfirmReceivedUseCase 提供者
final confirmReceivedUseCaseProvider = Provider<ConfirmReceivedUseCase>((ref) {
  final repository = ref.read(orderRepositoryProvider);
  return ConfirmReceivedUseCase(repository);
});
