import 'package:soko/core/api/base_api_service.dart';
import 'package:soko/core/network/api_response.dart';
import 'package:soko/features/cart/domain/entities/cart_item.dart';

/// 购物车API服务
class CartApiService extends BaseApiService {
  static const String _cartListPath = '/cart/list';
  static const String _cartAddPath = '/cart/add';
  static const String _cartUpdatePath = '/cart/update';
  static const String _cartRemovePath = '/cart/remove';
  static const String _cartClearPath = '/cart/clear';
  static const String _cartSummaryPath = '/cart/summary';
  static const String _cartSelectAllPath = '/cart/select-all';

  /// 获取购物车列表
  Future<ApiResponse<List<CartItem>>> getCartList() async {
    return get<List<CartItem>>(
      _cartListPath,
      fromJson: (data) => (data as List<dynamic>)
          .map((item) => CartItem.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  /// 添加到购物车
  Future<ApiResponse<CartItem>> addToCart({
    required AddToCartRequest request,
  }) async {
    return post<CartItem>(
      _cartAddPath,
      data: request.toJson(),
      fromJson: (data) => CartItem.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 更新购物车项
  Future<ApiResponse<CartItem>> updateCartItem({
    required String cartItemId,
    required UpdateCartItemRequest request,
  }) async {
    return put<CartItem>(
      '$_cartUpdatePath/$cartItemId',
      data: request.toJson(),
      fromJson: (data) => CartItem.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 删除购物车项
  Future<ApiResponse<void>> removeCartItem({
    required String cartItemId,
  }) async {
    return delete<void>('$_cartRemovePath/$cartItemId');
  }

  /// 批量删除购物车项
  Future<ApiResponse<void>> removeCartItems({
    required List<String> cartItemIds,
  }) async {
    return delete<void>(
      _cartRemovePath,
      data: {'cartItemIds': cartItemIds},
    );
  }

  /// 清空购物车
  Future<ApiResponse<void>> clearCart() async {
    return delete<void>(_cartClearPath);
  }

  /// 获取购物车统计信息
  Future<ApiResponse<CartSummary>> getCartSummary() async {
    return get<CartSummary>(
      _cartSummaryPath,
      fromJson: (data) => CartSummary.fromJson(data as Map<String, dynamic>),
    );
  }

  /// 全选/取消全选
  Future<ApiResponse<void>> selectAll({
    required bool selected,
  }) async {
    return post<void>(
      _cartSelectAllPath,
      data: {'selected': selected},
    );
  }

  /// 批量更新购物车项选中状态
  Future<ApiResponse<void>> updateSelectedStatus({
    required List<String> cartItemIds,
    required bool selected,
  }) async {
    return put<void>(
      '/cart/select',
      data: {
        'cartItemIds': cartItemIds,
        'selected': selected,
      },
    );
  }

  /// 获取购物车数量
  Future<ApiResponse<int>> getCartCount() async {
    return get<int>(
      '/cart/count',
      fromJson: (data) => data as int,
    );
  }

  /// 检查购物车商品库存
  Future<ApiResponse<List<CartValidationResult>>> validateCart() async {
    return get<List<CartValidationResult>>(
      '/cart/validate',
      fromJson: (data) => (data as List<dynamic>)
          .map((item) => CartValidationResult.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  /// 合并购物车（登录后合并本地购物车）
  Future<ApiResponse<List<CartItem>>> mergeCart({
    required List<AddToCartRequest> localCartItems,
  }) async {
    return post<List<CartItem>>(
      '/cart/merge',
      data: {
        'items': localCartItems.map((item) => item.toJson()).toList(),
      },
      fromJson: (data) => (data as List<dynamic>)
          .map((item) => CartItem.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  /// 计算运费
  Future<ApiResponse<FreightCalculationResult>> calculateFreight({
    required List<String> cartItemIds,
    required String addressId,
  }) async {
    return post<FreightCalculationResult>(
      '/cart/freight',
      data: {
        'cartItemIds': cartItemIds,
        'addressId': addressId,
      },
      fromJson: (data) => FreightCalculationResult.fromJson(data as Map<String, dynamic>),
    );
  }
}

/// 购物车验证结果
class CartValidationResult {

  const CartValidationResult({
    required this.cartItemId,
    required this.productId,
    this.skuId,
    required this.available,
    this.reason,
    this.maxQuantity,
    this.currentPrice,
  });

  factory CartValidationResult.fromJson(Map<String, dynamic> json) {
    return CartValidationResult(
      cartItemId: json['cartItemId'] as String,
      productId: json['productId'] as String,
      skuId: json['skuId'] as String?,
      available: json['available'] as bool,
      reason: json['reason'] as String?,
      maxQuantity: json['maxQuantity'] as int?,
      currentPrice: (json['currentPrice'] as num?)?.toDouble(),
    );
  }
  final String cartItemId;
  final String productId;
  final String? skuId;
  final bool available;
  final String? reason;
  final int? maxQuantity;
  final double? currentPrice;

  Map<String, dynamic> toJson() {
    return {
      'cartItemId': cartItemId,
      'productId': productId,
      'skuId': skuId,
      'available': available,
      'reason': reason,
      'maxQuantity': maxQuantity,
      'currentPrice': currentPrice,
    };
  }
}

/// 运费计算结果
class FreightCalculationResult {

  const FreightCalculationResult({
    required this.domesticFreight,
    required this.internationalFreight,
    required this.totalFreight,
    required this.freeShipping,
    this.freeShippingThreshold,
  });

  factory FreightCalculationResult.fromJson(Map<String, dynamic> json) {
    return FreightCalculationResult(
      domesticFreight: (json['domesticFreight'] as num).toDouble(),
      internationalFreight: (json['internationalFreight'] as num).toDouble(),
      totalFreight: (json['totalFreight'] as num).toDouble(),
      freeShipping: json['freeShipping'] as bool,
      freeShippingThreshold: json['freeShippingThreshold'] as String?,
    );
  }
  final double domesticFreight;
  final double internationalFreight;
  final double totalFreight;
  final bool freeShipping;
  final String? freeShippingThreshold;

  Map<String, dynamic> toJson() {
    return {
      'domesticFreight': domesticFreight,
      'internationalFreight': internationalFreight,
      'totalFreight': totalFreight,
      'freeShipping': freeShipping,
      'freeShippingThreshold': freeShippingThreshold,
    };
  }
}
