import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import 'package:soko/shared/presentation/widgets/custom_app_bar.dart';
import 'package:soko/shared/presentation/widgets/loading_widget.dart';
import 'package:soko/shared/presentation/widgets/error_retry_widget.dart';
import 'package:soko/features/product/presentation/providers/product_compare_provider.dart';
import 'package:soko/features/product/presentation/widgets/product_compare_card.dart';
import 'package:soko/features/product/presentation/widgets/product_compare_table.dart';
import 'package:soko/features/product/domain/entities/product.dart';

/// 商品比较页面
class ProductComparePage extends ConsumerStatefulWidget {
  const ProductComparePage({
    super.key,
    this.productIds,
  });

  final List<String>? productIds;

  @override
  ConsumerState<ProductComparePage> createState() => _ProductComparePageState();
}

class _ProductComparePageState extends ConsumerState<ProductComparePage> {
  @override
  void initState() {
    super.initState();
    
    // 如果有传入的商品ID，则加载这些商品进行比较
    if (widget.productIds != null && widget.productIds!.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        for (final productId in widget.productIds!) {
          ref.read(productCompareProvider.notifier).addProduct(productId);
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final compareState = ref.watch(productCompareProvider);

    return Scaffold(
      appBar: CustomAppBar(
        title: '商品比较',
        showBackButton: true,
        actions: [
          if (compareState.products.isNotEmpty)
            IconButton(
              onPressed: _clearAll,
              icon: const Icon(Icons.clear_all),
              tooltip: '清空所有',
            ),
        ],
      ),
      body: compareState.when(
        data: (data) => _buildCompareContent(data),
        loading: () => const LoadingWidget(),
        error: (error, stackTrace) => ErrorRetryWidget(
          message: error.toString(),
          onRetry: () => ref.read(productCompareProvider.notifier).refresh(),
        ),
      ),
      floatingActionButton: compareState.maybeWhen(
        data: (data) => data.products.length < 4 ? FloatingActionButton(
          onPressed: _addProduct,
          child: const Icon(Icons.add),
        ) : null,
        orElse: () => null,
      ),
    );
  }

  /// 构建比较内容
  Widget _buildCompareContent(ProductCompareState data) {
    if (data.products.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        // 商品卡片列表
        Container(
          height: 200.h,
          padding: EdgeInsets.symmetric(vertical: 16.h),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            itemCount: data.products.length + (data.products.length < 4 ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == data.products.length) {
                // 添加商品按钮
                return _buildAddProductCard();
              }
              
              final product = data.products[index];
              return ProductCompareCard(
                product: product,
                onRemove: () => _removeProduct(product.id),
                onTap: () => _navigateToProductDetail(product.id),
              );
            },
          ),
        ),
        
        // 比较表格
        Expanded(
          child: ProductCompareTable(
            products: data.products,
          ),
        ),
      ],
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.compare_arrows,
              size: 80.w,
              color: Colors.grey[400],
            ),
            SizedBox(height: 24.h),
            Text(
              '开始比较商品',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              '添加商品来比较价格、规格和特性',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 32.h),
            ElevatedButton.icon(
              onPressed: _addProduct,
              icon: const Icon(Icons.add),
              label: const Text('添加商品'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建添加商品卡片
  Widget _buildAddProductCard() {
    return Container(
      width: 150.w,
      margin: EdgeInsets.only(right: 12.w),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: Colors.grey[300]!,
          style: BorderStyle.solid,
        ),
      ),
      child: InkWell(
        onTap: _addProduct,
        borderRadius: BorderRadius.circular(12.r),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_circle_outline,
              size: 32.w,
              color: Theme.of(context).primaryColor,
            ),
            SizedBox(height: 8.h),
            Text(
              '添加商品',
              style: TextStyle(
                fontSize: 12.sp,
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 添加商品
  void _addProduct() {
    // 导航到商品选择页面
    context.push('/product/select').then((result) {
      if (result is String) {
        ref.read(productCompareProvider.notifier).addProduct(result);
      }
    });
  }

  /// 移除商品
  void _removeProduct(String productId) {
    ref.read(productCompareProvider.notifier).removeProduct(productId);
  }

  /// 清空所有商品
  void _clearAll() {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清空所有商品'),
        content: const Text('确定要清空所有比较的商品吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(productCompareProvider.notifier).clearAll();
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 导航到商品详情
  void _navigateToProductDetail(String productId) {
    context.push('/product/$productId');
  }
}
