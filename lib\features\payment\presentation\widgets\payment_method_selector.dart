import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/core/enums/app_enums.dart';
import 'package:soko/shared/presentation/widgets/custom_card.dart';

/// 支付方式选择器
class PaymentMethodSelector extends StatelessWidget {

  const PaymentMethodSelector({
    super.key,
    this.selectedMethod,
    required this.onMethodSelected,
  });
  final PaymentMethod? selectedMethod;
  final ValueChanged<PaymentMethod> onMethodSelected;

  @override
  Widget build(BuildContext context) {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.payment,
                color: Colors.purple,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                '选择支付方式',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // 支付方式列表
          ...PaymentMethod.values.map(_buildPaymentOption),
        ],
      ),
    );
  }

  /// 构建支付方式选项
  Widget _buildPaymentOption(PaymentMethod method) {
    final isSelected = selectedMethod == method;

    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: InkWell(
        onTap: () => onMethodSelected(method),
        borderRadius: BorderRadius.circular(8.r),
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: isSelected 
                ? Colors.blue.withValues(alpha: 0.05)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(
              color: isSelected 
                  ? Colors.blue 
                  : Colors.grey.withValues(alpha: 0.3),
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              // 支付方式图标
              Container(
                width: 48.w,
                height: 48.w,
                decoration: BoxDecoration(
                  color: _getPaymentMethodColor(method).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  _getPaymentMethodIcon(method),
                  color: _getPaymentMethodColor(method),
                  size: 24.w,
                ),
              ),
              SizedBox(width: 16.w),

              // 支付方式信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getPaymentMethodName(method),
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      _getPaymentMethodDescription(method),
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.grey[600],
                      ),
                    ),
                    // 沙盒环境标识
                    SizedBox(height: 4.h),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                      decoration: BoxDecoration(
                        color: Colors.orange.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: Text(
                        '沙盒环境',
                        style: TextStyle(
                          fontSize: 10.sp,
                          color: Colors.orange[700],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // 选择指示器
              Container(
                width: 24.w,
                height: 24.w,
                decoration: BoxDecoration(
                  color: isSelected ? Colors.blue : Colors.transparent,
                  border: Border.all(
                    color: isSelected ? Colors.blue : Colors.grey,
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: isSelected
                    ? Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 16.w,
                      )
                    : null,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 获取支付方式图标
  IconData _getPaymentMethodIcon(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.alipay:
        return Icons.account_balance_wallet;
      case PaymentMethod.wechat:
        return Icons.chat;
      case PaymentMethod.unionpay:
        return Icons.credit_card;
      case PaymentMethod.balance:
        return Icons.account_balance;
    }
  }

  /// 获取支付方式颜色
  Color _getPaymentMethodColor(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.alipay:
        return const Color(0xFF1677FF);
      case PaymentMethod.wechat:
        return const Color(0xFF07C160);
      case PaymentMethod.unionpay:
        return const Color(0xFFE60012);
      case PaymentMethod.balance:
        return const Color(0xFFFF6B35);
    }
  }

  /// 获取支付方式名称
  String _getPaymentMethodName(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.alipay:
        return '支付宝';
      case PaymentMethod.wechat:
        return '微信支付';
      case PaymentMethod.unionpay:
        return '银联支付';
      case PaymentMethod.balance:
        return '余额支付';
    }
  }

  /// 获取支付方式描述
  String _getPaymentMethodDescription(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.alipay:
        return '推荐使用，支持花呗分期';
      case PaymentMethod.wechat:
        return '微信用户首选支付方式';
      case PaymentMethod.unionpay:
        return '银行卡直接支付，安全可靠';
      case PaymentMethod.balance:
        return '账户余额支付，即时到账';
    }
  }
}
