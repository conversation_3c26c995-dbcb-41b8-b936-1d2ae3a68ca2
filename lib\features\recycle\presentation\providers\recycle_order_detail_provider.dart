import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/state/base_state.dart';
import 'package:soko/features/recycle/domain/entities/recycle_order.dart';
import 'package:soko/features/recycle/domain/repositories/recycle_repository.dart';
import 'package:soko/features/recycle/data/repositories/recycle_repository_impl.dart';

/// 回收订单详情状态管理
class RecycleOrderDetailNotifier extends StateNotifier<BaseState<RecycleOrder>> {
  RecycleOrderDetailNotifier(this._recycleRepository, this._orderId) 
      : super(const BaseState<RecycleOrder>.initial());

  final RecycleRepository _recycleRepository;
  final String _orderId;

  /// 加载订单详情
  Future<void> loadOrderDetail() async {
    state = const BaseState<RecycleOrder>.loading();

    try {
      final order = await _recycleRepository.getRecycleOrderDetail(_orderId);
      state = BaseState<RecycleOrder>.success(order);
    } catch (error) {
      state = BaseState<RecycleOrder>.error(error.toString());
    }
  }

  /// 刷新订单详情
  Future<void> refreshOrderDetail() async {
    await loadOrderDetail();
  }

  /// 取消订单
  Future<void> cancelOrder() async {
    try {
      await _recycleRepository.cancelRecycleOrder(_orderId);
      
      // 重新加载订单详情以获取最新状态
      await loadOrderDetail();
    } catch (error) {
      // 这里可以显示错误提示
      rethrow;
    }
  }

  /// 确认价格
  Future<void> confirmPrice() async {
    try {
      // TODO: 实现确认价格的API调用
      // await _recycleRepository.confirmPrice(_orderId);
      
      // 重新加载订单详情以获取最新状态
      await loadOrderDetail();
    } catch (error) {
      rethrow;
    }
  }

  /// 确认寄送
  Future<void> confirmShipment(Map<String, dynamic> shippingInfo) async {
    try {
      await _recycleRepository.confirmShipment(_orderId, shippingInfo);
      
      // 重新加载订单详情以获取最新状态
      await loadOrderDetail();
    } catch (error) {
      rethrow;
    }
  }

  /// 提交评价
  Future<void> submitReview(Map<String, dynamic> reviewData) async {
    try {
      // TODO: 实现提交评价的API调用
      // await _recycleRepository.submitReview(_orderId, reviewData);
      
      // 重新加载订单详情以获取最新状态
      await loadOrderDetail();
    } catch (error) {
      rethrow;
    }
  }
}

/// 回收订单详情Provider
final recycleOrderDetailProvider = StateNotifierProvider.family<
    RecycleOrderDetailNotifier, 
    BaseState<RecycleOrder>, 
    String>((ref, orderId) {
  final recycleRepository = ref.watch(recycleRepositoryProvider);
  return RecycleOrderDetailNotifier(recycleRepository, orderId);
});
