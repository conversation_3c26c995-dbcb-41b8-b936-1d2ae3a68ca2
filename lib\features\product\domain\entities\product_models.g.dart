// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Product _$ProductFromJson(Map<String, dynamic> json) => Product(
      id: json['id'] as String,
      name: json['name'] as String,
      brandId: json['brandId'] as String,
      brandName: json['brandName'] as String,
      categoryId: json['categoryId'] as String,
      categoryName: json['categoryName'] as String,
      price: (json['price'] as num).toDouble(),
      originalPrice: (json['originalPrice'] as num).toDouble(),
      condition: json['condition'] as String,
      conditionDesc: json['conditionDesc'] as String,
      images:
          (json['images'] as List<dynamic>).map((e) => e as String).toList(),
      description: json['description'] as String,
      specifications: Map<String, String>.from(json['specifications'] as Map),
      isAvailable: json['isAvailable'] as bool,
      stock: (json['stock'] as num).toInt(),
      sellerId: json['sellerId'] as String,
      sellerName: json['sellerName'] as String,
      sellerRating: (json['sellerRating'] as num?)?.toDouble(),
      location: json['location'] as String,
      createTime: (json['createTime'] as num).toInt(),
      updateTime: (json['updateTime'] as num).toInt(),
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      discount: (json['discount'] as num?)?.toDouble(),
      viewCount: (json['viewCount'] as num?)?.toInt(),
      favoriteCount: (json['favoriteCount'] as num?)?.toInt(),
    );

Map<String, dynamic> _$ProductToJson(Product instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'brandId': instance.brandId,
      'brandName': instance.brandName,
      'categoryId': instance.categoryId,
      'categoryName': instance.categoryName,
      'price': instance.price,
      'originalPrice': instance.originalPrice,
      'condition': instance.condition,
      'conditionDesc': instance.conditionDesc,
      'images': instance.images,
      'description': instance.description,
      'specifications': instance.specifications,
      'isAvailable': instance.isAvailable,
      'stock': instance.stock,
      'sellerId': instance.sellerId,
      'sellerName': instance.sellerName,
      'sellerRating': instance.sellerRating,
      'location': instance.location,
      'createTime': instance.createTime,
      'updateTime': instance.updateTime,
      'tags': instance.tags,
      'discount': instance.discount,
      'viewCount': instance.viewCount,
      'favoriteCount': instance.favoriteCount,
    };

ProductCategory _$ProductCategoryFromJson(Map<String, dynamic> json) =>
    ProductCategory(
      id: json['id'] as String,
      name: json['name'] as String,
      icon: json['icon'] as String,
      parentId: json['parentId'] as String?,
      description: json['description'] as String?,
      productCount: (json['productCount'] as num).toInt(),
      isActive: json['isActive'] as bool,
      sortOrder: (json['sortOrder'] as num?)?.toInt(),
      children: (json['children'] as List<dynamic>?)
          ?.map((e) => ProductCategory.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$ProductCategoryToJson(ProductCategory instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'icon': instance.icon,
      'parentId': instance.parentId,
      'description': instance.description,
      'productCount': instance.productCount,
      'isActive': instance.isActive,
      'sortOrder': instance.sortOrder,
      'children': instance.children,
    };

ProductSearchRequest _$ProductSearchRequestFromJson(
        Map<String, dynamic> json) =>
    ProductSearchRequest(
      keyword: json['keyword'] as String?,
      categoryId: json['categoryId'] as String?,
      brandId: json['brandId'] as String?,
      minPrice: (json['minPrice'] as num?)?.toDouble(),
      maxPrice: (json['maxPrice'] as num?)?.toDouble(),
      condition: json['condition'] as String?,
      location: json['location'] as String?,
      sortBy: json['sortBy'] as String?,
      sortOrder: json['sortOrder'] as String?,
      page: (json['page'] as num?)?.toInt() ?? 1,
      pageSize: (json['pageSize'] as num?)?.toInt() ?? 20,
    );

Map<String, dynamic> _$ProductSearchRequestToJson(
        ProductSearchRequest instance) =>
    <String, dynamic>{
      'keyword': instance.keyword,
      'categoryId': instance.categoryId,
      'brandId': instance.brandId,
      'minPrice': instance.minPrice,
      'maxPrice': instance.maxPrice,
      'condition': instance.condition,
      'location': instance.location,
      'sortBy': instance.sortBy,
      'sortOrder': instance.sortOrder,
      'page': instance.page,
      'pageSize': instance.pageSize,
    };
