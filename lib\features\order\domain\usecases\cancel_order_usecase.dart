import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/error/failures.dart';
import 'package:soko/core/usecases/usecase.dart';
import 'package:soko/features/order/data/repositories/order_repository_impl.dart';
import 'package:soko/features/order/domain/repositories/order_repository.dart';

/// 取消订单用例
class CancelOrderUseCase implements UseCase<bool, String> {

  CancelOrderUseCase(this.repository);
  final OrderRepository repository;

  @override
  Future<Either<Failure, bool>> call(String orderId) async {
    return repository.cancelOrder(orderId);
  }
}

/// CancelOrderUseCase 提供者
final cancelOrderUseCaseProvider = Provider<CancelOrderUseCase>((ref) {
  final repository = ref.read(orderRepositoryProvider);
  return CancelOrderUseCase(repository);
});
