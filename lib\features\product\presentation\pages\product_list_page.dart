import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import 'package:soko/core/theme/app_colors.dart';
import 'package:soko/shared/presentation/widgets/custom_app_bar.dart';
import 'package:soko/shared/presentation/widgets/loading_widget.dart';
import 'package:soko/shared/presentation/widgets/error_widget.dart';
import 'package:soko/shared/presentation/widgets/empty_widget.dart';
import 'package:soko/features/product/presentation/providers/product_list_provider.dart';
import 'package:soko/features/product/presentation/providers/product_favorite_provider.dart';
import 'package:soko/features/product/presentation/widgets/product_card.dart';
import 'package:soko/features/product/presentation/widgets/product_filter_bar.dart';

/// 商品列表页面
class ProductListPage extends ConsumerStatefulWidget {

  const ProductListPage({super.key, this.categoryId, this.keyword});
  final String? categoryId;
  final String? keyword;

  @override
  ConsumerState<ProductListPage> createState() => _ProductListPageState();
}

class _ProductListPageState extends ConsumerState<ProductListPage> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    // 初始化查询条件
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final notifier = ref.read(productListProvider.notifier);
      if (widget.keyword != null) {
        notifier.setKeyword(widget.keyword);
      } else if (widget.categoryId != null) {
        notifier.setCategory(widget.categoryId);
      } else {
        notifier.loadFirstPage();
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      ref.read(productListProvider.notifier).loadMore();
    }
  }

  @override
  Widget build(BuildContext context) {
    var title = '商品列表';
    if (widget.keyword != null) {
      title = '搜索: ${widget.keyword}';
    } else if (widget.categoryId != null) {
      title = '分类商品';
    }

    final state = ref.watch(productListProvider);
    final queryState = ref.watch(productListQueryProvider);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CustomAppBar(title: title),
      body: Column(
        children: [
          // 筛选栏
          ProductFilterBar(
            currentSortBy: queryState.sortBy,
            currentSortOrder: queryState.sortOrder,
            hasActiveFilters: _hasActiveFilters(queryState),
            onFilterTap: _showFilterDialog,
            onSortChanged: (sortBy, sortOrder) {
              ref
                  .read(productListProvider.notifier)
                  .setSortBy(sortBy, sortOrder);
            },
          ),
          // 商品列表
          Expanded(child: _buildProductList(state)),
        ],
      ),
    );
  }

  /// 构建商品列表
  Widget _buildProductList(state) {
    if (state.isLoading && state.items.isEmpty) {
      return const LoadingWidget();
    }

    if (state.error != null && state.items.isEmpty) {
      return CustomErrorWidget(
        message: state.error!,
        onRetry: () => ref.read(productListProvider.notifier).refresh(),
      );
    }

    if (state.items.isEmpty) {
      return const EmptyWidget(
        message: '暂无商品',
        description: '试试调整筛选条件或搜索其他关键词',
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(productListProvider.notifier).refresh();
      },
      child: ListView.builder(
        controller: _scrollController,
        padding: EdgeInsets.symmetric(vertical: 8.h),
        itemCount: state.items.length + (state.hasMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= state.items.length) {
            return _buildLoadMoreIndicator(state);
          }

          final product = state.items[index];
          return ProductCard(
            product: product,
            onTap: () => _navigateToProductDetail(product.id),
            onFavorite: () => _toggleFavorite(product.id),
          );
        },
      ),
    );
  }

  /// 构建加载更多指示器
  Widget _buildLoadMoreIndicator(state) {
    if (state.isLoading) {
      return Container(
        padding: EdgeInsets.all(16.w),
        alignment: Alignment.center,
        child: const LoadingWidget(),
      );
    }

    if (state.error != null) {
      return Container(
        padding: EdgeInsets.all(16.w),
        alignment: Alignment.center,
        child: Column(
          children: [
            const Text('加载失败', style: TextStyle(color: AppColors.textSecondary)),
            SizedBox(height: 8.h),
            TextButton(
              onPressed: () =>
                  ref.read(productListProvider.notifier).loadMore(),
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    return const SizedBox.shrink();
  }

  /// 检查是否有活跃的筛选条件
  bool _hasActiveFilters(ProductListQueryState queryState) {
    return queryState.keyword != null ||
        queryState.category != null ||
        queryState.acg != null ||
        queryState.brand != null ||
        queryState.minPrice != null ||
        queryState.maxPrice != null ||
        queryState.condition != null;
  }

  /// 显示筛选弹窗
  void _showFilterDialog() {
    final queryState = ref.read(productListQueryProvider);

    showDialog(
      context: context,
      builder: (context) => ProductFilterDialog(
        selectedCategory: queryState.category,
        selectedBrand: queryState.brand,
        selectedCondition: queryState.condition,
        minPrice: queryState.minPrice,
        maxPrice: queryState.maxPrice,
        onApply: ({category, brand, condition, minPrice, maxPrice}) {
          final notifier = ref.read(productListProvider.notifier);
          notifier.updateQuery(
            queryState.copyWith(
              category: category,
              brand: brand,
              condition: condition,
              minPrice: minPrice,
              maxPrice: maxPrice,
            ),
          );
        },
      ),
    );
  }

  /// 导航到商品详情页
  void _navigateToProductDetail(String productId) {
    context.push('/product/$productId');
  }

  /// 切换收藏状态
  void _toggleFavorite(String productId) {
    ref.read(favoriteProvider.notifier).toggleFavorite(productId);
  }
}
