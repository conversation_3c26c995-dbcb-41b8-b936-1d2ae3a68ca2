import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/services.dart';

import 'package:soko/features/recycle/domain/entities/recycle_order.dart';

/// 订单信息部分组件
class OrderInfoSection extends StatelessWidget {
  const OrderInfoSection({
    super.key,
    required this.order,
  });

  final RecycleOrder order;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.info_outline,
                size: 20.w,
                color: Theme.of(context).primaryColor,
              ),
              SizedBox(width: 8.w),
              Text(
                '订单信息',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          
          // 订单号
          _buildInfoRow(
            context,
            '订单号',
            order.id,
            copyable: true,
          ),
          SizedBox(height: 12.h),
          
          // 商品信息
          _buildInfoRow(context, '品牌', order.brandName),
          SizedBox(height: 8.h),
          _buildInfoRow(context, '型号', order.model),
          SizedBox(height: 8.h),
          _buildInfoRow(context, '分类', order.categoryName),
          SizedBox(height: 8.h),
          
          // 设备状况
          _buildInfoRow(context, '设备状况', order.conditionDescription),
          
          // 商品描述（如果有）
          if (order.productDesc != null && order.productDesc!.isNotEmpty) ...[
            SizedBox(height: 8.h),
            _buildInfoRow(context, '商品描述', order.productDesc!),
          ],
          
          // 收货地址（如果有）
          if (order.buyerAddress != null && order.buyerAddress!.isNotEmpty) ...[
            SizedBox(height: 12.h),
            _buildInfoRow(context, '收货地址', order.buyerAddress!),
          ],
          
          // 物流信息（如果有）
          if (order.shippingInfo != null && order.shippingInfo!.isNotEmpty) ...[
            SizedBox(height: 12.h),
            _buildShippingInfo(context),
          ],
        ],
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(
    BuildContext context,
    String label,
    String value, {
    bool copyable = false,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80.w,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
            ),
          ),
        ),
        Expanded(
          child: Row(
            children: [
              Expanded(
                child: Text(
                  value,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey[800],
                  ),
                ),
              ),
              if (copyable) ...[
                SizedBox(width: 8.w),
                GestureDetector(
                  onTap: () => _copyToClipboard(context, value),
                  child: Icon(
                    Icons.copy,
                    size: 16.w,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  /// 构建物流信息
  Widget _buildShippingInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '物流信息',
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.grey[600],
          ),
        ),
        SizedBox(height: 8.h),
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: Text(
            order.shippingInfo!,
            style: TextStyle(
              fontSize: 13.sp,
              color: Colors.grey[700],
              height: 1.4,
            ),
          ),
        ),
      ],
    );
  }

  /// 复制到剪贴板
  void _copyToClipboard(BuildContext context, String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已复制到剪贴板'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
      ),
    );
  }
}
