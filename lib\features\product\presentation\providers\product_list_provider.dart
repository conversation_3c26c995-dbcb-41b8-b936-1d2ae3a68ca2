import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/models/query_params.dart';
import 'package:soko/core/network/api_response.dart';
import 'package:soko/core/state/base_notifier.dart';
import 'package:soko/core/state/base_state.dart';
import 'package:soko/features/product/data/datasources/product_api_service.dart';
import 'package:soko/features/product/domain/entities/product.dart';

/// 商品列表查询参数状态
class ProductListQueryState {

  const ProductListQueryState({
    this.keyword,
    this.category,
    this.acg,
    this.brand,
    this.minPrice,
    this.maxPrice,
    this.condition,
    this.sortBy = 'createTime',
    this.sortOrder = 'desc',
  });
  final String? keyword;
  final String? category;
  final String? acg;
  final String? brand;
  final double? minPrice;
  final double? maxPrice;
  final String? condition;
  final String sortBy;
  final String sortOrder;

  ProductListQueryState copyWith({
    String? keyword,
    String? category,
    String? acg,
    String? brand,
    double? minPrice,
    double? maxPrice,
    String? condition,
    String? sortBy,
    String? sortOrder,
  }) {
    return ProductListQueryState(
      keyword: keyword ?? this.keyword,
      category: category ?? this.category,
      acg: acg ?? this.acg,
      brand: brand ?? this.brand,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      condition: condition ?? this.condition,
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
    );
  }

  /// 转换为查询参数
  ProductQueryParams toQueryParams(int page, int size) {
    return ProductQueryParams(
      page: page,
      size: size,
      keyword: keyword,
      category: category,
      acg: acg,
      brand: brand,
      minPrice: minPrice,
      maxPrice: maxPrice,
      condition: condition,
      sortBy: sortBy,
      sortOrder: sortOrder,
    );
  }
}

/// 商品列表状态管理器
class ProductListNotifier extends PageNotifier<Product> {
  final ProductApiService _productApiService = ProductApiService();
  ProductListQueryState _queryState = const ProductListQueryState();

  ProductListQueryState get queryState => _queryState;

  @override
  Future<PageResponse<Product>> loadPage(int page) async {
    final params = _queryState.toQueryParams(page, 20);
    final response = await _productApiService.getProductList(params: params);

    if (response.isSuccess && response.data != null) {
      return response.data!;
    }

    throw Exception(response.message);
  }

  /// 加载第一页
  Future<void> loadFirstPage() async {
    await refresh();
  }

  /// 更新查询条件
  void updateQuery(ProductListQueryState newQuery) {
    _queryState = newQuery;
    refresh();
  }

  /// 设置关键词搜索
  void setKeyword(String? keyword) {
    updateQuery(_queryState.copyWith(keyword: keyword));
  }

  /// 设置分类筛选
  void setCategory(String? category) {
    updateQuery(_queryState.copyWith(category: category));
  }

  /// 设置ACG筛选
  void setAcg(String? acg) {
    updateQuery(_queryState.copyWith(acg: acg));
  }

  /// 设置品牌筛选
  void setBrand(String? brand) {
    updateQuery(_queryState.copyWith(brand: brand));
  }

  /// 设置价格区间
  void setPriceRange(double? minPrice, double? maxPrice) {
    updateQuery(_queryState.copyWith(
      minPrice: minPrice,
      maxPrice: maxPrice,
    ),);
  }

  /// 设置商品状态
  void setCondition(String? condition) {
    updateQuery(_queryState.copyWith(condition: condition));
  }

  /// 设置排序方式
  void setSortBy(String sortBy, String sortOrder) {
    updateQuery(_queryState.copyWith(
      sortBy: sortBy,
      sortOrder: sortOrder,
    ),);
  }

  /// 清除所有筛选条件
  void clearFilters() {
    updateQuery(const ProductListQueryState());
  }
}

/// 商品列表状态提供者
final productListProvider =
    StateNotifierProvider<ProductListNotifier, PageState<Product>>((ref) {
  return ProductListNotifier();
});

/// 商品列表查询状态提供者
final productListQueryProvider = Provider<ProductListQueryState>((ref) {
  return ref.watch(productListProvider.notifier).queryState;
});
