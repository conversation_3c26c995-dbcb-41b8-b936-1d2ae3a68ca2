import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/features/recycle/domain/entities/recycle_models.dart';

/// 联系快递员卡片组件
class LogisticsContactCard extends StatelessWidget {
  const LogisticsContactCard({
    super.key,
    required this.logistics,
    this.onCallCourier,
  });

  final LogisticsInfo logistics;
  final VoidCallback? onCallCourier;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.contact_phone,
                size: 20.w,
                color: Theme.of(context).primaryColor,
              ),
              SizedBox(width: 8.w),
              Text(
                '联系快递员',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          
          // 快递员信息
          Row(
            children: [
              // 头像
              Container(
                width: 48.w,
                height: 48.w,
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.person,
                  size: 24.w,
                  color: Theme.of(context).primaryColor,
                ),
              ),
              SizedBox(width: 12.w),
              
              // 姓名和电话
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      logistics.courierName ?? '快递员',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (logistics.courierPhone != null) ...[
                      SizedBox(height: 4.h),
                      Text(
                        logistics.courierPhone!,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              
              // 拨打电话按钮
              if (logistics.courierPhone != null && onCallCourier != null)
                ElevatedButton.icon(
                  onPressed: onCallCourier,
                  icon: Icon(
                    Icons.phone,
                    size: 16.w,
                  ),
                  label: const Text('拨打'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20.r),
                    ),
                  ),
                ),
            ],
          ),
          
          // 温馨提示
          SizedBox(height: 16.h),
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 16.w,
                  color: Colors.blue[600],
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: Text(
                    '如有疑问可直接联系快递员，工作时间：8:00-20:00',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.blue[700],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
