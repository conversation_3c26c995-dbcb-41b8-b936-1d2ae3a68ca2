import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/shared/presentation/widgets/custom_app_bar.dart';

/// 创建回收订单页面
class CreateRecycleOrderPage extends ConsumerWidget {
  const CreateRecycleOrderPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return const Scaffold(
      appBar: CustomAppBar(title: '创建回收订单'),
      body: Center(
        child: Text('创建回收订单页面 - 待实现'),
      ),
    );
  }
}
