import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/state/base_state.dart';
import 'package:soko/features/recycle/domain/entities/recycle_models.dart';
import 'package:soko/features/recycle/domain/repositories/recycle_repository.dart';
import 'package:soko/features/recycle/data/repositories/recycle_repository_impl.dart';

/// 物流跟踪状态管理
class LogisticsTrackingNotifier extends StateNotifier<BaseState<LogisticsInfo>> {
  LogisticsTrackingNotifier(this._recycleRepository, this._orderId) 
      : super(const BaseState<LogisticsInfo>.initial());

  final RecycleRepository _recycleRepository;
  final String _orderId;

  /// 加载物流信息
  Future<void> loadLogisticsInfo() async {
    state = const BaseState<LogisticsInfo>.loading();

    try {
      final logistics = await _recycleRepository.getLogisticsInfo(_orderId);
      state = BaseState<LogisticsInfo>.success(logistics);
    } catch (error) {
      state = BaseState<LogisticsInfo>.error(error.toString());
    }
  }

  /// 刷新物流信息
  Future<void> refreshLogisticsInfo() async {
    await loadLogisticsInfo();
  }

  /// 更新物流状态
  Future<void> updateLogisticsStatus() async {
    try {
      await _recycleRepository.updateLogisticsStatus(_orderId);
      
      // 重新加载物流信息以获取最新状态
      await loadLogisticsInfo();
    } catch (error) {
      // 这里可以显示错误提示
      rethrow;
    }
  }

  /// 标记物流异常
  Future<void> reportLogisticsException(String reason) async {
    try {
      await _recycleRepository.reportLogisticsException(_orderId, reason);
      
      // 重新加载物流信息以获取最新状态
      await loadLogisticsInfo();
    } catch (error) {
      rethrow;
    }
  }
}

/// 物流跟踪Provider
final logisticsTrackingProvider = StateNotifierProvider.family<
    LogisticsTrackingNotifier, 
    BaseState<LogisticsInfo>, 
    String>((ref, orderId) {
  final recycleRepository = ref.watch(recycleRepositoryProvider);
  return LogisticsTrackingNotifier(recycleRepository, orderId);
});
