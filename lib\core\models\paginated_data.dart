import 'package:json_annotation/json_annotation.dart';

part 'paginated_data.g.dart';

/// 分页数据模型
@JsonSerializable(genericArgumentFactories: true)
class PaginatedData<T> {

  const PaginatedData({
    required this.data,
    required this.currentPage,
    required this.pageSize,
    required this.totalPages,
    required this.totalCount,
    required this.hasMore,
  });

  factory PaginatedData.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$PaginatedDataFromJson(json, fromJsonT);

  /// 创建空的分页数据
  factory PaginatedData.empty() => const PaginatedData(
        data: [],
        currentPage: 1,
        pageSize: 20,
        totalPages: 0,
        totalCount: 0,
        hasMore: false,
      );
  /// 数据列表
  final List<T> data;
  
  /// 当前页码
  final int currentPage;
  
  /// 每页大小
  final int pageSize;
  
  /// 总页数
  final int totalPages;
  
  /// 总数量
  final int totalCount;
  
  /// 是否有更多数据
  final bool hasMore;

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$PaginatedDataToJson(this, toJsonT);

  /// 复制并更新数据
  PaginatedData<T> copyWith({
    List<T>? data,
    int? currentPage,
    int? pageSize,
    int? totalPages,
    int? totalCount,
    bool? hasMore,
  }) {
    return PaginatedData<T>(
      data: data ?? this.data,
      currentPage: currentPage ?? this.currentPage,
      pageSize: pageSize ?? this.pageSize,
      totalPages: totalPages ?? this.totalPages,
      totalCount: totalCount ?? this.totalCount,
      hasMore: hasMore ?? this.hasMore,
    );
  }

  @override
  String toString() {
    return 'PaginatedData(data: ${data.length} items, currentPage: $currentPage, totalPages: $totalPages, totalCount: $totalCount, hasMore: $hasMore)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PaginatedData<T> &&
        other.data == data &&
        other.currentPage == currentPage &&
        other.pageSize == pageSize &&
        other.totalPages == totalPages &&
        other.totalCount == totalCount &&
        other.hasMore == hasMore;
  }

  @override
  int get hashCode {
    return data.hashCode ^
        currentPage.hashCode ^
        pageSize.hashCode ^
        totalPages.hashCode ^
        totalCount.hashCode ^
        hasMore.hashCode;
  }
}
