import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';

import 'package:soko/core/theme/app_colors.dart';
import 'package:soko/core/theme/app_text_styles.dart';
import 'package:soko/shared/presentation/widgets/loading_widget.dart';
import 'package:soko/features/product/domain/entities/product.dart';
import 'package:soko/features/product/presentation/providers/product_favorite_provider.dart';

/// 商品卡片组件
class ProductCard extends ConsumerWidget {

  const ProductCard({
    super.key,
    required this.product,
    this.onTap,
    this.onFavorite,
    this.isFavorite,
    this.showFavoriteButton = true,
  });
  final Product product;
  final VoidCallback? onTap;
  final VoidCallback? onFavorite;
  final bool? isFavorite;
  final bool showFavoriteButton;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final actualIsFavorite =
        isFavorite ?? ref.watch(isProductFavoriteProvider(product.id));
    return Card(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Container(
          padding: EdgeInsets.all(12.w),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 商品图片
              _buildProductImage(),
              SizedBox(width: 12.w),
              // 商品信息
              Expanded(
                child: _buildProductInfo(),
              ),
              // 收藏按钮
              if (showFavoriteButton) _buildFavoriteButton(actualIsFavorite),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建商品图片
  Widget _buildProductImage() {
    return Container(
      width: 100.w,
      height: 100.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.r),
        color: AppColors.background,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8.r),
        child: product.mainImage != null
            ? CachedNetworkImage(
                imageUrl: product.mainImage!,
                fit: BoxFit.cover,
                placeholder: (context, url) => const LoadingWidget(),
                errorWidget: (context, url, error) => ColoredBox(
                  color: AppColors.background,
                  child: Icon(
                    Icons.image_not_supported,
                    color: AppColors.textTertiary,
                    size: 32.sp,
                  ),
                ),
              )
            : ColoredBox(
                color: AppColors.background,
                child: Icon(
                  Icons.image_not_supported,
                  color: AppColors.textTertiary,
                  size: 32.sp,
                ),
              ),
      ),
    );
  }

  /// 构建商品信息
  Widget _buildProductInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 商品名称
        Text(
          product.name,
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        SizedBox(height: 4.h),
        // 商品分类和品牌
        if (product.brand != null || product.category.isNotEmpty)
          Text(
            [
              if (product.brand != null) product.brand!,
              if (product.category.isNotEmpty) product.category,
            ].join(' · '),
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        SizedBox(height: 8.h),
        // 价格信息
        _buildPriceInfo(),
        SizedBox(height: 8.h),
        // 标签信息
        _buildTags(),
      ],
    );
  }

  /// 构建价格信息
  Widget _buildPriceInfo() {
    return Row(
      children: [
        // 当前价格
        Text(
          '¥${product.minPrice.toStringAsFixed(2)}',
          style: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
        // 价格区间
        if (product.maxPrice != null && product.maxPrice! > product.minPrice)
          Text(
            ' - ¥${product.maxPrice!.toStringAsFixed(2)}',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        SizedBox(width: 8.w),
        // 原价
        if (product.originalPrice != null &&
            product.originalPrice! > product.minPrice)
          Text(
            '¥${product.originalPrice!.toStringAsFixed(2)}',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textTertiary,
              decoration: TextDecoration.lineThrough,
            ),
          ),
      ],
    );
  }

  /// 构建标签信息
  Widget _buildTags() {
    final tags = <Widget>[];

    // 新品标签
    if (product.newable == true) {
      tags.add(_buildTag('新品', AppColors.success));
    }

    // ACG标签
    if (product.acg != null && product.acg!.isNotEmpty) {
      tags.add(_buildTag(product.acg!, AppColors.info));
    }

    // 销售类型标签
    if (product.salesType != null) {
      var salesTypeText = '';
      var salesTypeColor = AppColors.textTertiary;

      switch (product.salesType) {
        case 'STOCK':
          salesTypeText = '现货';
          salesTypeColor = AppColors.success;
        case 'PRE_ORDER':
          salesTypeText = '预订';
          salesTypeColor = AppColors.warning;
        case 'CUSTOM':
          salesTypeText = '定制';
          salesTypeColor = AppColors.info;
      }

      if (salesTypeText.isNotEmpty) {
        tags.add(_buildTag(salesTypeText, salesTypeColor));
      }
    }

    if (tags.isEmpty) return const SizedBox.shrink();

    return Wrap(
      spacing: 4.w,
      runSpacing: 4.h,
      children: tags,
    );
  }

  /// 构建标签
  Widget _buildTag(String text, Color color) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4.r),
        border: Border.all(color: color.withOpacity(0.3), width: 0.5),
      ),
      child: Text(
        text,
        style: AppTextStyles.caption.copyWith(
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 构建收藏按钮
  Widget _buildFavoriteButton(bool isFavorite) {
    return IconButton(
      onPressed: onFavorite,
      icon: Icon(
        isFavorite ? Icons.favorite : Icons.favorite_border,
        color: isFavorite ? AppColors.error : AppColors.textTertiary,
        size: 20.sp,
      ),
      constraints: BoxConstraints(
        minWidth: 32.w,
        minHeight: 32.w,
      ),
      padding: EdgeInsets.zero,
    );
  }
}
