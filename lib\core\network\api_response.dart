import 'package:json_annotation/json_annotation.dart';

part 'api_response.g.dart';

/// API响应基础类
@JsonSerializable(genericArgumentFactories: true)
class ApiResponse<T> {

  const ApiResponse({
    required this.code,
    required this.message,
    this.data,
    this.timestamp,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$ApiResponseFromJson(json, fromJsonT);
  @JsonKey(name: 'code')
  final int code;

  @JsonKey(name: 'message')
  final String message;

  @Json<PERSON>ey(name: 'data')
  final T? data;

  @JsonKey(name: 'timestamp')
  final int? timestamp;

  /// 是否成功
  bool get isSuccess => code == 200 || code == 0;

  /// 是否失败
  bool get isFailure => !isSuccess;

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$ApiResponseToJson(this, toJsonT);

  @override
  String toString() {
    return 'ApiResponse(code: $code, message: $message, data: $data)';
  }
}

/// 分页响应类
@JsonSerializable(genericArgumentFactories: true)
class PageResponse<T> {

  const PageResponse({
    required this.list,
    required this.total,
    required this.page,
    required this.pageSize,
    required this.totalPages,
    required this.hasNext,
    required this.hasPrev,
  });

  factory PageResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$PageResponseFromJson(json, fromJsonT);
  @JsonKey(name: 'list')
  final List<T> list;

  @JsonKey(name: 'total')
  final int total;

  @JsonKey(name: 'page')
  final int page;

  @JsonKey(name: 'pageSize')
  final int pageSize;

  @JsonKey(name: 'totalPages')
  final int totalPages;

  @JsonKey(name: 'hasNext')
  final bool hasNext;

  @JsonKey(name: 'hasPrev')
  final bool hasPrev;

  /// 是否为空
  bool get isEmpty => list.isEmpty;

  /// 是否不为空
  bool get isNotEmpty => list.isNotEmpty;

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$PageResponseToJson(this, toJsonT);

  @override
  String toString() {
    return 'PageResponse(total: $total, page: $page, pageSize: $pageSize, listLength: ${list.length})';
  }
}

/// 简单响应类（只有成功失败状态）
@JsonSerializable()
class SimpleResponse {

  const SimpleResponse({
    required this.code,
    required this.message,
    this.timestamp,
  });

  factory SimpleResponse.fromJson(Map<String, dynamic> json) =>
      _$SimpleResponseFromJson(json);
  @JsonKey(name: 'code')
  final int code;

  @JsonKey(name: 'message')
  final String message;

  @JsonKey(name: 'timestamp')
  final int? timestamp;

  /// 是否成功
  bool get isSuccess => code == 200 || code == 0;

  /// 是否失败
  bool get isFailure => !isSuccess;

  Map<String, dynamic> toJson() => _$SimpleResponseToJson(this);

  @override
  String toString() {
    return 'SimpleResponse(code: $code, message: $message)';
  }
}
