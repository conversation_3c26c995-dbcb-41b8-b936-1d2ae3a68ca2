import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/core/theme/app_colors.dart';
import 'package:soko/core/theme/app_text_styles.dart';
import 'package:soko/features/cart/presentation/providers/cart_provider.dart';
import 'package:soko/features/cart/domain/services/price_calculation_service.dart';

/// 购物车价格详情组件
class CartPriceDetails extends ConsumerWidget {

  const CartPriceDetails({
    super.key,
    this.showTitle = true,
    this.margin,
  });
  final bool showTitle;
  final EdgeInsets? margin;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final cartState = ref.watch(cartProvider);
    final selectedItems = cartState.items.where((item) => item.selected && item.available).toList();

    if (selectedItems.isEmpty) {
      return const SizedBox.shrink();
    }

    final calculation = cartState.priceCalculation ?? 
        PriceCalculationService.calculateCartPrice(
          items: cartState.items,
          coupon: cartState.selectedCoupon,
          memberDiscount: cartState.memberDiscount,
        );

    return Container(
      margin: margin ?? EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (showTitle) ...[
            Row(
              children: [
                Icon(
                  Icons.receipt_outlined,
                  color: AppColors.primary,
                  size: 20.sp,
                ),
                SizedBox(width: 8.w),
                Text(
                  '价格详情',
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
          ],
          
          // 商品金额
          _buildPriceRow(
            '商品金额',
            '¥${calculation.productAmount.toStringAsFixed(2)}',
          ),
          
          // 会员折扣
          if (calculation.memberDiscount > 0) ...[
            SizedBox(height: 8.h),
            _buildPriceRow(
              '会员折扣',
              '-¥${calculation.memberDiscount.toStringAsFixed(2)}',
              valueColor: AppColors.success,
            ),
          ],
          
          // 优惠券折扣
          if (calculation.couponDiscount > 0) ...[
            SizedBox(height: 8.h),
            _buildPriceRow(
              '优惠券',
              '-¥${calculation.couponDiscount.toStringAsFixed(2)}',
              valueColor: AppColors.success,
              subtitle: calculation.appliedCoupon?.name,
            ),
          ],
          
          // 运费
          SizedBox(height: 8.h),
          _buildPriceRow(
            '运费',
            calculation.shippingFee > 0 
                ? '¥${calculation.shippingFee.toStringAsFixed(2)}'
                : '免运费',
            valueColor: calculation.shippingFee > 0 ? null : AppColors.success,
          ),
          
          // 分割线
          Container(
            margin: EdgeInsets.symmetric(vertical: 12.h),
            height: 1,
            color: AppColors.borderLight,
          ),
          
          // 实付金额
          _buildPriceRow(
            '实付金额',
            '¥${calculation.finalAmount.toStringAsFixed(2)}',
            titleStyle: AppTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.w600,
            ),
            valueStyle: AppTextStyles.headlineSmall.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          // 节省金额提示
          if (calculation.hasDiscount) ...[
            SizedBox(height: 8.h),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
              decoration: BoxDecoration(
                color: AppColors.success.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4.r),
              ),
              child: Text(
                '已为您节省 ¥${calculation.totalDiscount.toStringAsFixed(2)}',
                style: AppTextStyles.caption.copyWith(
                  color: AppColors.success,
                ),
              ),
            ),
          ],
          
          // 距离包邮提示
          if (!calculation.isFreeShipping && calculation.shippingFee > 0) ...[
            SizedBox(height: 8.h),
            _buildFreeShippingTip(calculation),
          ],
        ],
      ),
    );
  }

  /// 构建价格行
  Widget _buildPriceRow(
    String title,
    String value, {
    TextStyle? titleStyle,
    TextStyle? valueStyle,
    Color? valueColor,
    String? subtitle,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: titleStyle ?? AppTextStyles.bodyMedium,
              ),
              if (subtitle != null) ...[
                SizedBox(height: 2.h),
                Text(
                  subtitle,
                  style: AppTextStyles.caption.copyWith(
                    color: AppColors.textTertiary,
                  ),
                ),
              ],
            ],
          ),
        ),
        Text(
          value,
          style: valueStyle ??
              AppTextStyles.bodyMedium.copyWith(
                color: valueColor ?? AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
        ),
      ],
    );
  }

  /// 构建包邮提示
  Widget _buildFreeShippingTip(calculation) {
    final amountToFreeShipping = 99.0 - calculation.productAmount;
    
    if (amountToFreeShipping <= 0) return const SizedBox.shrink();
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: AppColors.warning.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Row(
        children: [
          Icon(
            Icons.local_shipping_outlined,
            size: 14.sp,
            color: AppColors.warning,
          ),
          SizedBox(width: 4.w),
          Text(
            '再买 ¥${amountToFreeShipping.toStringAsFixed(2)} 即可包邮',
            style: AppTextStyles.caption.copyWith(
              color: AppColors.warning,
            ),
          ),
        ],
      ),
    );
  }
}

/// 简化版价格摘要
class CartPriceSummary extends ConsumerWidget {
  const CartPriceSummary({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final cartState = ref.watch(cartProvider);
    final selectedItems = cartState.items.where((item) => item.selected && item.available).toList();

    if (selectedItems.isEmpty) {
      return const SizedBox.shrink();
    }

    final calculation = cartState.priceCalculation ?? 
        PriceCalculationService.calculateCartPrice(
          items: cartState.items,
          coupon: cartState.selectedCoupon,
          memberDiscount: cartState.memberDiscount,
        );

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Row(
        children: [
          // 商品数量
          Text(
            '共${selectedItems.fold<int>(0, (sum, item) => sum + item.quantity)}件',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const Spacer(),
          // 优惠信息
          if (calculation.hasDiscount) ...[
            Text(
              '已优惠¥${calculation.totalDiscount.toStringAsFixed(2)}',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.success,
              ),
            ),
            SizedBox(width: 8.w),
          ],
          // 合计金额
          Text(
            '合计：',
            style: AppTextStyles.bodyMedium,
          ),
          Text(
            '¥${calculation.finalAmount.toStringAsFixed(2)}',
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
