import 'dart:convert';
import 'dart:math';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:crypto/crypto.dart';

import 'package:soko/features/payment/domain/entities/payment_models.dart';

/// 支付宝支付服务
class AlipayService {
  const AlipayService(this._config);

  final PaymentConfig _config;

  /// 创建支付订单
  Future<PaymentResponse> createPayment(PaymentRequest request) async {
    try {
      // 生成支付参数
      final params = _buildPaymentParams(request);
      
      // 生成签名
      final sign = _generateSign(params);
      params['sign'] = sign;
      
      // 构建订单字符串
      final orderString = _buildOrderString(params);
      
      // 模拟支付宝沙盒环境响应
      await Future<void>.delayed(const Duration(milliseconds: 500));
      
      return PaymentResponse(
        success: true,
        paymentId: _generatePaymentId(),
        orderString: orderString,
        tradeNo: _generateTradeNo(),
      );
    } catch (error) {
      return PaymentResponse(
        success: false,
        paymentId: '',
        errorCode: 'SYSTEM_ERROR',
        errorMessage: error.toString(),
      );
    }
  }

  /// 查询支付结果
  Future<PaymentResult> queryPaymentResult(String paymentId) async {
    try {
      // 模拟查询延迟
      await Future<void>.delayed(const Duration(milliseconds: 300));
      
      // 模拟支付结果（沙盒环境下随机成功/失败）
      final isSuccess = Random().nextBool();
      
      if (isSuccess) {
        return PaymentResult(
          success: true,
          paymentId: paymentId,
          orderId: 'ORDER_${DateTime.now().millisecondsSinceEpoch}',
          tradeNo: _generateTradeNo(),
          totalAmount: '99.00',
          receiptAmount: '99.00',
          buyerPayAmount: '99.00',
          gmtPayment: DateTime.now().toIso8601String(),
          fundBillList: [
            const FundBill(
              fundChannel: 'ALIPAYACCOUNT',
              amount: '99.00',
            ),
          ],
        );
      } else {
        return PaymentResult(
          success: false,
          paymentId: paymentId,
          orderId: 'ORDER_${DateTime.now().millisecondsSinceEpoch}',
          errorCode: 'TRADE_NOT_EXIST',
          errorMessage: '交易不存在',
        );
      }
    } catch (error) {
      return PaymentResult(
        success: false,
        paymentId: paymentId,
        orderId: '',
        errorCode: 'SYSTEM_ERROR',
        errorMessage: error.toString(),
      );
    }
  }

  /// 验证支付回调
  Future<bool> verifyPaymentNotify(Map<String, dynamic> notifyData) async {
    try {
      // 提取签名
      final sign = notifyData['sign'] as String?;
      if (sign == null) return false;

      // 移除签名和签名类型参数
      final params = Map<String, dynamic>.from(notifyData);
      params.remove('sign');
      params.remove('sign_type');

      // 验证签名
      final expectedSign = _generateSign(params);
      return sign == expectedSign;
    } catch (error) {
      return false;
    }
  }

  /// 申请退款
  Future<bool> refundPayment(String tradeNo, double amount, String reason) async {
    try {
      // 构建退款参数
      final params = {
        'app_id': _config.appId,
        'method': 'alipay.trade.refund',
        'charset': 'utf-8',
        'sign_type': _config.signType,
        'timestamp': DateTime.now().toIso8601String(),
        'version': '1.0',
        'biz_content': jsonEncode({
          'trade_no': tradeNo,
          'refund_amount': amount.toStringAsFixed(2),
          'refund_reason': reason,
          'out_request_no': 'REFUND_${DateTime.now().millisecondsSinceEpoch}',
        }),
      };

      // 生成签名
      final sign = _generateSign(params);
      params['sign'] = sign;

      // 模拟退款请求
      await Future<void>.delayed(const Duration(milliseconds: 800));
      
      // 沙盒环境下模拟成功
      return true;
    } catch (error) {
      return false;
    }
  }

  /// 测试连接
  Future<bool> testConnection() async {
    try {
      // 模拟连接测试
      await Future<void>.delayed(const Duration(milliseconds: 200));
      return true;
    } catch (error) {
      return false;
    }
  }

  /// 构建支付参数
  Map<String, dynamic> _buildPaymentParams(PaymentRequest request) {
    return {
      'app_id': _config.appId,
      'method': 'alipay.trade.app.pay',
      'charset': 'utf-8',
      'sign_type': _config.signType,
      'timestamp': DateTime.now().toIso8601String(),
      'version': '1.0',
      'notify_url': _config.notifyUrl ?? '',
      'biz_content': jsonEncode({
        'out_trade_no': request.orderId,
        'total_amount': request.amount.toStringAsFixed(2),
        'subject': request.subject,
        'body': request.body,
        'timeout_express': request.timeoutExpress ?? '30m',
        'product_code': 'QUICK_MSECURITY_PAY',
        'passback_params': request.passbackParams,
      }),
    };
  }

  /// 生成签名
  String _generateSign(Map<String, dynamic> params) {
    // 排序参数
    final sortedKeys = params.keys.toList()..sort();
    
    // 构建待签名字符串
    final signString = sortedKeys
        .where((key) => params[key] != null && params[key].toString().isNotEmpty)
        .map((key) => '$key=${params[key]}')
        .join('&');

    // 使用RSA2签名（这里简化为MD5，实际应用中应使用RSA2）
    final bytes = utf8.encode(signString + _config.privateKey);
    final digest = md5.convert(bytes);
    return digest.toString();
  }

  /// 构建订单字符串
  String _buildOrderString(Map<String, dynamic> params) {
    return params.entries
        .map((entry) => '${entry.key}=${Uri.encodeComponent(entry.value.toString())}')
        .join('&');
  }

  /// 生成支付ID
  String _generatePaymentId() {
    return 'PAY_${DateTime.now().millisecondsSinceEpoch}_${Random().nextInt(9999).toString().padLeft(4, '0')}';
  }

  /// 生成交易号
  String _generateTradeNo() {
    return '2024${DateTime.now().millisecondsSinceEpoch}${Random().nextInt(999999).toString().padLeft(6, '0')}';
  }
}

/// 支付宝服务Provider
final alipayServiceProvider = Provider<AlipayService>((ref) {
  return const AlipayService(PaymentConfig.alipaySandbox);
});
