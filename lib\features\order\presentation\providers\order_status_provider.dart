import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/enums/app_enums.dart';
import 'package:soko/features/order/domain/entities/order.dart';
import 'package:soko/features/order/domain/services/order_status_service.dart';

/// 订单状态信息
class OrderStatusInfo {

  const OrderStatusInfo({
    required this.status,
    required this.displayText,
    required this.description,
    required this.color,
    required this.icon,
    required this.progress,
    required this.actionHint,
    required this.canCancel,
    required this.canPay,
    required this.canConfirmReceived,
    required this.canRequestRefund,
    required this.canDelete,
    required this.canViewTracking,
    required this.isFinal,
    required this.requiresUserAction,
    required this.nextPossibleStatuses,
  });

  factory OrderStatusInfo.fromStatus(OrderStatus status) {
    return OrderStatusInfo(
      status: status,
      displayText: OrderStatusService.getStatusDisplayText(status),
      description: OrderStatusService.getStatusDescription(status),
      color: OrderStatusService.getStatusColor(status),
      icon: OrderStatusService.getStatusIcon(status),
      progress: OrderStatusService.getStatusProgress(status),
      actionHint: OrderStatusService.getActionHint(status),
      canCancel: OrderStatusService.canCancelOrder(status),
      canPay: OrderStatusService.canPayOrder(status),
      canConfirmReceived: OrderStatusService.canConfirmReceived(status),
      canRequestRefund: OrderStatusService.canRequestRefund(status),
      canDelete: OrderStatusService.canDeleteOrder(status),
      canViewTracking: OrderStatusService.canViewTracking(status),
      isFinal: OrderStatusService.isFinalStatus(status),
      requiresUserAction: OrderStatusService.requiresUserAction(status),
      nextPossibleStatuses: OrderStatusService.getNextPossibleStatuses(status),
    );
  }
  final OrderStatus status;
  final String displayText;
  final String description;
  final String color;
  final String icon;
  final double progress;
  final String actionHint;
  final bool canCancel;
  final bool canPay;
  final bool canConfirmReceived;
  final bool canRequestRefund;
  final bool canDelete;
  final bool canViewTracking;
  final bool isFinal;
  final bool requiresUserAction;
  final List<OrderStatus> nextPossibleStatuses;
}

/// 订单状态管理器
class OrderStatusNotifier extends StateNotifier<Map<String, OrderStatusInfo>> {
  OrderStatusNotifier() : super({});

  /// 获取订单状态信息
  OrderStatusInfo getOrderStatusInfo(String orderId, OrderStatus status) {
    final key = '${orderId}_${status.name}';
    if (!state.containsKey(key)) {
      final statusInfo = OrderStatusInfo.fromStatus(status);
      state = {...state, key: statusInfo};
      return statusInfo;
    }
    return state[key]!;
  }

  /// 验证订单状态流转
  bool validateStatusTransition(String orderId, OrderStatus from, OrderStatus to) {
    return OrderStatusService.isValidStatusTransition(from, to);
  }

  /// 获取状态流转错误信息
  String getStatusTransitionError(OrderStatus from, OrderStatus to) {
    return OrderStatusService.getStatusTransitionError(from, to);
  }

  /// 更新订单状态信息
  void updateOrderStatus(String orderId, OrderStatus newStatus) {
    final key = '${orderId}_${newStatus.name}';
    final statusInfo = OrderStatusInfo.fromStatus(newStatus);
    state = {...state, key: statusInfo};
  }

  /// 清除订单状态信息
  void clearOrderStatus(String orderId) {
    final newState = Map<String, OrderStatusInfo>.from(state);
    newState.removeWhere((key, value) => key.startsWith('${orderId}_'));
    state = newState;
  }

  /// 清除所有状态信息
  void clearAllStatuses() {
    state = {};
  }

  /// 获取需要用户操作的订单状态列表
  List<OrderStatus> getUserActionRequiredStatuses() {
    return OrderStatus.values
        .where(OrderStatusService.requiresUserAction)
        .toList();
  }

  /// 获取最终状态列表
  List<OrderStatus> getFinalStatuses() {
    return OrderStatus.values
        .where(OrderStatusService.isFinalStatus)
        .toList();
  }

  /// 按优先级排序订单状态
  List<OrderStatus> sortStatusesByPriority(List<OrderStatus> statuses) {
    final sortedStatuses = List<OrderStatus>.from(statuses);
    sortedStatuses.sort((a, b) => 
        OrderStatusService.getStatusPriority(a).compareTo(
            OrderStatusService.getStatusPriority(b),),);
    return sortedStatuses;
  }
}

/// 订单状态管理器提供者
final orderStatusProvider = StateNotifierProvider<OrderStatusNotifier, Map<String, OrderStatusInfo>>((ref) {
  return OrderStatusNotifier();
});

/// 获取特定订单状态信息的提供者
final orderStatusInfoProvider = Provider.family<OrderStatusInfo, (String, OrderStatus)>((ref, params) {
  final (orderId, status) = params;
  final statusNotifier = ref.watch(orderStatusProvider.notifier);
  return statusNotifier.getOrderStatusInfo(orderId, status);
});

/// 获取订单状态信息的提供者（从Order对象）
final orderStatusFromOrderProvider = Provider.family<OrderStatusInfo, Order>((ref, order) {
  final statusNotifier = ref.watch(orderStatusProvider.notifier);
  return statusNotifier.getOrderStatusInfo(order.id, order.statusEnum);
});

/// 验证订单状态流转的提供者
final validateStatusTransitionProvider = Provider.family<bool, (String, OrderStatus, OrderStatus)>((ref, params) {
  final (orderId, from, to) = params;
  final statusNotifier = ref.watch(orderStatusProvider.notifier);
  return statusNotifier.validateStatusTransition(orderId, from, to);
});

/// 获取需要用户操作的订单状态提供者
final userActionRequiredStatusesProvider = Provider<List<OrderStatus>>((ref) {
  final statusNotifier = ref.watch(orderStatusProvider.notifier);
  return statusNotifier.getUserActionRequiredStatuses();
});

/// 获取最终状态列表提供者
final finalStatusesProvider = Provider<List<OrderStatus>>((ref) {
  final statusNotifier = ref.watch(orderStatusProvider.notifier);
  return statusNotifier.getFinalStatuses();
});
