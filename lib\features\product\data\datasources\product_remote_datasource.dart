import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/features/product/domain/entities/product_models.dart';
import 'package:soko/core/models/paginated_data.dart';

/// 商品远程数据源接口
abstract class ProductRemoteDataSource {
  Future<PaginatedData<Product>> searchProducts(ProductSearchRequest request);
  Future<Product> getProductDetail(String productId);
  Future<List<ProductCategory>> getCategories();
  Future<PaginatedData<Product>> getProductsByCategory(String categoryId, int page, int pageSize, String? sortBy, String? sortOrder);
  Future<PaginatedData<Product>> getProductsByBrand(String brandId, int page, int pageSize, String? sortBy, String? sortOrder);
  Future<List<Product>> getRecommendedProducts(String? categoryId, String? userId, int limit);
  Future<List<Product>> getPopularProducts(String? categoryId, int limit);
  Future<List<Product>> getLatestProducts(String? categoryId, int limit);
  Future<List<Product>> getSimilarProducts(String productId, int limit);
  Future<void> addToFavorites(String productId);
  Future<void> removeFromFavorites(String productId);
  Future<PaginatedData<Product>> getFavoriteProducts(int page, int pageSize);
  Future<bool> isFavorite(String productId);
  Future<void> incrementViewCount(String productId);
  Future<List<Product>> getViewHistory(int limit);
  Future<List<String>> getSearchSuggestions(String keyword);
  Future<List<String>> getHotSearchKeywords(int limit);
}

/// 商品远程数据源实现
class ProductRemoteDataSourceImpl implements ProductRemoteDataSource {
  @override
  Future<PaginatedData<Product>> searchProducts(ProductSearchRequest request) async {
    await Future<void>.delayed(const Duration(milliseconds: 800));
    
    final mockProducts = _generateMockProducts();
    
    // 模拟搜索过滤
    var filteredProducts = mockProducts.where((product) {
      if (request.keyword != null && request.keyword!.isNotEmpty) {
        final keyword = request.keyword!.toLowerCase();
        if (!product.name.toLowerCase().contains(keyword) &&
            !product.brandName.toLowerCase().contains(keyword) &&
            !product.categoryName.toLowerCase().contains(keyword)) {
          return false;
        }
      }
      
      if (request.categoryId != null && product.categoryId != request.categoryId) {
        return false;
      }
      
      if (request.brandId != null && product.brandId != request.brandId) {
        return false;
      }
      
      if (request.minPrice != null && product.price < request.minPrice!) {
        return false;
      }
      
      if (request.maxPrice != null && product.price > request.maxPrice!) {
        return false;
      }
      
      if (request.condition != null && product.condition != request.condition) {
        return false;
      }
      
      return true;
    }).toList();
    
    // 模拟排序
    if (request.sortBy != null) {
      switch (request.sortBy) {
        case 'price':
          filteredProducts.sort((a, b) => request.sortOrder == 'desc' 
              ? b.price.compareTo(a.price) 
              : a.price.compareTo(b.price));
          break;
        case 'createTime':
          filteredProducts.sort((a, b) => request.sortOrder == 'desc' 
              ? b.createTime.compareTo(a.createTime) 
              : a.createTime.compareTo(b.createTime));
          break;
        case 'viewCount':
          filteredProducts.sort((a, b) => request.sortOrder == 'desc' 
              ? (b.viewCount ?? 0).compareTo(a.viewCount ?? 0) 
              : (a.viewCount ?? 0).compareTo(b.viewCount ?? 0));
          break;
      }
    }
    
    // 模拟分页
    final startIndex = (request.page - 1) * request.pageSize;
    final endIndex = startIndex + request.pageSize;
    final pageData = filteredProducts.skip(startIndex).take(request.pageSize).toList();
    
    return PaginatedData<Product>(
      data: pageData,
      currentPage: request.page,
      totalPages: (filteredProducts.length / request.pageSize).ceil(),
      totalItems: filteredProducts.length,
      hasNextPage: endIndex < filteredProducts.length,
      hasPreviousPage: request.page > 1,
    );
  }

  @override
  Future<Product> getProductDetail(String productId) async {
    await Future<void>.delayed(const Duration(milliseconds: 500));
    
    final mockProducts = _generateMockProducts();
    final product = mockProducts.firstWhere(
      (p) => p.id == productId,
      orElse: () => throw Exception('Product not found'),
    );
    
    return product;
  }

  @override
  Future<List<ProductCategory>> getCategories() async {
    await Future<void>.delayed(const Duration(milliseconds: 300));
    
    return [
      const ProductCategory(
        id: 'cat_phone',
        name: '手机',
        icon: 'phone_android',
        productCount: 1250,
        isActive: true,
        sortOrder: 1,
      ),
      const ProductCategory(
        id: 'cat_laptop',
        name: '笔记本电脑',
        icon: 'laptop',
        productCount: 890,
        isActive: true,
        sortOrder: 2,
      ),
      const ProductCategory(
        id: 'cat_tablet',
        name: '平板电脑',
        icon: 'tablet',
        productCount: 456,
        isActive: true,
        sortOrder: 3,
      ),
      const ProductCategory(
        id: 'cat_watch',
        name: '智能手表',
        icon: 'watch',
        productCount: 234,
        isActive: true,
        sortOrder: 4,
      ),
      const ProductCategory(
        id: 'cat_headphone',
        name: '耳机音响',
        icon: 'headphones',
        productCount: 567,
        isActive: true,
        sortOrder: 5,
      ),
      const ProductCategory(
        id: 'cat_camera',
        name: '相机摄像',
        icon: 'camera_alt',
        productCount: 123,
        isActive: true,
        sortOrder: 6,
      ),
    ];
  }

  @override
  Future<PaginatedData<Product>> getProductsByCategory(
    String categoryId, 
    int page, 
    int pageSize, 
    String? sortBy, 
    String? sortOrder,
  ) async {
    final request = ProductSearchRequest(
      categoryId: categoryId,
      page: page,
      pageSize: pageSize,
      sortBy: sortBy,
      sortOrder: sortOrder,
    );
    return searchProducts(request);
  }

  @override
  Future<PaginatedData<Product>> getProductsByBrand(
    String brandId, 
    int page, 
    int pageSize, 
    String? sortBy, 
    String? sortOrder,
  ) async {
    final request = ProductSearchRequest(
      brandId: brandId,
      page: page,
      pageSize: pageSize,
      sortBy: sortBy,
      sortOrder: sortOrder,
    );
    return searchProducts(request);
  }

  @override
  Future<List<Product>> getRecommendedProducts(String? categoryId, String? userId, int limit) async {
    await Future<void>.delayed(const Duration(milliseconds: 400));
    
    final mockProducts = _generateMockProducts();
    return mockProducts.take(limit).toList();
  }

  @override
  Future<List<Product>> getPopularProducts(String? categoryId, int limit) async {
    await Future<void>.delayed(const Duration(milliseconds: 400));
    
    final mockProducts = _generateMockProducts();
    // 按浏览次数排序
    mockProducts.sort((a, b) => (b.viewCount ?? 0).compareTo(a.viewCount ?? 0));
    return mockProducts.take(limit).toList();
  }

  @override
  Future<List<Product>> getLatestProducts(String? categoryId, int limit) async {
    await Future<void>.delayed(const Duration(milliseconds: 400));
    
    final mockProducts = _generateMockProducts();
    // 按创建时间排序
    mockProducts.sort((a, b) => b.createTime.compareTo(a.createTime));
    return mockProducts.take(limit).toList();
  }

  @override
  Future<List<Product>> getSimilarProducts(String productId, int limit) async {
    await Future<void>.delayed(const Duration(milliseconds: 400));
    
    final mockProducts = _generateMockProducts();
    // 排除当前商品
    final similarProducts = mockProducts.where((p) => p.id != productId).toList();
    return similarProducts.take(limit).toList();
  }

  @override
  Future<void> addToFavorites(String productId) async {
    await Future<void>.delayed(const Duration(milliseconds: 300));
    // 模拟添加到收藏
  }

  @override
  Future<void> removeFromFavorites(String productId) async {
    await Future<void>.delayed(const Duration(milliseconds: 300));
    // 模拟从收藏中移除
  }

  @override
  Future<PaginatedData<Product>> getFavoriteProducts(int page, int pageSize) async {
    await Future<void>.delayed(const Duration(milliseconds: 500));
    
    final mockProducts = _generateMockProducts().take(5).toList();
    
    return PaginatedData<Product>(
      data: mockProducts,
      currentPage: page,
      totalPages: 1,
      totalItems: mockProducts.length,
      hasNextPage: false,
      hasPreviousPage: false,
    );
  }

  @override
  Future<bool> isFavorite(String productId) async {
    await Future<void>.delayed(const Duration(milliseconds: 200));
    // 模拟收藏状态
    return productId.hashCode % 3 == 0;
  }

  @override
  Future<void> incrementViewCount(String productId) async {
    await Future<void>.delayed(const Duration(milliseconds: 100));
    // 模拟增加浏览次数
  }

  @override
  Future<List<Product>> getViewHistory(int limit) async {
    await Future<void>.delayed(const Duration(milliseconds: 400));
    
    final mockProducts = _generateMockProducts();
    return mockProducts.take(limit).toList();
  }

  @override
  Future<List<String>> getSearchSuggestions(String keyword) async {
    await Future<void>.delayed(const Duration(milliseconds: 200));
    
    final suggestions = [
      'iPhone 14 Pro',
      'iPhone 13',
      'iPhone 12',
      'MacBook Pro',
      'MacBook Air',
      'iPad Pro',
      'iPad Air',
      'Apple Watch',
      'AirPods Pro',
      'Samsung Galaxy',
    ];
    
    return suggestions
        .where((s) => s.toLowerCase().contains(keyword.toLowerCase()))
        .take(5)
        .toList();
  }

  @override
  Future<List<String>> getHotSearchKeywords(int limit) async {
    await Future<void>.delayed(const Duration(milliseconds: 200));
    
    return [
      'iPhone',
      'MacBook',
      'iPad',
      'Samsung',
      'Huawei',
      'Xiaomi',
      'OPPO',
      'Vivo',
      'OnePlus',
      'Google Pixel',
    ].take(limit).toList();
  }

  /// 生成模拟商品数据
  List<Product> _generateMockProducts() {
    final now = DateTime.now();
    
    return [
      Product(
        id: 'prod_001',
        name: 'iPhone 14 Pro 256GB 深空黑色',
        brandId: 'brand_apple',
        brandName: 'Apple',
        categoryId: 'cat_phone',
        categoryName: '手机',
        price: 7999.0,
        originalPrice: 8999.0,
        condition: 'excellent',
        conditionDesc: '95新',
        images: [
          'https://example.com/iphone14pro_1.jpg',
          'https://example.com/iphone14pro_2.jpg',
          'https://example.com/iphone14pro_3.jpg',
        ],
        description: 'iPhone 14 Pro，搭载A16仿生芯片，4800万像素主摄系统，支持ProRAW拍摄。外观几乎全新，功能完好，配件齐全。',
        specifications: {
          '屏幕尺寸': '6.1英寸',
          '存储容量': '256GB',
          '颜色': '深空黑色',
          '网络': '5G',
          '电池健康度': '98%',
        },
        isAvailable: true,
        stock: 1,
        sellerId: 'seller_001',
        sellerName: '数码小王',
        sellerRating: 4.8,
        location: '北京市朝阳区',
        createTime: now.subtract(const Duration(days: 2)).millisecondsSinceEpoch,
        updateTime: now.subtract(const Duration(hours: 3)).millisecondsSinceEpoch,
        tags: ['热门', '5G', '拍照神器'],
        discount: 1000.0,
        viewCount: 1250,
        favoriteCount: 89,
      ),
      Product(
        id: 'prod_002',
        name: 'MacBook Pro 14英寸 M2 Pro芯片',
        brandId: 'brand_apple',
        brandName: 'Apple',
        categoryId: 'cat_laptop',
        categoryName: '笔记本电脑',
        price: 15999.0,
        originalPrice: 18999.0,
        condition: 'good',
        conditionDesc: '9成新',
        images: [
          'https://example.com/macbookpro_1.jpg',
          'https://example.com/macbookpro_2.jpg',
        ],
        description: 'MacBook Pro 14英寸，搭载M2 Pro芯片，16GB内存，512GB SSD。轻微使用痕迹，性能强劲，适合专业工作。',
        specifications: {
          '屏幕尺寸': '14.2英寸',
          '处理器': 'M2 Pro',
          '内存': '16GB',
          '存储': '512GB SSD',
          '颜色': '深空灰色',
        },
        isAvailable: true,
        stock: 1,
        sellerId: 'seller_002',
        sellerName: '科技达人',
        sellerRating: 4.9,
        location: '上海市浦东新区',
        createTime: now.subtract(const Duration(days: 5)).millisecondsSinceEpoch,
        updateTime: now.subtract(const Duration(days: 1)).millisecondsSinceEpoch,
        tags: ['专业', '高性能', 'M2芯片'],
        discount: 3000.0,
        viewCount: 890,
        favoriteCount: 156,
      ),
      Product(
        id: 'prod_003',
        name: 'iPad Pro 11英寸 第4代 128GB',
        brandId: 'brand_apple',
        brandName: 'Apple',
        categoryId: 'cat_tablet',
        categoryName: '平板电脑',
        price: 5299.0,
        originalPrice: 6199.0,
        condition: 'excellent',
        conditionDesc: '99新',
        images: [
          'https://example.com/ipadpro_1.jpg',
          'https://example.com/ipadpro_2.jpg',
        ],
        description: 'iPad Pro 11英寸第4代，M2芯片，128GB存储。几乎全新，带原装充电器和数据线。',
        specifications: {
          '屏幕尺寸': '11英寸',
          '处理器': 'M2',
          '存储容量': '128GB',
          '网络': 'Wi-Fi',
          '颜色': '银色',
        },
        isAvailable: true,
        stock: 1,
        sellerId: 'seller_003',
        sellerName: '平板专家',
        sellerRating: 4.7,
        location: '广州市天河区',
        createTime: now.subtract(const Duration(days: 1)).millisecondsSinceEpoch,
        updateTime: now.subtract(const Duration(hours: 12)).millisecondsSinceEpoch,
        tags: ['新品', 'M2芯片', '创作'],
        discount: 900.0,
        viewCount: 567,
        favoriteCount: 78,
      ),
    ];
  }
}

/// 商品远程数据源Provider
final productRemoteDataSourceProvider = Provider<ProductRemoteDataSource>((ref) {
  return ProductRemoteDataSourceImpl();
});
