import 'dart:io';

/// 网络连接信息接口
abstract class NetworkInfo {
  Future<bool> get isConnected;
}

/// 网络连接信息实现
class NetworkInfoImpl implements NetworkInfo {
  @override
  Future<bool> get isConnected async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  }
}
