import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 支付后续步骤组件
class PaymentNextSteps extends StatelessWidget {
  const PaymentNextSteps({
    super.key,
    required this.success,
    required this.orderId,
  });

  final bool success;
  final String orderId;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.timeline,
                size: 20.w,
                color: Theme.of(context).primaryColor,
              ),
              SizedBox(width: 8.w),
              Text(
                success ? '接下来会发生什么？' : '您可以尝试',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          
          // 步骤列表
          if (success) ..._buildSuccessSteps() else ..._buildFailureSteps(),
        ],
      ),
    );
  }

  /// 构建成功后的步骤
  List<Widget> _buildSuccessSteps() {
    final steps = [
      StepInfo(
        icon: Icons.inventory_2,
        title: '商家确认订单',
        description: '商家将在24小时内确认您的订单',
        status: StepStatus.pending,
      ),
      StepInfo(
        icon: Icons.local_shipping,
        title: '准备发货',
        description: '商品将在1-3个工作日内发货',
        status: StepStatus.pending,
      ),
      StepInfo(
        icon: Icons.location_on,
        title: '物流配送',
        description: '您可以在订单详情中跟踪物流信息',
        status: StepStatus.pending,
      ),
      StepInfo(
        icon: Icons.check_circle,
        title: '确认收货',
        description: '收到商品后请及时确认收货',
        status: StepStatus.pending,
      ),
    ];

    return steps.asMap().entries.map((entry) {
      final index = entry.key;
      final step = entry.value;
      final isLast = index == steps.length - 1;
      
      return _buildStepItem(step, isLast);
    }).toList();
  }

  /// 构建失败后的建议
  List<Widget> _buildFailureSteps() {
    final suggestions = [
      StepInfo(
        icon: Icons.refresh,
        title: '重新支付',
        description: '检查网络连接后重新尝试支付',
        status: StepStatus.action,
      ),
      StepInfo(
        icon: Icons.account_balance_wallet,
        title: '检查账户余额',
        description: '确保支付账户有足够的余额',
        status: StepStatus.action,
      ),
      StepInfo(
        icon: Icons.support_agent,
        title: '联系客服',
        description: '如问题持续存在，请联系客服获取帮助',
        status: StepStatus.action,
      ),
    ];

    return suggestions.asMap().entries.map((entry) {
      final index = entry.key;
      final suggestion = entry.value;
      final isLast = index == suggestions.length - 1;
      
      return _buildStepItem(suggestion, isLast);
    }).toList();
  }

  /// 构建步骤项
  Widget _buildStepItem(StepInfo step, bool isLast) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 左侧图标和连接线
        Column(
          children: [
            Container(
              width: 32.w,
              height: 32.w,
              decoration: BoxDecoration(
                color: _getStepColor(step.status).withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                step.icon,
                size: 16.w,
                color: _getStepColor(step.status),
              ),
            ),
            if (!isLast)
              Container(
                width: 2.w,
                height: 40.h,
                margin: EdgeInsets.symmetric(vertical: 8.h),
                color: Colors.grey[300],
              ),
          ],
        ),
        SizedBox(width: 12.w),
        
        // 右侧内容
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(bottom: isLast ? 0 : 16.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  step.title,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[800],
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  step.description,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.grey[600],
                    height: 1.3,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 获取步骤颜色
  Color _getStepColor(StepStatus status) {
    switch (status) {
      case StepStatus.completed:
        return Colors.green;
      case StepStatus.pending:
        return Colors.blue;
      case StepStatus.action:
        return Colors.orange;
    }
  }
}

/// 步骤信息
class StepInfo {
  const StepInfo({
    required this.icon,
    required this.title,
    required this.description,
    required this.status,
  });

  final IconData icon;
  final String title;
  final String description;
  final StepStatus status;
}

/// 步骤状态
enum StepStatus {
  completed,
  pending,
  action,
}
