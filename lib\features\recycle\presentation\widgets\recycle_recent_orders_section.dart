import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';

import 'package:soko/core/utils/date_utils.dart' as app_date_utils;
import 'package:soko/shared/presentation/widgets/custom_card.dart';
import 'package:soko/features/recycle/domain/entities/recycle_order.dart';

/// 最近回收订单组件
class RecycleRecentOrdersSection extends StatelessWidget {

  const RecycleRecentOrdersSection({
    super.key,
    required this.orders,
    required this.onOrderTap,
    required this.onViewAll,
  });
  final List<RecycleOrder> orders;
  final ValueChanged<RecycleOrder> onOrderTap;
  final VoidCallback onViewAll;

  @override
  Widget build(BuildContext context) {
    if (orders.isEmpty) {
      return _buildEmptyState();
    }

    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.history,
                color: Colors.brown,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                '最近订单',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const Spacer(),
              InkWell(
                onTap: onViewAll,
                child: Text(
                  '查看全部',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.blue,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // 订单列表
          ...orders.map(_buildOrderItem),
        ],
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return CustomCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.history,
                color: Colors.brown,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                '最近订单',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // 空状态
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(32.w),
            child: Column(
              children: [
                Icon(
                  Icons.inbox_outlined,
                  size: 48.w,
                  color: Colors.grey[400],
                ),
                SizedBox(height: 12.h),
                Text(
                  '暂无回收订单',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Colors.grey[600],
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  '创建您的第一个回收订单吧',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建订单项
  Widget _buildOrderItem(RecycleOrder order) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: InkWell(
        onTap: () => onOrderTap(order),
        borderRadius: BorderRadius.circular(8.r),
        child: Container(
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            color: Colors.grey.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
          ),
          child: Row(
            children: [
              // 商品图片
              ClipRRect(
                borderRadius: BorderRadius.circular(6.r),
                child: order.primaryImage?.isNotEmpty == true
                    ? CachedNetworkImage(
                        imageUrl: order.primaryImage!,
                        width: 50.w,
                        height: 50.w,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          width: 50.w,
                          height: 50.w,
                          color: Colors.grey[200],
                          child: Icon(
                            Icons.image,
                            color: Colors.grey[400],
                            size: 20.w,
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          width: 50.w,
                          height: 50.w,
                          color: Colors.grey[200],
                          child: Icon(
                            Icons.broken_image,
                            color: Colors.grey[400],
                            size: 20.w,
                          ),
                        ),
                      )
                    : Container(
                        width: 50.w,
                        height: 50.w,
                        color: Colors.grey[200],
                        child: Icon(
                          Icons.devices,
                          color: Colors.grey[400],
                          size: 20.w,
                        ),
                      ),
              ),
              SizedBox(width: 12.w),

              // 订单信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 商品名称
                    Text(
                      '${order.brandName} ${order.model}',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 4.h),

                    // 订单状态和时间
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                          decoration: BoxDecoration(
                            color: _getStatusColor(order.statusEnum).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(4.r),
                          ),
                          child: Text(
                            order.orderStatusDesc,
                            style: TextStyle(
                              fontSize: 10.sp,
                              color: _getStatusColor(order.statusEnum),
                            ),
                          ),
                        ),
                        SizedBox(width: 8.w),
                        Text(
                          app_date_utils.DateUtils.formatDateTime(
                            DateTime.fromMillisecondsSinceEpoch(order.createTime),
                          ),
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Colors.grey[500],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // 价格
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    '¥${order.actualPrice.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.red,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Icon(
                    Icons.chevron_right,
                    color: Colors.grey[400],
                    size: 16.w,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 获取状态颜色
  Color _getStatusColor(RecycleOrderStatus status) {
    switch (status) {
      case RecycleOrderStatus.created:
        return Colors.orange;
      case RecycleOrderStatus.confirmed:
        return Colors.blue;
      case RecycleOrderStatus.picked:
        return Colors.purple;
      case RecycleOrderStatus.evaluated:
        return Colors.teal;
      case RecycleOrderStatus.completed:
        return Colors.green;
      case RecycleOrderStatus.cancelled:
        return Colors.grey;
    }
  }
}
