import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import 'package:soko/core/theme/app_colors.dart';
import 'package:soko/core/theme/app_text_styles.dart';
import 'package:soko/shared/presentation/widgets/loading_widget.dart';
import 'package:soko/shared/presentation/widgets/error_widget.dart';
import 'package:soko/features/cart/presentation/providers/cart_provider.dart';
import 'package:soko/features/product/presentation/providers/product_detail_provider.dart';
import 'package:soko/features/product/presentation/widgets/product_image_carousel.dart';
import 'package:soko/features/product/presentation/widgets/product_spec_selector.dart';
import 'package:soko/features/product/domain/entities/product.dart';

/// Product 扩展方法
extension ProductDetailExtension on Product {
  /// 获取所有图片URL列表
  List<String> get images {
    if (files == null || files!.isEmpty) return [];
    return files!.map((file) => file.url).toList();
  }

  /// 获取规格列表（适配SKU）
  List<ProductSpec> get specs {
    if (skus == null || skus!.isEmpty) return [];
    return skus!
        .map((sku) => ProductSpec(
              id: sku.id,
              name: sku.name,
              price: sku.price,
              stock: sku.stock,
            ),)
        .toList();
  }

  /// 获取详情图片列表
  List<String> get detailImages {
    // 暂时返回空列表，后续可以根据需要扩展
    return [];
  }
}

/// 商品规格适配类
class ProductSpec {

  const ProductSpec({
    required this.id,
    required this.name,
    required this.price,
    required this.stock,
  });
  final String id;
  final String name;
  final double price;
  final int stock;
}

/// 商品详情页面
class ProductDetailPage extends ConsumerWidget {

  const ProductDetailPage({
    super.key,
    required this.productId,
  });
  final String productId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(productDetailProvider(productId));

    return Scaffold(
      backgroundColor: AppColors.background,
      body: state.isLoading && state.product == null
          ? const LoadingWidget()
          : state.error != null && state.product == null
              ? ErrorDisplayWidget(
                  message: state.error!,
                  onRetry: () => ref
                      .read(productDetailProvider(productId).notifier)
                      .refresh(),
                )
              : _buildContent(context, ref, state),
      bottomNavigationBar:
          state.product != null ? _buildBottomBar(context, ref, state) : null,
    );
  }

  /// 构建内容
  Widget _buildContent(BuildContext context, WidgetRef ref, state) {
    final product = state.product;
    final currentPrice =
        ref.read(productDetailProvider(productId).notifier).getCurrentPrice();

    return CustomScrollView(
      slivers: [
        // 自定义AppBar
        _buildSliverAppBar(context, ref, product),
        // 商品信息
        SliverToBoxAdapter(
          child: Column(
            children: [
              // 商品基本信息
              _buildProductInfo(product, currentPrice),
              // 规格选择
              if (product.skus?.isNotEmpty ?? false)
                ProductSpecSelector(
                  specs: product.skus!,
                  selectedSpecId: state.selectedSpecId,
                  onSpecSelected: (specId) {
                    ref
                        .read(productDetailProvider(productId).notifier)
                        .selectSpec(specId);
                  },
                ),
              // 商品详情
              _buildProductDetails(product),
              // 底部安全区域
              SizedBox(height: 80.h + MediaQuery.of(context).padding.bottom),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建SliverAppBar
  Widget _buildSliverAppBar(BuildContext context, WidgetRef ref, product) {
    return SliverAppBar(
      expandedHeight: 300.h,
      pinned: true,
      backgroundColor: Colors.white,
      foregroundColor: AppColors.textPrimary,
      leading: IconButton(
        onPressed: () => context.pop(),
        icon: Container(
          width: 32.w,
          height: 32.w,
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.3),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.arrow_back,
            color: Colors.white,
            size: 18.sp,
          ),
        ),
      ),
      actions: [
        IconButton(
          onPressed: () => _toggleFavorite(ref),
          icon: Container(
            width: 32.w,
            height: 32.w,
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.3),
              shape: BoxShape.circle,
            ),
            child: Icon(
              ref.watch(productDetailProvider(productId)).isFavorite
                  ? Icons.favorite
                  : Icons.favorite_border,
              color: ref.watch(productDetailProvider(productId)).isFavorite
                  ? AppColors.error
                  : Colors.white,
              size: 18.sp,
            ),
          ),
        ),
        IconButton(
          onPressed: () => _shareProduct(product),
          icon: Container(
            width: 32.w,
            height: 32.w,
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.3),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.share,
              color: Colors.white,
              size: 18.sp,
            ),
          ),
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: ProductImageCarousel(
          images: product.images,
          onImageTap: () => _showImagePreview(context, product.images),
        ),
      ),
    );
  }

  /// 构建商品信息
  Widget _buildProductInfo(product, double currentPrice) {
    return Container(
      padding: EdgeInsets.all(16.w),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 商品名称
          Text(
            product.name,
            style: AppTextStyles.headlineSmall.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),
          // 商品描述
          if (product.description != null &&
              product.description!.isNotEmpty) ...[
            Text(
              product.description!,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: 12.h),
          ],
          // 价格信息
          Row(
            children: [
              Text(
                '¥${currentPrice.toStringAsFixed(2)}',
                style: AppTextStyles.headlineMedium.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (product.originalPrice != null &&
                  product.originalPrice! > currentPrice) ...[
                SizedBox(width: 8.w),
                Text(
                  '¥${product.originalPrice!.toStringAsFixed(2)}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textTertiary,
                    decoration: TextDecoration.lineThrough,
                  ),
                ),
              ],
            ],
          ),
          SizedBox(height: 12.h),
          // 商品标签
          _buildProductTags(product),
        ],
      ),
    );
  }

  /// 构建商品标签
  Widget _buildProductTags(product) {
    final tags = <Widget>[];

    if (product.newable == true) {
      tags.add(_buildTag('新品', AppColors.success));
    }

    if (product.acg != null && product.acg!.isNotEmpty) {
      tags.add(_buildTag(product.acg!, AppColors.info));
    }

    if (product.brand != null && product.brand!.isNotEmpty) {
      tags.add(_buildTag(product.brand!, AppColors.warning));
    }

    if (tags.isEmpty) return const SizedBox.shrink();

    return Wrap(
      spacing: 8.w,
      runSpacing: 8.h,
      children: tags,
    );
  }

  /// 构建标签
  Widget _buildTag(String text, Color color) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4.r),
        border: Border.all(color: color.withOpacity(0.3), width: 0.5),
      ),
      child: Text(
        text,
        style: AppTextStyles.caption.copyWith(
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 构建商品详情
  Widget _buildProductDetails(product) {
    return Container(
      margin: EdgeInsets.only(top: 8.h),
      padding: EdgeInsets.all(16.w),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '商品详情',
            style: AppTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 12.h),
          // 商品详情内容
          if (product.detailImages != null &&
              product.detailImages!.isNotEmpty) ...[
            ...product.detailImages!.map((imageUrl) => Container(
                  margin: EdgeInsets.only(bottom: 8.h),
                  child: Image.network(
                    imageUrl,
                    width: double.infinity,
                    fit: BoxFit.fitWidth,
                    errorBuilder: (context, error, stackTrace) => Container(
                      height: 200.h,
                      color: AppColors.background,
                      child: Center(
                        child: Icon(
                          Icons.broken_image,
                          size: 48.sp,
                          color: AppColors.textTertiary,
                        ),
                      ),
                    ),
                  ),
                ),),
          ] else ...[
            Container(
              height: 200.h,
              alignment: Alignment.center,
              child: Text(
                '暂无详细信息',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textTertiary,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建底部操作栏
  Widget _buildBottomBar(BuildContext context, WidgetRef ref, state) {
    final notifier = ref.read(productDetailProvider(productId).notifier);
    final canPurchase = notifier.canPurchase;
    final hasStock = notifier.hasStock;

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 8,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // 收藏按钮
            IconButton(
              onPressed: () => _toggleFavorite(ref),
              icon: Icon(
                state.isFavorite ? Icons.favorite : Icons.favorite_border,
                color:
                    state.isFavorite ? AppColors.error : AppColors.textTertiary,
                size: 24.sp,
              ),
            ),
            SizedBox(width: 8.w),
            // 加入购物车按钮
            Expanded(
              child: OutlinedButton(
                onPressed: hasStock ? () => _addToCart(context, ref) : null,
                style: OutlinedButton.styleFrom(
                  side: const BorderSide(color: AppColors.primary),
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                ),
                child: Text(
                  hasStock ? '加入购物车' : '缺货',
                  style: TextStyle(
                    color:
                        hasStock ? AppColors.primary : AppColors.textDisabled,
                  ),
                ),
              ),
            ),
            SizedBox(width: 12.w),
            // 立即购买按钮
            Expanded(
              child: ElevatedButton(
                onPressed: canPurchase ? () => _buyNow(context, ref) : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                ),
                child: Text(
                  canPurchase ? '立即购买' : '缺货',
                  style: const TextStyle(
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 切换收藏状态
  void _toggleFavorite(WidgetRef ref) {
    ref.read(productDetailProvider(productId).notifier).toggleFavorite();
  }

  /// 分享商品
  void _shareProduct(product) {
    // TODO: 实现分享功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('分享功能待实现')),
    );
  }

  /// 显示图片预览
  void _showImagePreview(BuildContext context, List<String> images) {
    if (images.isNotEmpty) {
      showDialog(
        context: context,
        builder: (context) => ProductImagePreviewDialog(
          images: images,
        ),
      );
    }
  }

  /// 加入购物车
  void _addToCart(BuildContext context, WidgetRef ref) {
    final state = ref.read(productDetailProvider(productId));
    final product = state.product!;

    if (product.skus?.isNotEmpty ?? false) {
      // 显示规格选择弹窗
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => ProductSpecBottomSheet(
          product: product,
          selectedSpecId: state.selectedSpecId,
          quantity: state.quantity,
          onConfirm: (specId, quantity) {
            _performAddToCart(context, ref, specId, quantity);
          },
        ),
      );
    } else {
      _performAddToCart(context, ref, null, 1);
    }
  }

  /// 执行加入购物车操作
  void _performAddToCart(
      BuildContext context, WidgetRef ref, String? specId, int quantity,) {
    ref.read(cartProvider.notifier).addToCart(
          productId: productId,
          skuId: specId,
          quantity: quantity,
        );

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('已加入购物车：$quantity 件')),
    );
  }

  /// 立即购买
  void _buyNow(BuildContext context, WidgetRef ref) {
    final state = ref.read(productDetailProvider(productId));
    final product = state.product!;

    if (product.skus?.isNotEmpty ?? false) {
      // 显示规格选择弹窗
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => ProductSpecBottomSheet(
          product: product,
          selectedSpecId: state.selectedSpecId,
          quantity: state.quantity,
          onConfirm: (specId, quantity) {
            // TODO: 跳转到订单确认页面
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('立即购买：$quantity 件')),
            );
          },
        ),
      );
    } else {
      // TODO: 直接跳转到订单确认页面
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('立即购买')),
      );
    }
  }
}
