/// 应用常量
class AppConstants {
  // 私有构造函数
  AppConstants._();

  // 应用信息
  static const String appName = '中古虾';
  static const String appVersion = '1.0.0';
  static const String appPackageName = 'com.soko.app';

  // 网络相关
  static const int connectTimeout = 60000;
  static const int receiveTimeout = 60000;
  static const int sendTimeout = 60000;

  // 分页相关
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  static const int firstPage = 1;

  // 缓存相关
  static const int imageCacheMaxAge = 7 * 24 * 60 * 60; // 7天
  static const int apiCacheMaxAge = 5 * 60; // 5分钟
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB

  // 文件相关
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const int maxVideoSize = 50 * 1024 * 1024; // 50MB
  static const List<String> supportedImageTypes = ['jpg', 'jpeg', 'png', 'webp'];
  static const List<String> supportedVideoTypes = ['mp4', 'avi', 'mov'];

  // 业务相关
  static const double minOrderAmount = 0.01;
  static const double maxOrderAmount = 99999.99;
  static const int maxCartItems = 99;
  static const int maxAddressCount = 20;
  static const int maxCouponCount = 50;

  // 动画时长
  static const int shortAnimationDuration = 200;
  static const int mediumAnimationDuration = 300;
  static const int longAnimationDuration = 500;

  // 防抖时长
  static const int debounceDelay = 500;
  static const int throttleDelay = 1000;

  // 重试相关
  static const int maxRetryCount = 3;
  static const int retryDelay = 1000;

  // 正则表达式
  static const String phoneRegex = r'^1[3-9]\d{9}$';
  static const String emailRegex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String passwordRegex = r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$';
  static const String idCardRegex = r'^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$';

  // 默认值
  static const String defaultAvatar = 'assets/images/default_avatar.png';
  static const String defaultProductImage = 'assets/images/default_product.png';
  static const String defaultBannerImage = 'assets/images/default_banner.png';

  // 外部链接
  static const String privacyPolicyUrl = 'https://soko.com/privacy';
  static const String userAgreementUrl = 'https://soko.com/agreement';
  static const String aboutUsUrl = 'https://soko.com/about';
  static const String helpCenterUrl = 'https://soko.com/help';

  // 分享相关
  static const String shareAppUrl = 'https://soko.com/download';
  static const String shareProductUrlTemplate = 'https://soko.com/product/{id}';

  // 推送相关
  static const String pushChannelId = 'soko_notification';
  static const String pushChannelName = '中古虾通知';
  static const String pushChannelDescription = '中古虾应用通知';

  // 统计相关
  static const String analyticsEventPrefix = 'soko_';
  static const String crashlyticsCollectionEnabled = 'crashlytics_collection_enabled';

  // 主题相关
  static const String lightTheme = 'light';
  static const String darkTheme = 'dark';
  static const String systemTheme = 'system';

  // 语言相关
  static const String chineseLanguage = 'zh_CN';
  static const String englishLanguage = 'en_US';

  // 地图相关
  static const double defaultLatitude = 39.9042;
  static const double defaultLongitude = 116.4074;
  static const double defaultZoom = 15;

  // 支付相关
  static const List<String> supportedPaymentMethods = ['alipay', 'wechat', 'unionpay'];
  static const int paymentTimeout = 30 * 60; // 30分钟

  // 评价相关
  static const int maxRating = 5;
  static const int minRating = 1;
  static const int maxReviewLength = 500;

  // 搜索相关
  static const int maxSearchHistoryCount = 20;
  static const int maxHotSearchCount = 10;
  static const int searchDebounceDelay = 300;

  // 上传相关
  static const int maxUploadConcurrency = 3;
  static const int uploadChunkSize = 1024 * 1024; // 1MB

  // 下载相关
  static const int maxDownloadConcurrency = 2;
  static const String downloadDirectory = 'downloads';

  // 日志相关
  static const int maxLogFileSize = 10 * 1024 * 1024; // 10MB
  static const int maxLogFileCount = 5;
  static const String logDirectory = 'logs';

  // 数据库相关
  static const String databaseName = 'soko.db';
  static const int databaseVersion = 1;

  // WebView相关
  static const String userAgent = 'SokoApp/1.0.0 (Flutter)';
  static const int webViewTimeout = 30000;

  // 二维码相关
  static const int qrCodeSize = 200;
  static const String qrCodeErrorCorrectionLevel = 'M';

  // 生物识别相关
  static const String biometricReason = '请验证您的身份';
  static const bool biometricStickyAuth = true;

  // 位置相关
  static const double locationAccuracy = 100;
  static const int locationTimeout = 30000;

  // 相机相关
  static const int maxImageQuality = 85;
  static const double maxImageWidth = 1920;
  static const double maxImageHeight = 1080;
}
