import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 回收订单创建 - 联系信息步骤
class RecycleCreateContactStep extends ConsumerStatefulWidget {

  const RecycleCreateContactStep({
    super.key,
    this.contactName,
    this.contactPhone,
    this.pickupAddress,
    required this.onContactInfoChanged,
  });
  final String? contactName;
  final String? contactPhone;
  final String? pickupAddress;
  final Function(String?, String?, String?) onContactInfoChanged;

  @override
  ConsumerState<RecycleCreateContactStep> createState() => _RecycleCreateContactStepState();
}

class _RecycleCreateContactStepState extends ConsumerState<RecycleCreateContactStep> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _nameController.text = widget.contactName ?? '';
    _phoneController.text = widget.contactPhone ?? '';
    _addressController.text = widget.pickupAddress ?? '';
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 步骤标题
          Text(
            '填写联系信息',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '请填写准确的联系方式，便于我们安排上门回收',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 24.h),

          // 联系信息表单
          Expanded(
            child: _buildContactForm(),
          ),
        ],
      ),
    );
  }

  /// 构建联系信息表单
  Widget _buildContactForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 联系人姓名
        Text(
          '联系人姓名 *',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        SizedBox(height: 8.h),
        TextField(
          controller: _nameController,
          decoration: InputDecoration(
            hintText: '请输入联系人姓名',
            prefixIcon: const Icon(Icons.person_outline),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
          ),
          onChanged: (value) {
            _updateContactInfo();
          },
        ),
        SizedBox(height: 20.h),

        // 联系电话
        Text(
          '联系电话 *',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        SizedBox(height: 8.h),
        TextField(
          controller: _phoneController,
          keyboardType: TextInputType.phone,
          decoration: InputDecoration(
            hintText: '请输入手机号码',
            prefixIcon: const Icon(Icons.phone_outlined),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
          ),
          onChanged: (value) {
            _updateContactInfo();
          },
        ),
        SizedBox(height: 20.h),

        // 取货地址
        Text(
          '取货地址 *',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        SizedBox(height: 8.h),
        TextField(
          controller: _addressController,
          maxLines: 3,
          decoration: InputDecoration(
            hintText: '请输入详细的取货地址，包括省市区、街道、门牌号等',
            prefixIcon: const Icon(Icons.location_on_outlined),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
          ),
          onChanged: (value) {
            _updateContactInfo();
          },
        ),
        SizedBox(height: 16.h),

        // 提示信息
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.local_shipping_outlined,
                    color: Colors.green,
                    size: 16.w,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    '上门回收服务',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.green[700],
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8.h),
              Text(
                '• 我们提供免费上门回收服务\n• 工作人员会提前电话联系确认时间\n• 请确保联系方式准确无误\n• 建议选择您方便接收的时间段',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Colors.green[600],
                  height: 1.4,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 更新联系信息
  void _updateContactInfo() {
    widget.onContactInfoChanged(
      _nameController.text,
      _phoneController.text,
      _addressController.text,
    );
  }
}
