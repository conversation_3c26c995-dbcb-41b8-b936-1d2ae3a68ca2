import 'package:json_annotation/json_annotation.dart';

import 'package:soko/core/enums/app_enums.dart';

part 'address.g.dart';

/// 地址实体类
@JsonSerializable()
class Address {

  const Address({
    required this.id,
    required this.userId,
    required this.receiverName,
    required this.receiverPhone,
    required this.province,
    required this.city,
    required this.district,
    required this.address,
    this.postalCode,
    this.addressType,
    required this.isDefault,
    required this.createTime,
    required this.updateTime,
  });

  factory Address.fromJson(Map<String, dynamic> json) => _$AddressFromJson(json);
  @JsonKey(name: 'id')
  final String id;

  @Json<PERSON>ey(name: 'userId')
  final String userId;

  @J<PERSON><PERSON><PERSON>(name: 'receiverName')
  final String receiverName;

  @JsonKey(name: 'receiverPhone')
  final String receiverPhone;

  @JsonKey(name: 'province')
  final String province;

  @JsonKey(name: 'city')
  final String city;

  @<PERSON>sonKey(name: 'district')
  final String district;

  @Json<PERSON>ey(name: 'address')
  final String address;

  @Json<PERSON>ey(name: 'postalCode')
  final String? postalCode;

  @Json<PERSON>ey(name: 'addressType')
  final String? addressType;

  @JsonKey(name: 'isDefault')
  final bool isDefault;

  @JsonKey(name: 'createTime')
  final int createTime;

  @JsonKey(name: 'updateTime')
  final int updateTime;

  Map<String, dynamic> toJson() => _$AddressToJson(this);

  /// 获取完整地址
  String get fullAddress => '$province$city$district$address';

  /// 获取地址类型枚举
  AddressType get addressTypeEnum {
    switch (addressType) {
      case 'home':
        return AddressType.home;
      case 'office':
        return AddressType.office;
      default:
        return AddressType.other;
    }
  }

  /// 获取格式化的手机号
  String get formattedPhone {
    if (receiverPhone.length == 11) {
      return '${receiverPhone.substring(0, 3)} ${receiverPhone.substring(3, 7)} ${receiverPhone.substring(7)}';
    }
    return receiverPhone;
  }

  /// 复制并更新地址信息
  Address copyWith({
    String? id,
    String? userId,
    String? receiverName,
    String? receiverPhone,
    String? province,
    String? city,
    String? district,
    String? address,
    String? postalCode,
    String? addressType,
    bool? isDefault,
    int? createTime,
    int? updateTime,
  }) {
    return Address(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      receiverName: receiverName ?? this.receiverName,
      receiverPhone: receiverPhone ?? this.receiverPhone,
      province: province ?? this.province,
      city: city ?? this.city,
      district: district ?? this.district,
      address: address ?? this.address,
      postalCode: postalCode ?? this.postalCode,
      addressType: addressType ?? this.addressType,
      isDefault: isDefault ?? this.isDefault,
      createTime: createTime ?? this.createTime,
      updateTime: updateTime ?? this.updateTime,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Address && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Address(id: $id, receiverName: $receiverName, fullAddress: $fullAddress)';
  }
}

/// 创建地址请求模型
@JsonSerializable()
class CreateAddressRequest {

  const CreateAddressRequest({
    required this.receiverName,
    required this.receiverPhone,
    required this.province,
    required this.city,
    required this.district,
    required this.address,
    this.postalCode,
    this.addressType,
    required this.isDefault,
  });

  factory CreateAddressRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateAddressRequestFromJson(json);
  @JsonKey(name: 'receiverName')
  final String receiverName;

  @JsonKey(name: 'receiverPhone')
  final String receiverPhone;

  @JsonKey(name: 'province')
  final String province;

  @JsonKey(name: 'city')
  final String city;

  @JsonKey(name: 'district')
  final String district;

  @JsonKey(name: 'address')
  final String address;

  @JsonKey(name: 'postalCode')
  final String? postalCode;

  @JsonKey(name: 'addressType')
  final String? addressType;

  @JsonKey(name: 'isDefault')
  final bool isDefault;

  Map<String, dynamic> toJson() => _$CreateAddressRequestToJson(this);
}

/// 更新地址请求模型
@JsonSerializable()
class UpdateAddressRequest {

  const UpdateAddressRequest({
    this.receiverName,
    this.receiverPhone,
    this.province,
    this.city,
    this.district,
    this.address,
    this.postalCode,
    this.addressType,
    this.isDefault,
  });

  factory UpdateAddressRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateAddressRequestFromJson(json);
  @JsonKey(name: 'receiverName')
  final String? receiverName;

  @JsonKey(name: 'receiverPhone')
  final String? receiverPhone;

  @JsonKey(name: 'province')
  final String? province;

  @JsonKey(name: 'city')
  final String? city;

  @JsonKey(name: 'district')
  final String? district;

  @JsonKey(name: 'address')
  final String? address;

  @JsonKey(name: 'postalCode')
  final String? postalCode;

  @JsonKey(name: 'addressType')
  final String? addressType;

  @JsonKey(name: 'isDefault')
  final bool? isDefault;

  Map<String, dynamic> toJson() => _$UpdateAddressRequestToJson(this);
}
