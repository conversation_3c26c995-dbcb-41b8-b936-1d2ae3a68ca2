import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/core/theme/app_colors.dart';
import 'package:soko/core/theme/app_text_styles.dart';
import 'package:soko/features/cart/presentation/providers/cart_provider.dart';
import 'package:soko/features/cart/domain/entities/coupon.dart';
import 'package:soko/features/cart/domain/services/price_calculation_service.dart';

/// 购物车摘要栏
class CartSummaryBar extends ConsumerWidget {

  const CartSummaryBar({
    super.key,
    this.isEditMode = false,
    this.onCheckout,
    this.onDeleteSelected,
  });
  final bool isEditMode;
  final VoidCallback? onCheckout;
  final VoidCallback? onDeleteSelected;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final cartState = ref.watch(cartProvider);
    final calculation = cartState.priceCalculation ??
        PriceCalculationService.calculateCartPrice(
          items: cartState.items,
          coupon: cartState.selectedCoupon,
          memberDiscount: cartState.memberDiscount,
        );

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 8,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: isEditMode
            ? _buildEditModeBar(cartState)
            : _buildNormalBar(calculation),
      ),
    );
  }

  /// 构建普通模式底栏
  Widget _buildNormalBar(PriceCalculation calculation) {
    return Row(
      children: [
        // 价格信息
        Expanded(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 合计金额
              Row(
                children: [
                  Text(
                    '合计：',
                    style: AppTextStyles.bodyMedium,
                  ),
                  Text(
                    '¥${calculation.finalAmount.toStringAsFixed(2)}',
                    style: AppTextStyles.headlineSmall.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              // 运费信息
              if (calculation.shippingFee > 0) ...[
                SizedBox(height: 2.h),
                Text(
                  '运费：¥${calculation.shippingFee.toStringAsFixed(2)}',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ] else if (calculation.finalAmount > 0) ...[
                SizedBox(height: 2.h),
                Text(
                  '免运费',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.success,
                  ),
                ),
              ],
            ],
          ),
        ),
        SizedBox(width: 16.w),
        // 结算按钮
        ElevatedButton(
          onPressed: calculation.finalAmount > 0 ? onCheckout : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
          ),
          child: Text(
            '结算',
            style: AppTextStyles.bodyMedium.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建编辑模式底栏
  Widget _buildEditModeBar(CartState cartState) {
    final selectedItems = cartState.items
        .where((item) => item.selected && item.available)
        .toList();
    return Row(
      children: [
        // 选中商品数量
        Expanded(
          child: Text(
            '已选择 ${selectedItems.length} 件商品',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ),
        // 删除按钮
        ElevatedButton(
          onPressed: selectedItems.isNotEmpty ? onDeleteSelected : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.error,
            padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
          ),
          child: Text(
            '删除',
            style: AppTextStyles.bodyMedium.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}

/// 购物车价格详情组件
class CartPriceDetails extends ConsumerWidget {
  const CartPriceDetails({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final cartState = ref.watch(cartProvider);
    final summary = cartState.summary;

    if (summary.selectedItems == 0) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '价格详情',
            style: AppTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 12.h),
          // 商品金额
          _buildPriceRow(
            '商品金额',
            '¥${summary.totalAmount.toStringAsFixed(2)}',
          ),
          // 优惠金额
          if (summary.hasDiscount) ...[
            SizedBox(height: 8.h),
            _buildPriceRow(
              '优惠金额',
              '-¥${summary.totalDiscount.toStringAsFixed(2)}',
              valueColor: AppColors.success,
            ),
          ],
          // 运费
          if (summary.hasFreight) ...[
            SizedBox(height: 8.h),
            _buildPriceRow(
              '运费',
              '¥${summary.freight!.toStringAsFixed(2)}',
            ),
          ],
          // 分割线
          Container(
            margin: EdgeInsets.symmetric(vertical: 12.h),
            height: 1,
            color: AppColors.borderLight,
          ),
          // 实付金额
          _buildPriceRow(
            '实付金额',
            '¥${summary.finalAmount.toStringAsFixed(2)}',
            titleStyle: AppTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.w600,
            ),
            valueStyle: AppTextStyles.headlineSmall.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建价格行
  Widget _buildPriceRow(
    String title,
    String value, {
    TextStyle? titleStyle,
    TextStyle? valueStyle,
    Color? valueColor,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: titleStyle ?? AppTextStyles.bodyMedium,
        ),
        Text(
          value,
          style: valueStyle ??
              AppTextStyles.bodyMedium.copyWith(
                color: valueColor ?? AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
        ),
      ],
    );
  }
}
