import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import 'package:soko/core/theme/app_colors.dart';
import 'package:soko/core/theme/app_text_styles.dart';
import 'package:soko/shared/presentation/widgets/custom_app_bar.dart';
import 'package:soko/features/product/presentation/providers/category_provider.dart';
import 'package:soko/features/product/presentation/widgets/category_navigation.dart';
import 'package:soko/features/product/presentation/pages/product_list_page.dart';

/// 分类页面
class CategoryPage extends ConsumerStatefulWidget {
  const CategoryPage({super.key});

  @override
  ConsumerState<CategoryPage> createState() => _CategoryPageState();
}

class _CategoryPageState extends ConsumerState<CategoryPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CustomAppBar(
        title: '分类',
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: '分类导航'),
            Tab(text: '商品列表'),
          ],
          labelColor: AppColors.primary,
          unselectedLabelColor: AppColors.textSecondary,
          indicatorColor: AppColors.primary,
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildCategoryNavigation(),
          _buildProductList(),
        ],
      ),
    );
  }

  /// 构建分类导航
  Widget _buildCategoryNavigation() {
    return SingleChildScrollView(
      child: CategoryNavigation(
        onCategorySelected: (category) {
          // 选择分类后切换到商品列表页面
          ref.read(categorySelectionProvider.notifier).selectCategory(
            category,
            ref.read(categoryTreeProvider).categories,
          );
          _tabController.animateTo(1);
        },
        onAcgTypeSelected: (acgType) {
          // 选择ACG类型后切换到商品列表页面
          ref.read(categorySelectionProvider.notifier).selectAcgType(acgType);
          _tabController.animateTo(1);
        },
      ),
    );
  }

  /// 构建商品列表
  Widget _buildProductList() {
    final selection = ref.watch(categorySelectionProvider);
    
    if (!ref.read(categorySelectionProvider.notifier).hasSelection) {
      return _buildEmptySelection();
    }

    return Column(
      children: [
        // 选择的分类信息
        _buildSelectedCategoryInfo(selection),
        // 商品列表
        Expanded(
          child: ProductListPage(
            categoryId: selection.selectedCategory?.id,
            keyword: selection.selectedAcgType,
          ),
        ),
      ],
    );
  }

  /// 构建空选择状态
  Widget _buildEmptySelection() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.category_outlined,
            size: 64.sp,
            color: AppColors.textTertiary,
          ),
          SizedBox(height: 16.h),
          Text(
            '请先选择分类',
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '在分类导航中选择您感兴趣的分类',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textTertiary,
            ),
          ),
          SizedBox(height: 24.h),
          ElevatedButton(
            onPressed: () => _tabController.animateTo(0),
            child: const Text('去选择分类'),
          ),
        ],
      ),
    );
  }

  /// 构建选择的分类信息
  Widget _buildSelectedCategoryInfo(selection) {
    return Container(
      padding: EdgeInsets.all(16.w),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 分类路径
          if (selection.categoryPath.isNotEmpty) ...[
            Row(
              children: [
                Icon(
                  Icons.location_on_outlined,
                  size: 16.sp,
                  color: AppColors.textTertiary,
                ),
                SizedBox(width: 4.w),
                Expanded(
                  child: Text(
                    selection.categoryPath.map((cat) => cat.name).join(' > '),
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 8.h),
          ],
          
          // 当前分类
          Row(
            children: [
              if (selection.selectedCategory?.icon != null) ...[
                Text(
                  selection.selectedCategory!.icon!,
                  style: TextStyle(fontSize: 20.sp),
                ),
                SizedBox(width: 8.w),
              ],
              Expanded(
                child: Text(
                  selection.selectedCategory?.name ?? 
                  _getAcgTypeName(selection.selectedAcgType) ?? 
                  '未知分类',
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              // 清除选择按钮
              IconButton(
                onPressed: () {
                  ref.read(categorySelectionProvider.notifier).clearSelection();
                },
                icon: Icon(
                  Icons.close,
                  size: 20.sp,
                  color: AppColors.textTertiary,
                ),
                constraints: BoxConstraints(
                  minWidth: 32.w,
                  minHeight: 32.w,
                ),
                padding: EdgeInsets.zero,
              ),
            ],
          ),
          
          // 商品数量
          if (selection.selectedCategory?.productCount != null) ...[
            SizedBox(height: 4.h),
            Text(
              '共 ${selection.selectedCategory!.productCount} 件商品',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textTertiary,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 获取ACG类型名称
  String? _getAcgTypeName(String? acgType) {
    if (acgType == null) return null;
    
    final acgCategory = AcgCategory.fromCode(acgType);
    return acgCategory?.displayName ?? acgType;
  }
}

/// 分类选择页面
class CategorySelectionPage extends ConsumerWidget {

  const CategorySelectionPage({
    super.key,
    this.selectedCategoryId,
    this.onCategorySelected,
  });
  final String? selectedCategoryId;
  final Function(String categoryId)? onCategorySelected;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final categoryTreeState = ref.watch(categoryTreeProvider);
    final selectedCategory = selectedCategoryId != null
        ? ref.read(categoryTreeProvider.notifier).findCategoryById(selectedCategoryId!)
        : null;

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CustomAppBar(
        title: '选择分类',
        leading: IconButton(
          onPressed: () => context.pop(),
          icon: const Icon(Icons.arrow_back),
        ),
      ),
      body: SingleChildScrollView(
        child: CategoryNavigation(
          showHotCategories: true,
          onCategorySelected: (category) {
            onCategorySelected?.call(category.id);
            context.pop();
          },
          onAcgTypeSelected: (acgType) {
            // 找到对应的ACG分类
            final acgCategories = categoryTreeState.categories
                .where((cat) => cat.code == acgType)
                .toList();
            
            if (acgCategories.isNotEmpty) {
              onCategorySelected?.call(acgCategories.first.id);
              context.pop();
            }
          },
        ),
      ),
    );
  }
}
