// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'payment_request.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PaymentRequest _$PaymentRequestFromJson(Map<String, dynamic> json) {
  return _PaymentRequest.fromJson(json);
}

/// @nodoc
mixin _$PaymentRequest {
  /// 订单ID
  String get orderId => throw _privateConstructorUsedError;

  /// 支付金额（分）
  int get amount => throw _privateConstructorUsedError;

  /// 支付方式
  PaymentMethod get paymentMethod => throw _privateConstructorUsedError;

  /// 支付标题
  String get subject => throw _privateConstructorUsedError;

  /// 支付描述
  String? get body => throw _privateConstructorUsedError;

  /// 回调地址
  String? get notifyUrl => throw _privateConstructorUsedError;

  /// 返回地址
  String? get returnUrl => throw _privateConstructorUsedError;

  /// 超时时间（分钟）
  int get timeoutMinutes => throw _privateConstructorUsedError;

  /// 是否沙盒环境
  bool get isSandbox => throw _privateConstructorUsedError;

  /// 扩展参数
  Map<String, dynamic>? get extraParams => throw _privateConstructorUsedError;

  /// Serializes this PaymentRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PaymentRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PaymentRequestCopyWith<PaymentRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentRequestCopyWith<$Res> {
  factory $PaymentRequestCopyWith(
          PaymentRequest value, $Res Function(PaymentRequest) then) =
      _$PaymentRequestCopyWithImpl<$Res, PaymentRequest>;
  @useResult
  $Res call(
      {String orderId,
      int amount,
      PaymentMethod paymentMethod,
      String subject,
      String? body,
      String? notifyUrl,
      String? returnUrl,
      int timeoutMinutes,
      bool isSandbox,
      Map<String, dynamic>? extraParams});
}

/// @nodoc
class _$PaymentRequestCopyWithImpl<$Res, $Val extends PaymentRequest>
    implements $PaymentRequestCopyWith<$Res> {
  _$PaymentRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderId = null,
    Object? amount = null,
    Object? paymentMethod = null,
    Object? subject = null,
    Object? body = freezed,
    Object? notifyUrl = freezed,
    Object? returnUrl = freezed,
    Object? timeoutMinutes = null,
    Object? isSandbox = null,
    Object? extraParams = freezed,
  }) {
    return _then(_value.copyWith(
      orderId: null == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int,
      paymentMethod: null == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as PaymentMethod,
      subject: null == subject
          ? _value.subject
          : subject // ignore: cast_nullable_to_non_nullable
              as String,
      body: freezed == body
          ? _value.body
          : body // ignore: cast_nullable_to_non_nullable
              as String?,
      notifyUrl: freezed == notifyUrl
          ? _value.notifyUrl
          : notifyUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      returnUrl: freezed == returnUrl
          ? _value.returnUrl
          : returnUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      timeoutMinutes: null == timeoutMinutes
          ? _value.timeoutMinutes
          : timeoutMinutes // ignore: cast_nullable_to_non_nullable
              as int,
      isSandbox: null == isSandbox
          ? _value.isSandbox
          : isSandbox // ignore: cast_nullable_to_non_nullable
              as bool,
      extraParams: freezed == extraParams
          ? _value.extraParams
          : extraParams // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PaymentRequestImplCopyWith<$Res>
    implements $PaymentRequestCopyWith<$Res> {
  factory _$$PaymentRequestImplCopyWith(_$PaymentRequestImpl value,
          $Res Function(_$PaymentRequestImpl) then) =
      __$$PaymentRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String orderId,
      int amount,
      PaymentMethod paymentMethod,
      String subject,
      String? body,
      String? notifyUrl,
      String? returnUrl,
      int timeoutMinutes,
      bool isSandbox,
      Map<String, dynamic>? extraParams});
}

/// @nodoc
class __$$PaymentRequestImplCopyWithImpl<$Res>
    extends _$PaymentRequestCopyWithImpl<$Res, _$PaymentRequestImpl>
    implements _$$PaymentRequestImplCopyWith<$Res> {
  __$$PaymentRequestImplCopyWithImpl(
      _$PaymentRequestImpl _value, $Res Function(_$PaymentRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? orderId = null,
    Object? amount = null,
    Object? paymentMethod = null,
    Object? subject = null,
    Object? body = freezed,
    Object? notifyUrl = freezed,
    Object? returnUrl = freezed,
    Object? timeoutMinutes = null,
    Object? isSandbox = null,
    Object? extraParams = freezed,
  }) {
    return _then(_$PaymentRequestImpl(
      orderId: null == orderId
          ? _value.orderId
          : orderId // ignore: cast_nullable_to_non_nullable
              as String,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int,
      paymentMethod: null == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as PaymentMethod,
      subject: null == subject
          ? _value.subject
          : subject // ignore: cast_nullable_to_non_nullable
              as String,
      body: freezed == body
          ? _value.body
          : body // ignore: cast_nullable_to_non_nullable
              as String?,
      notifyUrl: freezed == notifyUrl
          ? _value.notifyUrl
          : notifyUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      returnUrl: freezed == returnUrl
          ? _value.returnUrl
          : returnUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      timeoutMinutes: null == timeoutMinutes
          ? _value.timeoutMinutes
          : timeoutMinutes // ignore: cast_nullable_to_non_nullable
              as int,
      isSandbox: null == isSandbox
          ? _value.isSandbox
          : isSandbox // ignore: cast_nullable_to_non_nullable
              as bool,
      extraParams: freezed == extraParams
          ? _value._extraParams
          : extraParams // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PaymentRequestImpl implements _PaymentRequest {
  const _$PaymentRequestImpl(
      {required this.orderId,
      required this.amount,
      required this.paymentMethod,
      required this.subject,
      this.body,
      this.notifyUrl,
      this.returnUrl,
      this.timeoutMinutes = 30,
      this.isSandbox = true,
      final Map<String, dynamic>? extraParams})
      : _extraParams = extraParams;

  factory _$PaymentRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$PaymentRequestImplFromJson(json);

  /// 订单ID
  @override
  final String orderId;

  /// 支付金额（分）
  @override
  final int amount;

  /// 支付方式
  @override
  final PaymentMethod paymentMethod;

  /// 支付标题
  @override
  final String subject;

  /// 支付描述
  @override
  final String? body;

  /// 回调地址
  @override
  final String? notifyUrl;

  /// 返回地址
  @override
  final String? returnUrl;

  /// 超时时间（分钟）
  @override
  @JsonKey()
  final int timeoutMinutes;

  /// 是否沙盒环境
  @override
  @JsonKey()
  final bool isSandbox;

  /// 扩展参数
  final Map<String, dynamic>? _extraParams;

  /// 扩展参数
  @override
  Map<String, dynamic>? get extraParams {
    final value = _extraParams;
    if (value == null) return null;
    if (_extraParams is EqualUnmodifiableMapView) return _extraParams;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'PaymentRequest(orderId: $orderId, amount: $amount, paymentMethod: $paymentMethod, subject: $subject, body: $body, notifyUrl: $notifyUrl, returnUrl: $returnUrl, timeoutMinutes: $timeoutMinutes, isSandbox: $isSandbox, extraParams: $extraParams)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentRequestImpl &&
            (identical(other.orderId, orderId) || other.orderId == orderId) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.paymentMethod, paymentMethod) ||
                other.paymentMethod == paymentMethod) &&
            (identical(other.subject, subject) || other.subject == subject) &&
            (identical(other.body, body) || other.body == body) &&
            (identical(other.notifyUrl, notifyUrl) ||
                other.notifyUrl == notifyUrl) &&
            (identical(other.returnUrl, returnUrl) ||
                other.returnUrl == returnUrl) &&
            (identical(other.timeoutMinutes, timeoutMinutes) ||
                other.timeoutMinutes == timeoutMinutes) &&
            (identical(other.isSandbox, isSandbox) ||
                other.isSandbox == isSandbox) &&
            const DeepCollectionEquality()
                .equals(other._extraParams, _extraParams));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      orderId,
      amount,
      paymentMethod,
      subject,
      body,
      notifyUrl,
      returnUrl,
      timeoutMinutes,
      isSandbox,
      const DeepCollectionEquality().hash(_extraParams));

  /// Create a copy of PaymentRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentRequestImplCopyWith<_$PaymentRequestImpl> get copyWith =>
      __$$PaymentRequestImplCopyWithImpl<_$PaymentRequestImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PaymentRequestImplToJson(
      this,
    );
  }
}

abstract class _PaymentRequest implements PaymentRequest {
  const factory _PaymentRequest(
      {required final String orderId,
      required final int amount,
      required final PaymentMethod paymentMethod,
      required final String subject,
      final String? body,
      final String? notifyUrl,
      final String? returnUrl,
      final int timeoutMinutes,
      final bool isSandbox,
      final Map<String, dynamic>? extraParams}) = _$PaymentRequestImpl;

  factory _PaymentRequest.fromJson(Map<String, dynamic> json) =
      _$PaymentRequestImpl.fromJson;

  /// 订单ID
  @override
  String get orderId;

  /// 支付金额（分）
  @override
  int get amount;

  /// 支付方式
  @override
  PaymentMethod get paymentMethod;

  /// 支付标题
  @override
  String get subject;

  /// 支付描述
  @override
  String? get body;

  /// 回调地址
  @override
  String? get notifyUrl;

  /// 返回地址
  @override
  String? get returnUrl;

  /// 超时时间（分钟）
  @override
  int get timeoutMinutes;

  /// 是否沙盒环境
  @override
  bool get isSandbox;

  /// 扩展参数
  @override
  Map<String, dynamic>? get extraParams;

  /// Create a copy of PaymentRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentRequestImplCopyWith<_$PaymentRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PaymentResponse _$PaymentResponseFromJson(Map<String, dynamic> json) {
  return _PaymentResponse.fromJson(json);
}

/// @nodoc
mixin _$PaymentResponse {
  /// 支付订单号
  String get paymentOrderNo => throw _privateConstructorUsedError;

  /// 支付方式
  PaymentMethod get paymentMethod => throw _privateConstructorUsedError;

  /// 支付状态
  PaymentStatus get status => throw _privateConstructorUsedError;

  /// 支付金额（分）
  int get amount => throw _privateConstructorUsedError;

  /// 第三方支付订单号
  String? get thirdPartyOrderNo => throw _privateConstructorUsedError;

  /// 支付时间
  DateTime? get payTime => throw _privateConstructorUsedError;

  /// 支付参数（用于调起支付）
  Map<String, dynamic>? get paymentParams => throw _privateConstructorUsedError;

  /// 错误信息
  String? get errorMessage => throw _privateConstructorUsedError;

  /// 扩展信息
  Map<String, dynamic>? get extraInfo => throw _privateConstructorUsedError;

  /// Serializes this PaymentResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PaymentResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PaymentResponseCopyWith<PaymentResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaymentResponseCopyWith<$Res> {
  factory $PaymentResponseCopyWith(
          PaymentResponse value, $Res Function(PaymentResponse) then) =
      _$PaymentResponseCopyWithImpl<$Res, PaymentResponse>;
  @useResult
  $Res call(
      {String paymentOrderNo,
      PaymentMethod paymentMethod,
      PaymentStatus status,
      int amount,
      String? thirdPartyOrderNo,
      DateTime? payTime,
      Map<String, dynamic>? paymentParams,
      String? errorMessage,
      Map<String, dynamic>? extraInfo});
}

/// @nodoc
class _$PaymentResponseCopyWithImpl<$Res, $Val extends PaymentResponse>
    implements $PaymentResponseCopyWith<$Res> {
  _$PaymentResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PaymentResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? paymentOrderNo = null,
    Object? paymentMethod = null,
    Object? status = null,
    Object? amount = null,
    Object? thirdPartyOrderNo = freezed,
    Object? payTime = freezed,
    Object? paymentParams = freezed,
    Object? errorMessage = freezed,
    Object? extraInfo = freezed,
  }) {
    return _then(_value.copyWith(
      paymentOrderNo: null == paymentOrderNo
          ? _value.paymentOrderNo
          : paymentOrderNo // ignore: cast_nullable_to_non_nullable
              as String,
      paymentMethod: null == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as PaymentMethod,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as PaymentStatus,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int,
      thirdPartyOrderNo: freezed == thirdPartyOrderNo
          ? _value.thirdPartyOrderNo
          : thirdPartyOrderNo // ignore: cast_nullable_to_non_nullable
              as String?,
      payTime: freezed == payTime
          ? _value.payTime
          : payTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      paymentParams: freezed == paymentParams
          ? _value.paymentParams
          : paymentParams // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      extraInfo: freezed == extraInfo
          ? _value.extraInfo
          : extraInfo // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PaymentResponseImplCopyWith<$Res>
    implements $PaymentResponseCopyWith<$Res> {
  factory _$$PaymentResponseImplCopyWith(_$PaymentResponseImpl value,
          $Res Function(_$PaymentResponseImpl) then) =
      __$$PaymentResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String paymentOrderNo,
      PaymentMethod paymentMethod,
      PaymentStatus status,
      int amount,
      String? thirdPartyOrderNo,
      DateTime? payTime,
      Map<String, dynamic>? paymentParams,
      String? errorMessage,
      Map<String, dynamic>? extraInfo});
}

/// @nodoc
class __$$PaymentResponseImplCopyWithImpl<$Res>
    extends _$PaymentResponseCopyWithImpl<$Res, _$PaymentResponseImpl>
    implements _$$PaymentResponseImplCopyWith<$Res> {
  __$$PaymentResponseImplCopyWithImpl(
      _$PaymentResponseImpl _value, $Res Function(_$PaymentResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of PaymentResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? paymentOrderNo = null,
    Object? paymentMethod = null,
    Object? status = null,
    Object? amount = null,
    Object? thirdPartyOrderNo = freezed,
    Object? payTime = freezed,
    Object? paymentParams = freezed,
    Object? errorMessage = freezed,
    Object? extraInfo = freezed,
  }) {
    return _then(_$PaymentResponseImpl(
      paymentOrderNo: null == paymentOrderNo
          ? _value.paymentOrderNo
          : paymentOrderNo // ignore: cast_nullable_to_non_nullable
              as String,
      paymentMethod: null == paymentMethod
          ? _value.paymentMethod
          : paymentMethod // ignore: cast_nullable_to_non_nullable
              as PaymentMethod,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as PaymentStatus,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as int,
      thirdPartyOrderNo: freezed == thirdPartyOrderNo
          ? _value.thirdPartyOrderNo
          : thirdPartyOrderNo // ignore: cast_nullable_to_non_nullable
              as String?,
      payTime: freezed == payTime
          ? _value.payTime
          : payTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      paymentParams: freezed == paymentParams
          ? _value._paymentParams
          : paymentParams // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      extraInfo: freezed == extraInfo
          ? _value._extraInfo
          : extraInfo // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PaymentResponseImpl implements _PaymentResponse {
  const _$PaymentResponseImpl(
      {required this.paymentOrderNo,
      required this.paymentMethod,
      required this.status,
      required this.amount,
      this.thirdPartyOrderNo,
      this.payTime,
      final Map<String, dynamic>? paymentParams,
      this.errorMessage,
      final Map<String, dynamic>? extraInfo})
      : _paymentParams = paymentParams,
        _extraInfo = extraInfo;

  factory _$PaymentResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$PaymentResponseImplFromJson(json);

  /// 支付订单号
  @override
  final String paymentOrderNo;

  /// 支付方式
  @override
  final PaymentMethod paymentMethod;

  /// 支付状态
  @override
  final PaymentStatus status;

  /// 支付金额（分）
  @override
  final int amount;

  /// 第三方支付订单号
  @override
  final String? thirdPartyOrderNo;

  /// 支付时间
  @override
  final DateTime? payTime;

  /// 支付参数（用于调起支付）
  final Map<String, dynamic>? _paymentParams;

  /// 支付参数（用于调起支付）
  @override
  Map<String, dynamic>? get paymentParams {
    final value = _paymentParams;
    if (value == null) return null;
    if (_paymentParams is EqualUnmodifiableMapView) return _paymentParams;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  /// 错误信息
  @override
  final String? errorMessage;

  /// 扩展信息
  final Map<String, dynamic>? _extraInfo;

  /// 扩展信息
  @override
  Map<String, dynamic>? get extraInfo {
    final value = _extraInfo;
    if (value == null) return null;
    if (_extraInfo is EqualUnmodifiableMapView) return _extraInfo;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'PaymentResponse(paymentOrderNo: $paymentOrderNo, paymentMethod: $paymentMethod, status: $status, amount: $amount, thirdPartyOrderNo: $thirdPartyOrderNo, payTime: $payTime, paymentParams: $paymentParams, errorMessage: $errorMessage, extraInfo: $extraInfo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaymentResponseImpl &&
            (identical(other.paymentOrderNo, paymentOrderNo) ||
                other.paymentOrderNo == paymentOrderNo) &&
            (identical(other.paymentMethod, paymentMethod) ||
                other.paymentMethod == paymentMethod) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.thirdPartyOrderNo, thirdPartyOrderNo) ||
                other.thirdPartyOrderNo == thirdPartyOrderNo) &&
            (identical(other.payTime, payTime) || other.payTime == payTime) &&
            const DeepCollectionEquality()
                .equals(other._paymentParams, _paymentParams) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            const DeepCollectionEquality()
                .equals(other._extraInfo, _extraInfo));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      paymentOrderNo,
      paymentMethod,
      status,
      amount,
      thirdPartyOrderNo,
      payTime,
      const DeepCollectionEquality().hash(_paymentParams),
      errorMessage,
      const DeepCollectionEquality().hash(_extraInfo));

  /// Create a copy of PaymentResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PaymentResponseImplCopyWith<_$PaymentResponseImpl> get copyWith =>
      __$$PaymentResponseImplCopyWithImpl<_$PaymentResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PaymentResponseImplToJson(
      this,
    );
  }
}

abstract class _PaymentResponse implements PaymentResponse {
  const factory _PaymentResponse(
      {required final String paymentOrderNo,
      required final PaymentMethod paymentMethod,
      required final PaymentStatus status,
      required final int amount,
      final String? thirdPartyOrderNo,
      final DateTime? payTime,
      final Map<String, dynamic>? paymentParams,
      final String? errorMessage,
      final Map<String, dynamic>? extraInfo}) = _$PaymentResponseImpl;

  factory _PaymentResponse.fromJson(Map<String, dynamic> json) =
      _$PaymentResponseImpl.fromJson;

  /// 支付订单号
  @override
  String get paymentOrderNo;

  /// 支付方式
  @override
  PaymentMethod get paymentMethod;

  /// 支付状态
  @override
  PaymentStatus get status;

  /// 支付金额（分）
  @override
  int get amount;

  /// 第三方支付订单号
  @override
  String? get thirdPartyOrderNo;

  /// 支付时间
  @override
  DateTime? get payTime;

  /// 支付参数（用于调起支付）
  @override
  Map<String, dynamic>? get paymentParams;

  /// 错误信息
  @override
  String? get errorMessage;

  /// 扩展信息
  @override
  Map<String, dynamic>? get extraInfo;

  /// Create a copy of PaymentResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PaymentResponseImplCopyWith<_$PaymentResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
