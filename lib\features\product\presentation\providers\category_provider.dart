import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/features/product/data/datasources/category_api_service.dart';
import 'package:soko/features/product/domain/entities/category.dart';

/// 分类树状态
class CategoryTreeState {

  const CategoryTreeState({
    this.categories = const [],
    this.isLoading = false,
    this.error,
  });
  final List<ProductCategory> categories;
  final bool isLoading;
  final String? error;

  CategoryTreeState copyWith({
    List<ProductCategory>? categories,
    bool? isLoading,
    String? error,
  }) {
    return CategoryTreeState(
      categories: categories ?? this.categories,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// 分类选择状态
class CategorySelectionState {

  const CategorySelectionState({
    this.selectedCategory,
    this.categoryPath = const [],
    this.selectedAcgType,
  });
  final ProductCategory? selectedCategory;
  final List<ProductCategory> categoryPath;
  final String? selectedAcgType;

  CategorySelectionState copyWith({
    ProductCategory? selectedCategory,
    List<ProductCategory>? categoryPath,
    String? selectedAcgType,
  }) {
    return CategorySelectionState(
      selectedCategory: selectedCategory ?? this.selectedCategory,
      categoryPath: categoryPath ?? this.categoryPath,
      selectedAcgType: selectedAcgType ?? this.selectedAcgType,
    );
  }

  /// 清除选择
  CategorySelectionState clear() {
    return const CategorySelectionState();
  }
}

/// 分类树状态管理器
class CategoryTreeNotifier extends StateNotifier<CategoryTreeState> {

  CategoryTreeNotifier() : super(const CategoryTreeState()) {
    loadCategoryTree();
  }
  final CategoryApiService _categoryApiService = MockCategoryApiService();

  /// 加载分类树
  Future<void> loadCategoryTree() async {
    state = state.copyWith(isLoading: true);

    try {
      final response = await _categoryApiService.getCategoryTree();

      if (response.isSuccess && response.data != null) {
        state = state.copyWith(
          categories: response.data,
          isLoading: false,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? '加载失败',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 刷新分类树
  Future<void> refresh() async {
    await loadCategoryTree();
  }

  /// 根据ID查找分类
  ProductCategory? findCategoryById(String id) {
    return _findCategoryInList(state.categories, id);
  }

  /// 在分类列表中查找分类
  ProductCategory? _findCategoryInList(
      List<ProductCategory> categories, String id,) {
    for (final category in categories) {
      if (category.id == id) return category;

      if (category.hasChildren) {
        final found = _findCategoryInList(category.children!, id);
        if (found != null) return found;
      }
    }
    return null;
  }

  /// 获取所有叶子分类
  List<ProductCategory> getLeafCategories() {
    final leafCategories = <ProductCategory>[];
    _collectLeafCategories(state.categories, leafCategories);
    return leafCategories;
  }

  /// 收集叶子分类
  void _collectLeafCategories(
      List<ProductCategory> categories, List<ProductCategory> result,) {
    for (final category in categories) {
      if (!category.hasChildren) {
        result.add(category);
      } else {
        _collectLeafCategories(category.children!, result);
      }
    }
  }

  /// 获取ACG分类
  List<ProductCategory> getAcgCategories() {
    return state.categories.where((cat) => cat.isAcgCategory).toList();
  }
}

/// 分类选择状态管理器
class CategorySelectionNotifier extends StateNotifier<CategorySelectionState> {
  CategorySelectionNotifier() : super(const CategorySelectionState());

  /// 选择分类
  void selectCategory(
      ProductCategory category, List<ProductCategory> allCategories,) {
    final categoryPath = category.getCategoryPath(allCategories);

    state = state.copyWith(
      selectedCategory: category,
      categoryPath: categoryPath,
      selectedAcgType: category.acgType,
    );
  }

  /// 选择ACG类型
  void selectAcgType(String acgType) {
    state = state.copyWith(
      selectedAcgType: acgType,
      categoryPath: [],
    );
  }

  /// 清除选择
  void clearSelection() {
    state = state.clear();
  }

  /// 是否选择了分类
  bool get hasSelection {
    return state.selectedCategory != null || state.selectedAcgType != null;
  }

  /// 获取选择的分类ID
  String? get selectedCategoryId {
    return state.selectedCategory?.id;
  }

  /// 获取选择的ACG类型
  String? get selectedAcgType {
    return state.selectedAcgType;
  }
}

/// 热门分类状态
class HotCategoriesState {

  const HotCategoriesState({
    this.categories = const [],
    this.isLoading = false,
    this.error,
  });
  final List<ProductCategory> categories;
  final bool isLoading;
  final String? error;

  HotCategoriesState copyWith({
    List<ProductCategory>? categories,
    bool? isLoading,
    String? error,
  }) {
    return HotCategoriesState(
      categories: categories ?? this.categories,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// 热门分类状态管理器
class HotCategoriesNotifier extends StateNotifier<HotCategoriesState> {

  HotCategoriesNotifier() : super(const HotCategoriesState()) {
    loadHotCategories();
  }
  final CategoryApiService _categoryApiService = MockCategoryApiService();

  /// 加载热门分类
  Future<void> loadHotCategories() async {
    state = state.copyWith(isLoading: true);

    try {
      final response = await _categoryApiService.getHotCategories();

      if (response.isSuccess && response.data != null) {
        state = state.copyWith(
          categories: response.data,
          isLoading: false,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message ?? '加载失败',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 刷新热门分类
  Future<void> refresh() async {
    await loadHotCategories();
  }
}

/// 分类树状态提供者
final categoryTreeProvider =
    StateNotifierProvider<CategoryTreeNotifier, CategoryTreeState>(
  (ref) => CategoryTreeNotifier(),
);

/// 分类选择状态提供者
final categorySelectionProvider =
    StateNotifierProvider<CategorySelectionNotifier, CategorySelectionState>(
  (ref) => CategorySelectionNotifier(),
);

/// 热门分类状态提供者
final hotCategoriesProvider =
    StateNotifierProvider<HotCategoriesNotifier, HotCategoriesState>(
  (ref) => HotCategoriesNotifier(),
);

/// ACG分类提供者
final acgCategoriesProvider = Provider<List<ProductCategory>>((ref) {
  final categoryTree = ref.watch(categoryTreeProvider);
  return categoryTree.categories.where((cat) => cat.isAcgCategory).toList();
});

/// 当前选择的分类提供者
final selectedCategoryProvider = Provider<ProductCategory?>((ref) {
  final selection = ref.watch(categorySelectionProvider);
  return selection.selectedCategory;
});

/// 当前选择的ACG类型提供者
final selectedAcgTypeProvider = Provider<String?>((ref) {
  final selection = ref.watch(categorySelectionProvider);
  return selection.selectedAcgType;
});
