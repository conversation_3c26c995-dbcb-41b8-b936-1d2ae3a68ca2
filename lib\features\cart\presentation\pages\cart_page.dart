import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import 'package:soko/core/theme/app_colors.dart';
import 'package:soko/core/theme/app_text_styles.dart';
import 'package:soko/shared/presentation/widgets/custom_app_bar.dart';
import 'package:soko/shared/presentation/widgets/loading_widget.dart';
import 'package:soko/shared/presentation/widgets/error_widget.dart';
import 'package:soko/shared/presentation/widgets/empty_widget.dart';
import 'package:soko/features/cart/presentation/providers/cart_provider.dart';
import 'package:soko/features/cart/presentation/widgets/cart_item_card.dart';
import 'package:soko/features/cart/presentation/widgets/cart_summary_bar.dart';

/// 购物车页面
class CartPage extends ConsumerStatefulWidget {
  const CartPage({super.key});

  @override
  ConsumerState<CartPage> createState() => _CartPageState();
}

class _CartPageState extends ConsumerState<CartPage> {
  bool _isEditMode = false;

  @override
  void initState() {
    super.initState();

    // 加载购物车数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(cartProvider.notifier).loadCart();
    });
  }

  @override
  Widget build(BuildContext context) {
    final cartState = ref.watch(cartProvider);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CustomAppBar(
        title: '购物车',
        showBackButton: false,
        actions: [
          if (cartState.items.isNotEmpty)
            TextButton(
              onPressed: () {
                setState(() {
                  _isEditMode = !_isEditMode;
                });
              },
              child: Text(
                _isEditMode ? '完成' : '编辑',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body: _buildBody(cartState),
      bottomNavigationBar: cartState.items.isNotEmpty
          ? CartSummaryBar(
              isEditMode: _isEditMode,
              onCheckout: _handleCheckout,
              onDeleteSelected: _handleDeleteSelected,
            )
          : null,
    );
  }

  /// 构建主体内容
  Widget _buildBody(CartState cartState) {
    if (cartState.isLoading && cartState.items.isEmpty) {
      return const LoadingWidget();
    }

    if (cartState.error != null && cartState.items.isEmpty) {
      return ErrorDisplayWidget(
        message: cartState.error!,
        onRetry: () => ref.read(cartProvider.notifier).refresh(),
      );
    }

    if (cartState.items.isEmpty) {
      return const EmptyWidget(
        message: '购物车是空的',
        description: '快去挑选你喜欢的商品吧',
        icon: Icons.shopping_cart_outlined,
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(cartProvider.notifier).refresh();
      },
      child: Column(
        children: [
          // 全选栏
          _buildSelectAllBar(cartState),
          // 购物车列表
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.symmetric(vertical: 8.h),
              itemCount: cartState.items.length,
              itemBuilder: (context, index) {
                final item = cartState.items[index];
                return CartItemCard(
                  item: item,
                  isEditMode: _isEditMode,
                  onTap: () => _navigateToProductDetail(item.productId),
                  onQuantityChanged: (quantity) =>
                      _updateQuantity(item.id, quantity),
                  onSelectionChanged: () => _toggleSelection(item.id),
                  onRemove: () => _removeItem(item.id),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建全选栏
  Widget _buildSelectAllBar(CartState cartState) {
    final availableItems =
        cartState.items.where((item) => item.available).toList();
    final isAllSelected = availableItems.isNotEmpty &&
        availableItems.every((item) => item.selected);

    if (availableItems.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      color: Colors.white,
      child: Row(
        children: [
          // 全选复选框
          Checkbox(
            value: isAllSelected,
            onChanged: (_) => _toggleSelectAll(),
            activeColor: AppColors.primary,
          ),
          SizedBox(width: 8.w),
          // 全选文本
          Text(
            '全选',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const Spacer(),
          // 商品数量统计
          Text(
            '共${availableItems.length}件商品',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  /// 切换全选状态
  void _toggleSelectAll() {
    ref.read(cartProvider.notifier).toggleSelectAll();
  }

  /// 切换商品选择状态
  void _toggleSelection(String itemId) {
    ref.read(cartProvider.notifier).toggleItemSelection(itemId);
  }

  /// 更新商品数量
  void _updateQuantity(String itemId, int quantity) {
    ref.read(cartProvider.notifier).updateQuantity(itemId, quantity);
  }

  /// 删除商品
  void _removeItem(String itemId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: const Text('确定要删除这件商品吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(cartProvider.notifier).removeItem(itemId);
            },
            child: const Text(
              '删除',
              style: TextStyle(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }

  /// 处理结算
  void _handleCheckout() {
    final selectedItems = ref.read(cartProvider.notifier).selectedItems;

    if (selectedItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请先选择要结算的商品')),
      );
      return;
    }

    context.push('/checkout');
  }

  /// 删除选中的商品
  void _handleDeleteSelected() {
    final selectedItems = ref.read(cartProvider.notifier).selectedItems;

    if (selectedItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请先选择要删除的商品')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除选中的 ${selectedItems.length} 件商品吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(cartProvider.notifier).removeSelectedItems();
              setState(() {
                _isEditMode = false;
              });
            },
            child: const Text(
              '删除',
              style: TextStyle(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }

  /// 导航到商品详情页
  void _navigateToProductDetail(String productId) {
    context.push('/product/$productId');
  }
}
