import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 用户引导组件
class OnboardingWidget extends StatefulWidget {
  const OnboardingWidget({
    super.key,
    required this.pages,
    this.onCompleted,
    this.showSkipButton = true,
    this.skipText = '跳过',
    this.nextText = '下一步',
    this.doneText = '开始使用',
  });

  final List<OnboardingPage> pages;
  final VoidCallback? onCompleted;
  final bool showSkipButton;
  final String skipText;
  final String nextText;
  final String doneText;

  @override
  State<OnboardingWidget> createState() => _OnboardingWidgetState();
}

class _OnboardingWidgetState extends State<OnboardingWidget> {
  final PageController _pageController = PageController();
  int _currentIndex = 0;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // 跳过按钮
            if (widget.showSkipButton)
              Align(
                alignment: Alignment.topRight,
                child: Padding(
                  padding: EdgeInsets.all(16.w),
                  child: TextButton(
                    onPressed: widget.onCompleted,
                    child: Text(widget.skipText),
                  ),
                ),
              ),
            
            // 页面内容
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentIndex = index;
                  });
                },
                itemCount: widget.pages.length,
                itemBuilder: (context, index) {
                  return _buildPage(widget.pages[index]);
                },
              ),
            ),
            
            // 底部控制
            _buildBottomControls(),
          ],
        ),
      ),
    );
  }

  /// 构建页面
  Widget _buildPage(OnboardingPage page) {
    return Padding(
      padding: EdgeInsets.all(32.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 图片或图标
          if (page.image != null)
            Image.asset(
              page.image!,
              width: 200.w,
              height: 200.w,
            )
          else if (page.icon != null)
            Icon(
              page.icon,
              size: 120.w,
              color: Theme.of(context).primaryColor,
            ),
          
          SizedBox(height: 40.h),
          
          // 标题
          Text(
            page.title,
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          
          // 描述
          Text(
            page.description,
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.grey[600],
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 构建底部控制
  Widget _buildBottomControls() {
    return Padding(
      padding: EdgeInsets.all(24.w),
      child: Column(
        children: [
          // 页面指示器
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              widget.pages.length,
              (index) => Container(
                width: 8.w,
                height: 8.w,
                margin: EdgeInsets.symmetric(horizontal: 4.w),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: index == _currentIndex
                      ? Theme.of(context).primaryColor
                      : Colors.grey[300],
                ),
              ),
            ),
          ),
          SizedBox(height: 24.h),
          
          // 按钮
          SizedBox(
            width: double.infinity,
            height: 48.h,
            child: ElevatedButton(
              onPressed: _handleNext,
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
              child: Text(
                _isLastPage ? widget.doneText : widget.nextText,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 是否为最后一页
  bool get _isLastPage => _currentIndex == widget.pages.length - 1;

  /// 处理下一步
  void _handleNext() {
    if (_isLastPage) {
      widget.onCompleted?.call();
    } else {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }
}

/// 功能引导组件
class FeatureGuideWidget extends StatefulWidget {
  const FeatureGuideWidget({
    super.key,
    required this.child,
    required this.guideKey,
    required this.title,
    required this.description,
    this.onGuideCompleted,
  });

  final Widget child;
  final GlobalKey guideKey;
  final String title;
  final String description;
  final VoidCallback? onGuideCompleted;

  @override
  State<FeatureGuideWidget> createState() => _FeatureGuideWidgetState();
}

class _FeatureGuideWidgetState extends State<FeatureGuideWidget> {
  OverlayEntry? _overlayEntry;

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }

  /// 显示引导
  void showGuide() {
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
  }

  /// 隐藏引导
  void hideGuide() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    widget.onGuideCompleted?.call();
  }

  /// 创建遮罩层
  OverlayEntry _createOverlayEntry() {
    final renderBox = widget.guideKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) {
      throw Exception('Guide target not found');
    }

    final position = renderBox.localToGlobal(Offset.zero);
    final size = renderBox.size;

    return OverlayEntry(
      builder: (context) => Material(
        color: Colors.black.withValues(alpha: 0.8),
        child: Stack(
          children: [
            // 高亮区域
            Positioned(
              left: position.dx - 8.w,
              top: position.dy - 8.h,
              child: Container(
                width: size.width + 16.w,
                height: size.height + 16.h,
                decoration: BoxDecoration(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(
                    color: Colors.white,
                    width: 2,
                  ),
                ),
              ),
            ),
            
            // 说明文字
            Positioned(
              left: 32.w,
              right: 32.w,
              top: position.dy + size.height + 32.h,
              child: Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.title,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      widget.description,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.grey[600],
                      ),
                    ),
                    SizedBox(height: 16.h),
                    Align(
                      alignment: Alignment.centerRight,
                      child: TextButton(
                        onPressed: hideGuide,
                        child: const Text('知道了'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 帮助提示组件
class HelpTooltipWidget extends StatelessWidget {
  const HelpTooltipWidget({
    super.key,
    required this.message,
    required this.child,
    this.preferBelow = true,
  });

  final String message;
  final Widget child;
  final bool preferBelow;

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: message,
      preferBelow: preferBelow,
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(8.r),
      ),
      textStyle: TextStyle(
        fontSize: 12.sp,
        color: Colors.white,
      ),
      child: child,
    );
  }
}

/// 快速操作指南组件
class QuickActionsGuideWidget extends StatelessWidget {
  const QuickActionsGuideWidget({
    super.key,
    required this.actions,
  });

  final List<QuickAction> actions;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '快速操作',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 12.h),
          
          ...actions.map((action) => _buildActionItem(action)),
        ],
      ),
    );
  }

  /// 构建操作项
  Widget _buildActionItem(QuickAction action) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: Row(
        children: [
          Container(
            width: 32.w,
            height: 32.w,
            decoration: BoxDecoration(
              color: action.color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              action.icon,
              size: 16.w,
              color: action.color,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  action.title,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (action.description != null) ...[
                  SizedBox(height: 2.h),
                  Text(
                    action.description!,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ],
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            size: 12.w,
            color: Colors.grey[400],
          ),
        ],
      ),
    );
  }
}

/// 引导页面模型
class OnboardingPage {
  const OnboardingPage({
    required this.title,
    required this.description,
    this.image,
    this.icon,
  });

  final String title;
  final String description;
  final String? image;
  final IconData? icon;
}

/// 快速操作模型
class QuickAction {
  const QuickAction({
    required this.title,
    required this.icon,
    required this.color,
    this.description,
    this.onTap,
  });

  final String title;
  final IconData icon;
  final Color color;
  final String? description;
  final VoidCallback? onTap;
}
