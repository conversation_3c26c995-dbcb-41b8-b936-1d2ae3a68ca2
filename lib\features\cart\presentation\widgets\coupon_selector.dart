import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:soko/core/theme/app_colors.dart';
import 'package:soko/core/theme/app_text_styles.dart';
import 'package:soko/shared/presentation/widgets/loading_widget.dart';
import 'package:soko/features/cart/domain/entities/coupon.dart';
import 'package:soko/features/cart/domain/services/price_calculation_service.dart';

/// 优惠券选择器
class CouponSelector extends ConsumerStatefulWidget {

  const CouponSelector({
    super.key,
    required this.availableCoupons,
    this.selectedCoupon,
    this.onCouponSelected,
    required this.totalAmount,
  });
  final List<Coupon> availableCoupons;
  final Coupon? selectedCoupon;
  final Function(Coupon? coupon)? onCouponSelected;
  final double totalAmount;

  @override
  ConsumerState<CouponSelector> createState() => _CouponSelectorState();
}

class _CouponSelectorState extends ConsumerState<CouponSelector> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                Icons.local_offer_outlined,
                color: AppColors.primary,
                size: 20.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                '优惠券',
                style: AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              if (widget.availableCoupons.isNotEmpty)
                TextButton(
                  onPressed: () => _showCouponList(context),
                  child: Text(
                    '选择优惠券',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.primary,
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: 12.h),
          // 当前选择的优惠券或提示
          _buildCurrentCoupon(),
        ],
      ),
    );
  }

  /// 构建当前优惠券显示
  Widget _buildCurrentCoupon() {
    if (widget.selectedCoupon != null) {
      return _buildCouponCard(widget.selectedCoupon!, isSelected: true);
    }

    if (widget.availableCoupons.isEmpty) {
      return Container(
        padding: EdgeInsets.symmetric(vertical: 12.h),
        child: Text(
          '暂无可用优惠券',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      );
    }

    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.h),
      child: Text(
        '有 ${widget.availableCoupons.length} 张优惠券可用',
        style: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.primary,
        ),
      ),
    );
  }

  /// 显示优惠券列表
  void _showCouponList(BuildContext context) {
    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CouponListBottomSheet(
        availableCoupons: widget.availableCoupons,
        selectedCoupon: widget.selectedCoupon,
        totalAmount: widget.totalAmount,
        onCouponSelected: (coupon) {
          widget.onCouponSelected?.call(coupon);
          Navigator.of(context).pop();
        },
      ),
    );
  }

  /// 构建优惠券卡片
  Widget _buildCouponCard(Coupon coupon, {bool isSelected = false}) {
    final discount = coupon.calculateDiscount(widget.totalAmount);
    
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: isSelected ? AppColors.primary.withOpacity(0.1) : AppColors.background,
        borderRadius: BorderRadius.circular(6.r),
        border: Border.all(
          color: isSelected ? AppColors.primary : AppColors.borderLight,
        ),
      ),
      child: Row(
        children: [
          // 优惠券图标
          Container(
            width: 40.w,
            height: 40.w,
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(6.r),
            ),
            child: Icon(
              Icons.local_offer,
              color: Colors.white,
              size: 20.sp,
            ),
          ),
          SizedBox(width: 12.w),
          // 优惠券信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  coupon.name,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 2.h),
                Text(
                  coupon.conditionText,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          // 优惠金额
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                coupon.displayText,
                style: AppTextStyles.bodyLarge.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (discount > 0) ...[
                SizedBox(height: 2.h),
                Text(
                  '省${PriceCalculationService.formatAmount(discount)}',
                  style: AppTextStyles.caption.copyWith(
                    color: AppColors.success,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }
}

/// 优惠券列表底部弹窗
class CouponListBottomSheet extends StatefulWidget {

  const CouponListBottomSheet({
    super.key,
    required this.availableCoupons,
    this.selectedCoupon,
    required this.totalAmount,
    this.onCouponSelected,
  });
  final List<Coupon> availableCoupons;
  final Coupon? selectedCoupon;
  final double totalAmount;
  final Function(Coupon? coupon)? onCouponSelected;

  @override
  State<CouponListBottomSheet> createState() => _CouponListBottomSheetState();
}

class _CouponListBottomSheetState extends State<CouponListBottomSheet> {
  Coupon? _selectedCoupon;

  @override
  void initState() {
    super.initState();
    _selectedCoupon = widget.selectedCoupon;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.8,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题栏
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(color: AppColors.borderLight),
              ),
            ),
            child: Row(
              children: [
                Text(
                  '选择优惠券',
                  style: AppTextStyles.headlineSmall.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: Icon(Icons.close, size: 20.sp),
                  padding: EdgeInsets.zero,
                  constraints: BoxConstraints(
                    minWidth: 32.w,
                    minHeight: 32.w,
                  ),
                ),
              ],
            ),
          ),
          // 优惠券列表
          Flexible(
            child: ListView.builder(
              shrinkWrap: true,
              padding: EdgeInsets.all(16.w),
              itemCount: widget.availableCoupons.length + 1, // +1 for "不使用优惠券"
              itemBuilder: (context, index) {
                if (index == 0) {
                  // 不使用优惠券选项
                  return _buildNoCouponOption();
                }
                
                final coupon = widget.availableCoupons[index - 1];
                return _buildCouponItem(coupon);
              },
            ),
          ),
          // 确认按钮
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: const BoxDecoration(
              border: Border(
                top: BorderSide(color: AppColors.borderLight),
              ),
            ),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => widget.onCouponSelected?.call(_selectedCoupon),
                child: const Text('确认选择'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建不使用优惠券选项
  Widget _buildNoCouponOption() {
    final isSelected = _selectedCoupon == null;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedCoupon = null;
        });
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 12.h),
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary.withOpacity(0.1) : Colors.white,
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: isSelected ? AppColors.primary : AppColors.borderLight,
          ),
        ),
        child: Row(
          children: [
            Icon(
              isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
              color: isSelected ? AppColors.primary : AppColors.textTertiary,
              size: 20.sp,
            ),
            SizedBox(width: 12.w),
            Text(
              '不使用优惠券',
              style: AppTextStyles.bodyMedium.copyWith(
                color: isSelected ? AppColors.primary : AppColors.textPrimary,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建优惠券项
  Widget _buildCouponItem(Coupon coupon) {
    final isSelected = _selectedCoupon?.id == coupon.id;
    final discount = coupon.calculateDiscount(widget.totalAmount);
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedCoupon = coupon;
        });
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 12.h),
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary.withOpacity(0.1) : Colors.white,
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: isSelected ? AppColors.primary : AppColors.borderLight,
          ),
        ),
        child: Row(
          children: [
            // 选择按钮
            Icon(
              isSelected ? Icons.radio_button_checked : Icons.radio_button_unchecked,
              color: isSelected ? AppColors.primary : AppColors.textTertiary,
              size: 20.sp,
            ),
            SizedBox(width: 12.w),
            // 优惠券信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    coupon.name,
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                      color: isSelected ? AppColors.primary : AppColors.textPrimary,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    coupon.conditionText,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                  if (coupon.description != null) ...[
                    SizedBox(height: 2.h),
                    Text(
                      coupon.description!,
                      style: AppTextStyles.caption.copyWith(
                        color: AppColors.textTertiary,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            // 优惠金额
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  coupon.displayText,
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: isSelected ? AppColors.primary : AppColors.textPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (discount > 0) ...[
                  SizedBox(height: 2.h),
                  Text(
                    '省${PriceCalculationService.formatAmount(discount)}',
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.success,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }
}
