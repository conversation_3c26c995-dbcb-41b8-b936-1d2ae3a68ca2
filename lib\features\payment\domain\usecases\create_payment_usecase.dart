import 'package:dartz/dartz.dart' hide Order;
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/error/failures.dart';
import 'package:soko/core/usecases/usecase.dart';
import 'package:soko/features/payment/data/repositories/payment_repository_impl.dart';
import 'package:soko/features/payment/domain/entities/payment_request.dart';
import 'package:soko/features/payment/domain/repositories/payment_repository.dart';

/// 创建支付用例
class CreatePaymentUseCase implements UseCase<PaymentResponse, PaymentRequest> {

  CreatePaymentUseCase(this.repository);
  final PaymentRepository repository;

  @override
  Future<Either<Failure, PaymentResponse>> call(PaymentRequest request) async {
    // 验证支付请求
    final validationResult = _validatePaymentRequest(request);
    if (validationResult != null) {
      return Left(ValidationFailure(validationResult));
    }

    return repository.createPayment(request);
  }

  /// 验证支付请求
  String? _validatePaymentRequest(PaymentRequest request) {
    // 检查订单ID
    if (request.orderId.isEmpty) {
      return '订单ID不能为空';
    }

    // 检查支付金额
    if (request.amount <= 0) {
      return '支付金额必须大于0';
    }

    // 检查支付标题
    if (request.subject.isEmpty) {
      return '支付标题不能为空';
    }

    return null;
  }
}

/// CreatePaymentUseCase 提供者
final createPaymentUseCaseProvider = Provider<CreatePaymentUseCase>((ref) {
  final repository = ref.read(paymentRepositoryProvider);
  return CreatePaymentUseCase(repository);
});
