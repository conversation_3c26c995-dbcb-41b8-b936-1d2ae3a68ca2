import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/state/base_notifier.dart';
import 'package:soko/core/state/base_state.dart';
import 'package:soko/features/auth/data/datasources/auth_api_service.dart';
import 'package:soko/features/auth/domain/entities/user.dart';

/// 认证状态提供者
final authProvider = StateNotifierProvider<AuthNotifier, BaseState<User>>((ref) {
  return AuthNotifier();
});

/// 登录状态提供者
final loginProvider = StateNotifierProvider<LoginNotifier, AsyncState<LoginResponse>>((ref) {
  return LoginNotifier();
});

/// 注册状态提供者
final registerProvider = StateNotifierProvider<RegisterNotifier, AsyncState<LoginResponse>>((ref) {
  return RegisterNotifier();
});

/// 短信验证码状态提供者
final smsCodeProvider = StateNotifierProvider<SmsCodeNotifier, AsyncState<void>>((ref) {
  return SmsCodeNotifier();
});

/// 认证状态管理器
class AuthNotifier extends BaseNotifier<User> {
  final AuthApiService _authApiService = AuthApiService();

  /// 获取用户信息
  Future<void> getUserInfo() async {
    await execute(() async {
      final response = await _authApiService.getUserInfo();
      if (response.isSuccess && response.data != null) {
        return response.data!;
      }
      throw Exception(response.message);
    });
  }

  /// 更新用户信息
  Future<void> updateUserInfo({
    String? nickname,
    String? avatar,
    String? gender,
    String? birthday,
    String? realName,
    String? idCard,
  }) async {
    await execute(() async {
      final response = await _authApiService.updateUserInfo(
        nickname: nickname,
        avatar: avatar,
        gender: gender,
        birthday: birthday,
        realName: realName,
        idCard: idCard,
      );
      if (response.isSuccess && response.data != null) {
        return response.data!;
      }
      throw Exception(response.message);
    });
  }

  /// 登出
  Future<void> logout() async {
    await execute(() async {
      final response = await _authApiService.logout();
      if (response.isSuccess) {
        // 清除本地存储的用户信息
        // 这里可以调用存储服务清除数据
        throw Exception('登出成功'); // 抛出异常来触发状态重置
      }
      throw Exception(response.message);
    });
  }
}

/// 登录状态管理器
class LoginNotifier extends AsyncNotifier<LoginResponse> {
  final AuthApiService _authApiService = AuthApiService();

  /// 密码登录
  Future<void> loginWithPassword({
    required String phone,
    required String password,
  }) async {
    await execute(() async {
      final response = await _authApiService.loginWithPassword(
        phone: phone,
        password: password,
      );
      if (response.isSuccess && response.data != null) {
        return response.data!;
      }
      throw Exception(response.message);
    });
  }

  /// 验证码登录
  Future<void> loginWithSms({
    required String phone,
    required String smsCode,
  }) async {
    await execute(() async {
      final response = await _authApiService.loginWithSms(
        phone: phone,
        smsCode: smsCode,
      );
      if (response.isSuccess && response.data != null) {
        return response.data!;
      }
      throw Exception(response.message);
    });
  }
}

/// 注册状态管理器
class RegisterNotifier extends AsyncNotifier<LoginResponse> {
  final AuthApiService _authApiService = AuthApiService();

  /// 注册
  Future<void> register({
    required String phone,
    required String password,
    required String smsCode,
    String? nickname,
    String? inviteCode,
  }) async {
    await execute(() async {
      final response = await _authApiService.register(
        phone: phone,
        password: password,
        smsCode: smsCode,
        nickname: nickname,
        inviteCode: inviteCode,
      );
      if (response.isSuccess && response.data != null) {
        return response.data!;
      }
      throw Exception(response.message);
    });
  }
}

/// 短信验证码状态管理器
class SmsCodeNotifier extends AsyncNotifier<void> {
  final AuthApiService _authApiService = AuthApiService();

  /// 发送短信验证码
  Future<void> sendSmsCode({
    required String phone,
    required String type,
  }) async {
    await execute(() async {
      final response = await _authApiService.sendSmsCode(
        phone: phone,
        type: type,
      );
      if (response.isSuccess) {
        return;
      }
      throw Exception(response.message);
    });
  }

  /// 验证短信验证码
  Future<void> verifySmsCode({
    required String phone,
    required String smsCode,
    required String type,
  }) async {
    await execute(() async {
      final response = await _authApiService.verifySmsCode(
        phone: phone,
        smsCode: smsCode,
        type: type,
      );
      if (response.isSuccess) {
        return;
      }
      throw Exception(response.message);
    });
  }
}

/// 密码管理状态提供者
final passwordProvider = StateNotifierProvider<PasswordNotifier, AsyncState<void>>((ref) {
  return PasswordNotifier();
});

/// 密码管理状态管理器
class PasswordNotifier extends AsyncNotifier<void> {
  final AuthApiService _authApiService = AuthApiService();

  /// 重置密码
  Future<void> resetPassword({
    required String phone,
    required String newPassword,
    required String smsCode,
  }) async {
    await execute(() async {
      final response = await _authApiService.resetPassword(
        phone: phone,
        newPassword: newPassword,
        smsCode: smsCode,
      );
      if (response.isSuccess) {
        return;
      }
      throw Exception(response.message);
    });
  }

  /// 修改密码
  Future<void> changePassword({
    required String oldPassword,
    required String newPassword,
  }) async {
    await execute(() async {
      final response = await _authApiService.changePassword(
        oldPassword: oldPassword,
        newPassword: newPassword,
      );
      if (response.isSuccess) {
        return;
      }
      throw Exception(response.message);
    });
  }
}

/// 短信倒计时状态提供者
final smsCountdownProvider = StateNotifierProvider<SmsCountdownNotifier, int>((ref) {
  return SmsCountdownNotifier();
});

/// 短信倒计时状态管理器
class SmsCountdownNotifier extends StateNotifier<int> {
  SmsCountdownNotifier() : super(0);

  /// 开始倒计时
  void startCountdown({int seconds = 60}) {
    state = seconds;
    _countdown();
  }

  void _countdown() {
    if (state > 0) {
      Future.delayed(const Duration(seconds: 1), () {
        if (mounted) {
          state = state - 1;
          _countdown();
        }
      });
    }
  }

  /// 重置倒计时
  void reset() {
    state = 0;
  }

  /// 是否可以发送验证码
  bool get canSend => state == 0;
}
