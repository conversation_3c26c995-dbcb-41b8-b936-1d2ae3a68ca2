import 'package:soko/features/product/domain/entities/product_models.dart';
import 'package:soko/core/models/paginated_data.dart';

/// 商品业务仓库接口
abstract class ProductRepository {
  /// 搜索商品
  Future<PaginatedData<Product>> searchProducts(ProductSearchRequest request);

  /// 获取商品详情
  Future<Product> getProductDetail(String productId);

  /// 获取商品分类列表
  Future<List<ProductCategory>> getCategories();

  /// 获取分类下的商品
  Future<PaginatedData<Product>> getProductsByCategory(
    String categoryId, {
    int page = 1,
    int pageSize = 20,
    String? sortBy,
    String? sortOrder,
  });

  /// 获取品牌下的商品
  Future<PaginatedData<Product>> getProductsByBrand(
    String brandId, {
    int page = 1,
    int pageSize = 20,
    String? sortBy,
    String? sortOrder,
  });

  /// 获取推荐商品
  Future<List<Product>> getRecommendedProducts({
    String? categoryId,
    String? userId,
    int limit = 10,
  });

  /// 获取热门商品
  Future<List<Product>> getPopularProducts({
    String? categoryId,
    int limit = 10,
  });

  /// 获取最新商品
  Future<List<Product>> getLatestProducts({
    String? categoryId,
    int limit = 10,
  });

  /// 获取相似商品
  Future<List<Product>> getSimilarProducts(
    String productId, {
    int limit = 10,
  });

  /// 添加到收藏
  Future<void> addToFavorites(String productId);

  /// 从收藏中移除
  Future<void> removeFromFavorites(String productId);

  /// 获取收藏列表
  Future<PaginatedData<Product>> getFavoriteProducts({
    int page = 1,
    int pageSize = 20,
  });

  /// 检查是否已收藏
  Future<bool> isFavorite(String productId);

  /// 增加浏览次数
  Future<void> incrementViewCount(String productId);

  /// 获取浏览历史
  Future<List<Product>> getViewHistory({
    int limit = 20,
  });

  /// 获取搜索建议
  Future<List<String>> getSearchSuggestions(String keyword);

  /// 获取热门搜索关键词
  Future<List<String>> getHotSearchKeywords({
    int limit = 10,
  });
}
