import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/error/failures.dart';
import 'package:soko/core/usecases/usecase.dart';
import 'package:soko/features/order/data/repositories/order_repository_impl.dart';
import 'package:soko/features/order/domain/entities/order.dart';
import 'package:soko/features/order/domain/repositories/order_repository.dart';

/// 获取订单详情用例
class GetOrderDetailUseCase implements UseCase<Order, String> {

  GetOrderDetailUseCase(this.repository);
  final OrderRepository repository;

  @override
  Future<Either<Failure, Order>> call(String orderId) async {
    return repository.getOrderById(orderId);
  }
}

/// GetOrderDetailUseCase 提供者
final getOrderDetailUseCaseProvider = Provider<GetOrderDetailUseCase>((ref) {
  final repository = ref.read(orderRepositoryProvider);
  return GetOrderDetailUseCase(repository);
});
