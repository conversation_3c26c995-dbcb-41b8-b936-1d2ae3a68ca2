import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';

import 'package:soko/shared/presentation/widgets/custom_app_bar.dart';
import 'package:soko/shared/presentation/widgets/loading_widget.dart';
import 'package:soko/shared/presentation/widgets/error_retry_widget.dart';
import 'package:soko/features/recycle/presentation/providers/logistics_tracking_provider.dart';
import 'package:soko/features/recycle/presentation/widgets/logistics_info_card.dart';
import 'package:soko/features/recycle/presentation/widgets/logistics_timeline.dart';
import 'package:soko/features/recycle/presentation/widgets/logistics_contact_card.dart';

/// 物流跟踪页面
class LogisticsTrackingPage extends ConsumerStatefulWidget {
  const LogisticsTrackingPage({
    super.key,
    required this.orderId,
  });

  final String orderId;

  @override
  ConsumerState<LogisticsTrackingPage> createState() => _LogisticsTrackingPageState();
}

class _LogisticsTrackingPageState extends ConsumerState<LogisticsTrackingPage> {
  @override
  void initState() {
    super.initState();
    // 页面初始化时加载物流信息
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(logisticsTrackingProvider(widget.orderId).notifier).loadLogisticsInfo();
    });
  }

  @override
  Widget build(BuildContext context) {
    final logisticsState = ref.watch(logisticsTrackingProvider(widget.orderId));

    return Scaffold(
      appBar: const CustomAppBar(
        title: '物流跟踪',
        showBackButton: true,
      ),
      body: logisticsState.when(
        initial: () => const LoadingWidget(),
        loading: () => const LoadingWidget(),
        success: (logistics) => _buildLogisticsContent(logistics),
        error: (error) => ErrorRetryWidget(
          message: error,
          onRetry: () => ref.read(logisticsTrackingProvider(widget.orderId).notifier).loadLogisticsInfo(),
        ),
      ),
    );
  }

  /// 构建物流内容
  Widget _buildLogisticsContent(LogisticsInfo logistics) {
    return RefreshIndicator(
      onRefresh: () => ref.read(logisticsTrackingProvider(widget.orderId).notifier).refreshLogisticsInfo(),
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 物流基本信息卡片
            LogisticsInfoCard(
              logistics: logistics,
              onCopyTrackingNumber: () => _copyToClipboard(logistics.trackingNumber),
              onOpenOfficialApp: () => _openOfficialApp(logistics),
            ),
            SizedBox(height: 16.h),
            
            // 联系快递员卡片
            if (logistics.courierPhone != null || logistics.courierName != null)
              LogisticsContactCard(
                logistics: logistics,
                onCallCourier: () => _callCourier(logistics.courierPhone!),
              ),
            if (logistics.courierPhone != null || logistics.courierName != null)
              SizedBox(height: 16.h),
            
            // 物流时间线
            if (logistics.tracks != null && logistics.tracks!.isNotEmpty)
              LogisticsTimeline(tracks: logistics.tracks!),
            
            // 底部安全距离
            SizedBox(height: 32.h),
          ],
        ),
      ),
    );
  }

  /// 复制到剪贴板
  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('运单号已复制到剪贴板'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.r),
        ),
      ),
    );
  }

  /// 打开快递公司官方应用
  void _openOfficialApp(LogisticsInfo logistics) async {
    // 根据快递公司代码构建官方应用链接
    String? appUrl;
    
    switch (logistics.courierCompanyCode.toLowerCase()) {
      case 'sf':
        appUrl = 'sf://tracking/${logistics.trackingNumber}';
        break;
      case 'sto':
        appUrl = 'sto://tracking/${logistics.trackingNumber}';
        break;
      case 'yt':
        appUrl = 'yt://tracking/${logistics.trackingNumber}';
        break;
      case 'zto':
        appUrl = 'zto://tracking/${logistics.trackingNumber}';
        break;
      default:
        // 如果没有对应的应用，打开网页版
        appUrl = 'https://www.kuaidi100.com/query?type=${logistics.courierCompanyCode}&postid=${logistics.trackingNumber}';
        break;
    }

    if (appUrl != null) {
      try {
        final uri = Uri.parse(appUrl);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri);
        } else {
          // 如果无法打开应用，显示提示
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('无法打开${logistics.courierCompany}应用'),
                duration: const Duration(seconds: 2),
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('打开应用失败'),
              duration: const Duration(seconds: 2),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    }
  }

  /// 拨打快递员电话
  void _callCourier(String phoneNumber) async {
    final uri = Uri.parse('tel:$phoneNumber');
    try {
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('无法拨打电话'),
              duration: const Duration(seconds: 2),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('拨打电话失败'),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }
}
