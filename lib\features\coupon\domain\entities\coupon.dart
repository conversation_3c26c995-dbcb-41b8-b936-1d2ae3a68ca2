import 'package:json_annotation/json_annotation.dart';

import 'package:soko/core/enums/app_enums.dart';

part 'coupon.g.dart';

/// 优惠券实体类
@JsonSerializable()
class Coupon {

  const Coupon({
    required this.id,
    required this.name,
    this.description,
    required this.type,
    required this.value,
    this.minAmount,
    this.maxDiscount,
    required this.startTime,
    required this.endTime,
    this.totalCount,
    this.usedCount,
    this.userLimit,
    required this.status,
    required this.createTime,
    required this.updateTime,
  });

  factory Coupon.fromJson(Map<String, dynamic> json) => _$CouponFromJson(json);
  @Json<PERSON>ey(name: 'id')
  final String id;

  @Json<PERSON>ey(name: 'name')
  final String name;

  @Json<PERSON>ey(name: 'description')
  final String? description;

  @Json<PERSON>ey(name: 'type')
  final String type;

  @Json<PERSON>ey(name: 'value')
  final double value;

  @Json<PERSON>ey(name: 'minAmount')
  final double? minAmount;

  @Json<PERSON>ey(name: 'maxDiscount')
  final double? maxDiscount;

  @Json<PERSON>ey(name: 'startTime')
  final int startTime;

  @Json<PERSON><PERSON>(name: 'endTime')
  final int endTime;

  @Json<PERSON>ey(name: 'totalCount')
  final int? totalCount;

  @JsonKey(name: 'usedCount')
  final int? usedCount;

  @JsonKey(name: 'userLimit')
  final int? userLimit;

  @JsonKey(name: 'status')
  final String status;

  @JsonKey(name: 'createTime')
  final int createTime;

  @JsonKey(name: 'updateTime')
  final int updateTime;

  Map<String, dynamic> toJson() => _$CouponToJson(this);

  /// 获取优惠券类型枚举
  CouponType get typeEnum {
    switch (type) {
      case 'discount':
        return CouponType.discount;
      case 'cashback':
        return CouponType.cashback;
      case 'free_shipping':
        return CouponType.freeShipping;
      default:
        return CouponType.discount;
    }
  }

  /// 获取优惠券状态枚举
  CouponStatus get statusEnum {
    switch (status) {
      case 'available':
        return CouponStatus.available;
      case 'used':
        return CouponStatus.used;
      case 'expired':
        return CouponStatus.expired;
      default:
        return CouponStatus.expired;
    }
  }

  /// 是否可用
  bool get isAvailable {
    final now = DateTime.now().millisecondsSinceEpoch;
    return statusEnum == CouponStatus.available &&
           now >= startTime &&
           now <= endTime;
  }

  /// 是否已过期
  bool get isExpired {
    final now = DateTime.now().millisecondsSinceEpoch;
    return now > endTime || statusEnum == CouponStatus.expired;
  }

  /// 是否已用完
  bool get isSoldOut {
    if (totalCount == null) return false;
    return (usedCount ?? 0) >= totalCount!;
  }

  /// 获取优惠描述
  String get discountDescription {
    switch (typeEnum) {
      case CouponType.discount:
        final discount = (value * 100).toInt();
        return '$discount折';
      case CouponType.cashback:
        if (minAmount != null) {
          return '满${minAmount!.toStringAsFixed(0)}减${value.toStringAsFixed(0)}';
        }
        return '立减${value.toStringAsFixed(0)}元';
      case CouponType.freeShipping:
        return '免运费';
    }
  }

  /// 计算优惠金额
  double calculateDiscount(double amount) {
    if (!isAvailable) return 0;
    if (minAmount != null && amount < minAmount!) return 0;

    switch (typeEnum) {
      case CouponType.discount:
        final discount = amount * (1 - value);
        if (maxDiscount != null && discount > maxDiscount!) {
          return maxDiscount!;
        }
        return discount;
      case CouponType.cashback:
        return value;
      case CouponType.freeShipping:
        return 0; // 免运费券的优惠金额在运费计算中处理
    }
  }

  /// 是否可以应用到指定金额
  bool canApplyTo(double amount) {
    if (!isAvailable) return false;
    if (minAmount != null && amount < minAmount!) return false;
    return true;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Coupon && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Coupon(id: $id, name: $name, type: $type, value: $value)';
  }
}

/// 用户优惠券
@JsonSerializable()
class UserCoupon {

  const UserCoupon({
    required this.id,
    required this.userId,
    required this.couponId,
    this.coupon,
    required this.status,
    this.usedTime,
    this.usedOrderId,
    required this.receiveTime,
    required this.expireTime,
  });

  factory UserCoupon.fromJson(Map<String, dynamic> json) => _$UserCouponFromJson(json);
  @JsonKey(name: 'id')
  final String id;

  @JsonKey(name: 'userId')
  final String userId;

  @JsonKey(name: 'couponId')
  final String couponId;

  @JsonKey(name: 'coupon')
  final Coupon? coupon;

  @JsonKey(name: 'status')
  final String status;

  @JsonKey(name: 'usedTime')
  final int? usedTime;

  @JsonKey(name: 'usedOrderId')
  final String? usedOrderId;

  @JsonKey(name: 'receiveTime')
  final int receiveTime;

  @JsonKey(name: 'expireTime')
  final int expireTime;

  Map<String, dynamic> toJson() => _$UserCouponToJson(this);

  /// 获取优惠券状态枚举
  CouponStatus get statusEnum {
    switch (status) {
      case 'available':
        return CouponStatus.available;
      case 'used':
        return CouponStatus.used;
      case 'expired':
        return CouponStatus.expired;
      default:
        return CouponStatus.expired;
    }
  }

  /// 是否可用
  bool get isAvailable {
    final now = DateTime.now().millisecondsSinceEpoch;
    return statusEnum == CouponStatus.available && now <= expireTime;
  }

  /// 是否已过期
  bool get isExpired {
    final now = DateTime.now().millisecondsSinceEpoch;
    return now > expireTime || statusEnum == CouponStatus.expired;
  }

  /// 是否已使用
  bool get isUsed {
    return statusEnum == CouponStatus.used;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserCoupon && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'UserCoupon(id: $id, couponId: $couponId, status: $status)';
  }
}
