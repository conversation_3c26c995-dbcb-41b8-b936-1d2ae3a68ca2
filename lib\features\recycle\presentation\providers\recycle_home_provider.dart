import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:soko/core/state/base_state.dart';
import 'package:soko/features/recycle/data/datasources/recycle_api_service.dart';
import 'package:soko/features/recycle/domain/entities/recycle_order.dart';

/// 回收首页状态
class RecycleHomeState {

  const RecycleHomeState({
    this.statsState = const AsyncState.idle(),
    this.processState = const AsyncState.idle(),
    this.recentOrdersState = const AsyncState.idle(),
    this.categoriesState = const AsyncState.idle(),
  });
  final AsyncState<RecycleStats> statsState;
  final AsyncState<List<ProcessStep>> processState;
  final AsyncState<List<RecycleOrder>> recentOrdersState;
  final AsyncState<List<CategoryItem>> categoriesState;

  RecycleHomeState copyWith({
    AsyncState<RecycleStats>? statsState,
    AsyncState<List<ProcessStep>>? processState,
    AsyncState<List<RecycleOrder>>? recentOrdersState,
    AsyncState<List<CategoryItem>>? categoriesState,
  }) {
    return RecycleHomeState(
      statsState: statsState ?? this.statsState,
      processState: processState ?? this.processState,
      recentOrdersState: recentOrdersState ?? this.recentOrdersState,
      categoriesState: categoriesState ?? this.categoriesState,
    );
  }
}

/// 回收首页状态管理器
class RecycleHomeNotifier extends StateNotifier<RecycleHomeState> {

  RecycleHomeNotifier(this._apiService) : super(const RecycleHomeState());
  final RecycleApiService _apiService;

  /// 初始化首页数据
  Future<void> initializeHomeData() async {
    await Future.wait([
      loadStats(),
      loadProcess(),
      loadRecentOrders(),
      loadCategories(),
    ]);
  }

  /// 加载统计信息
  Future<void> loadStats() async {
    state = state.copyWith(statsState: const AsyncState.loading());

    try {
      final response = await _apiService.getRecycleStats();
      
      if (response.isSuccess && response.data != null) {
        state = state.copyWith(
          statsState: AsyncState.success(response.data!),
        );
      } else {
        state = state.copyWith(
          statsState: AsyncState.error(response.message ?? '加载统计信息失败'),
        );
      }
    } catch (e) {
      state = state.copyWith(
        statsState: AsyncState.error(e.toString()),
      );
    }
  }

  /// 加载流程说明
  Future<void> loadProcess() async {
    state = state.copyWith(processState: const AsyncState.loading());

    try {
      final response = await _apiService.getRecycleProcess();
      
      if (response.isSuccess && response.data != null) {
        state = state.copyWith(
          processState: AsyncState.success(response.data!),
        );
      } else {
        state = state.copyWith(
          processState: AsyncState.error(response.message ?? '加载流程说明失败'),
        );
      }
    } catch (e) {
      state = state.copyWith(
        processState: AsyncState.error(e.toString()),
      );
    }
  }

  /// 加载最近订单
  Future<void> loadRecentOrders() async {
    state = state.copyWith(recentOrdersState: const AsyncState.loading());

    try {
      final params = RecycleOrderQueryParams(
        page: 1,
        size: 5, // 只加载最近5个订单
      );
      
      final response = await _apiService.getRecycleOrderList(params: params);
      
      if (response.isSuccess && response.data != null) {
        state = state.copyWith(
          recentOrdersState: AsyncState.success(response.data!.items),
        );
      } else {
        state = state.copyWith(
          recentOrdersState: AsyncState.error(response.message ?? '加载最近订单失败'),
        );
      }
    } catch (e) {
      state = state.copyWith(
        recentOrdersState: AsyncState.error(e.toString()),
      );
    }
  }

  /// 加载分类信息
  Future<void> loadCategories() async {
    state = state.copyWith(categoriesState: const AsyncState.loading());

    try {
      final response = await _apiService.getRecycleCategories();
      
      if (response.isSuccess && response.data != null) {
        state = state.copyWith(
          categoriesState: AsyncState.success(response.data!),
        );
      } else {
        state = state.copyWith(
          categoriesState: AsyncState.error(response.message ?? '加载分类信息失败'),
        );
      }
    } catch (e) {
      state = state.copyWith(
        categoriesState: AsyncState.error(e.toString()),
      );
    }
  }

  /// 刷新首页数据
  Future<void> refreshHomeData() async {
    await initializeHomeData();
  }
}

/// 回收首页状态管理器提供者
final recycleHomeProvider = StateNotifierProvider<RecycleHomeNotifier, RecycleHomeState>((ref) {
  final apiService = RecycleApiService();
  return RecycleHomeNotifier(apiService);
});
